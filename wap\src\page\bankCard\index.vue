<template>
    <div class="bank-card-page">
        <div class="bank-card-page__header">
            <van-nav-bar :title="pageTitle" left-arrow fixed @click-left="$router.go(-1)" />
        </div>

        <div class="bank-card-page__content" v-if="!isLoading">
            <!-- 页面说明 -->
            <div class="info-card" v-if="isAddMode">
                <div class="info-card__icon">
                    <van-icon name="credit-pay" size="1.2rem" />
                </div>
                <div class="info-card__content">
                    <h3 class="info-card__title">绑定银行卡</h3>
                    <p class="info-card__description">为了您的资金安全，请绑定本人实名银行卡</p>
                </div>
            </div>

            <!-- 银行卡展示卡片（非添加模式） -->
            <div class="bank-card" v-if="!isAddMode">
                <div class="bank-card__background">
                    <div class="bank-card__header">
                        <div class="bank-card__bank-name">{{ formData.bankName }}</div>
                        <div class="bank-card__type">储蓄卡</div>
                    </div>
                    <div class="bank-card__number">{{ formattedBankNo }}</div>
                    <div class="bank-card__branch">{{ formData.bankAddress }}</div>
                </div>
            </div>

            <!-- 表单区域 -->
            <div class="form-section">
                <div class="form-item">
                    <label class="form-item__label">
                        <van-icon name="shop-o" class="form-item__icon" />
                        银行名称
                    </label>
                    <div class="form-item__input">
                        <input type="text" :disabled="!isAddMode" v-model="formData.bankName" placeholder="请输入银行名称"
                            class="input-field" :class="{
                                'input-field--disabled': !isAddMode,
                                'input-field--error': !formData.bankName.trim() && isSubmitting
                            }" />
                    </div>
                </div>

                <div class="form-item">
                    <label class="form-item__label">
                        <van-icon name="location-o" class="form-item__icon" />
                        开户支行
                    </label>
                    <div class="form-item__input">
                        <input type="text" :disabled="!isAddMode" v-model="formData.bankAddress" placeholder="请输入开户支行"
                            class="input-field" :class="{
                                'input-field--disabled': !isAddMode,
                                'input-field--error': !formData.bankAddress.trim() && isSubmitting
                            }" />
                    </div>
                </div>

                <div class="form-item">
                    <label class="form-item__label">
                        <van-icon name="credit-pay" class="form-item__icon" />
                        银行卡号
                    </label>
                    <div class="form-item__input">
                        <input type="text" placeholder="请输入银行卡号" :disabled="!isAddMode" v-model="formData.bankNo"
                            @input="formatBankCardInput" class="input-field" :class="{
                                'input-field--disabled': !isAddMode,
                                'input-field--error': !formData.bankNo.trim() && isSubmitting
                            }" maxlength="23" />
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="action-section">
                <button class="action-button" :class="{
                    'action-button--primary': isAddMode,
                    'action-button--secondary': !isAddMode,
                    'action-button--loading': isSubmitting,
                    'action-button--disabled': isAddMode && !isFormValid
                }" @click="handleSubmit" :disabled="isSubmitting || (isAddMode && !isFormValid)">
                    <van-loading size="0.4rem" v-if="isSubmitting" />
                    <span>{{ buttonText }}</span>
                </button>
            </div>

            <!-- 安全提示 -->
            <div class="security-tips" v-if="isAddMode">
                <div class="security-tips__header">
                    <van-icon name="shield-o" size="0.4rem" />
                    <span>安全提示</span>
                </div>
                <ul class="security-tips__list">
                    <li class="security-tips__item">请确保银行卡为本人实名认证</li>
                    <li class="security-tips__item">银行卡必须支持在线支付功能</li>
                    <li class="security-tips__item">请仔细核对银行卡号，避免输入错误</li>
                </ul>
            </div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-overlay" v-if="isLoading">
            <van-loading size="1rem" text-size="0.28rem">加载中...</van-loading>
        </div>
    </div>
</template>

<script>
import * as api from '@/axios/api'
import { Toast } from 'mint-ui'
import { isNull, bankNoReg, isName } from '@/utils/utils'

export default {
    name: 'BankCardPage',
    data() {
        return {
            formData: {
                bankName: '', // 银行名称
                bankAddress: '', // 开户支行
                bankNo: '', // 银行卡号
            },
            isAddMode: false, // 是否为添加模式
            isLoading: false, // 页面加载状态
            isSubmitting: false, // 提交状态
        }
    },
    computed: {
        // 页面标题
        pageTitle() {
            return this.isAddMode ? '绑定银行卡' : '银行卡信息';
        },

        // 按钮文字
        buttonText() {
            if (this.isSubmitting) {
                return this.isAddMode ? '提交中...' : '处理中...';
            }
            return this.isAddMode ? '提交' : '更换';
        },

        // 表单是否有效
        isFormValid() {
            const { bankName, bankAddress, bankNo } = this.formData;
            return bankName.trim() && bankAddress.trim() && bankNo.trim();
        },

        // 格式化银行卡号显示
        formattedBankNo() {
            if (!this.formData.bankNo) return '';
            // 隐藏中间数字，只显示前4位和后4位
            const bankNo = this.formData.bankNo;
            if (bankNo.length <= 8) return bankNo;
            return `${bankNo.substring(0, 4)} **** **** ${bankNo.substring(bankNo.length - 4)}`;
        },

        // 兼容旧属性名
        addBank() {
            return this.isAddMode;
        },
        bankName: {
            get() {
                return this.formData.bankName;
            },
            set(value) {
                this.formData.bankName = value;
            }
        },
        bankAddress: {
            get() {
                return this.formData.bankAddress;
            },
            set(value) {
                this.formData.bankAddress = value;
            }
        },
        bankNo: {
            get() {
                return this.formData.bankNo;
            },
            set(value) {
                this.formData.bankNo = value;
            }
        }
    },
    async mounted() {
        await this.initializePage();
    },
    methods: {
        // 初始化页面
        async initializePage() {
            try {
                this.isLoading = true;
                await this.loadBankCardInfo();
            } catch (error) {
                console.error('页面初始化失败:', error);
                this.handleError(error, '页面加载失败');
            } finally {
                this.isLoading = false;
            }
        },

        // 加载银行卡信息
        async loadBankCardInfo() {
            try {
                const response = await api.getBankCard();
                this.handleBankCardResponse(response);
            } catch (error) {
                console.error('获取银行卡信息失败:', error);
                // 如果获取失败，默认为添加模式
                this.isAddMode = true;
                throw error;
            }
        },

        // 处理银行卡信息响应
        handleBankCardResponse(response) {
            if (!response) {
                throw new Error('服务器无响应');
            }

            if (response.status === 0) {
                // 已有银行卡信息
                const { bankAddress, bankName, bankNo } = response.data || {};
                this.formData = {
                    bankAddress: bankAddress || '',
                    bankName: bankName || '',
                    bankNo: bankNo || '',
                };
                this.isAddMode = false;
            } else {
                // 没有银行卡信息，进入添加模式
                this.isAddMode = true;
            }
        },

        // 处理表单提交
        async handleSubmit() {
            if (this.isSubmitting) {
                return;
            }

            if (this.isAddMode) {
                await this.submitBankCard();
            } else {
                this.navigateToUpdate();
            }
        },

        // 提交银行卡信息
        async submitBankCard() {
            try {
                // 表单验证
                if (!this.validateForm()) {
                    return;
                }

                this.isSubmitting = true;

                const cleanedData = this.getCleanedFormData();
                const response = await api.addBankCard(cleanedData);

                this.handleSubmitResponse(response);

            } catch (error) {
                this.handleError(error, '提交失败');
            } finally {
                this.isSubmitting = false;
            }
        },

        // 表单验证
        validateForm() {
            const cleanedData = this.getCleanedFormData();

            // 验证银行卡号
            if (isNull(cleanedData.bankNo) || !bankNoReg(cleanedData.bankNo)) {
                Toast(this.$t('hj217') || '请输入正确的银行卡号');
                return false;
            }

            // 验证银行名称
            if (isNull(cleanedData.bankName) || !isName(cleanedData.bankName)) {
                Toast(this.$t('hj218') || '请输入正确的银行名称');
                return false;
            }

            // 验证开户支行
            if (isNull(cleanedData.bankAddress) || !isName(cleanedData.bankAddress)) {
                Toast(this.$t('hj219') || '请输入正确的开户支行');
                return false;
            }

            return true;
        },

        // 获取清理后的表单数据
        getCleanedFormData() {
            return {
                bankNo: String(this.formData.bankNo).replace(/\s+/g, ''),
                bankName: String(this.formData.bankName).replace(/\s+/g, ''),
                bankAddress: String(this.formData.bankAddress).replace(/\s+/g, ''),
            };
        },

        // 处理提交响应
        handleSubmitResponse(response) {
            if (!response) {
                throw new Error('服务器无响应');
            }

            if (response.status === 0) {
                Toast(this.$t('hj220') || '银行卡绑定成功');
                // 延迟跳转，让用户看到成功提示
                setTimeout(() => {
                    this.navigateToUserPage();
                }, 1500);
            } else {
                Toast(response.msg || '绑定失败，请重试');
            }
        },

        // 跳转到用户页面
        navigateToUserPage() {
            try {
                this.$router.push('/newUser');
            } catch (error) {
                console.error('页面跳转失败:', error);
                this.$router.go(-1);
            }
        },

        // 跳转到更新页面
        navigateToUpdate() {
            try {
                this.$router.push('/bankUpDate');
            } catch (error) {
                console.error('跳转更新页面失败:', error);
                Toast('页面跳转失败');
            }
        },

        // 统一错误处理
        handleError(error, defaultMessage = '操作失败') {
            console.error('错误详情:', error);

            if (error.response) {
                switch (error.response.status) {
                    case 401:
                        Toast('登录已过期，请重新登录');
                        break;
                    case 403:
                        Toast('没有操作权限');
                        break;
                    case 500:
                        Toast('服务器异常，请稍后重试');
                        break;
                    default:
                        Toast((error.response.data && error.response.data.msg) || '网络请求失败');
                }
            } else {
                Toast(error.message || defaultMessage);
            }
        },

        // 输入框格式化（银行卡号）
        formatBankCardInput() {
            // 自动格式化银行卡号输入（每4位加空格）
            let value = this.formData.bankNo.replace(/\s/g, '');
            let formatted = value.replace(/(\d{4})(?=\d)/g, '$1 ');
            this.formData.bankNo = formatted;
        },

        // 兼容旧方法名
        async toSure() {
            await this.handleSubmit();
        },

        async getCardDetail() {
            await this.loadBankCardInfo();
        }
    }
}
</script>

<style lang="less" scoped>
.bank-card-page {
    font-size: 0.3256rem;
    padding: 0;
    background: linear-gradient(180deg, #F6F6F6 0%, #FAFAFA 50%, #F0F0F0 100%);
    min-height: 100vh;
    box-sizing: border-box;
    overflow-x: hidden;

    &__header {
        width: 100%;
        height: 1.07rem;
        z-index: 999;
        position: relative;
    }

    &__content {
        margin-top: 0.2326rem;
        padding-bottom: 0.4651rem;
        min-height: calc(100vh - 1.3023rem);
        box-sizing: border-box;
        width: 100%;
        overflow-x: hidden;
    }
}

// 信息卡片样式
.info-card {
    margin: 0.3488rem;
    padding: 0.4651rem;
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border-radius: 0.2326rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: center;
    animation: slideInUp 0.3s ease-out;

    &__icon {
        margin-right: 0.3488rem;
        color: #EA001B;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 1.3953rem;
        height: 1.3953rem;
        background: linear-gradient(135deg, rgba(234, 0, 27, 0.1), rgba(255, 107, 61, 0.1));
        border-radius: 50%;
    }

    &__content {
        flex: 1;

        .info-card__title {
            font-size: 0.4186rem;
            font-weight: 600;
            color: #2c3e50;
            margin: 0 0 0.1162rem 0;
            line-height: 1.2;
        }

        .info-card__description {
            font-size: 0.3023rem;
            color: #7f8c8d;
            margin: 0;
            line-height: 1.4;
        }
    }
}

// 银行卡展示样式
.bank-card {
    margin: 0.3488rem;
    animation: slideInUp 0.3s ease-out 0.1s both;

    &__background {
        background: linear-gradient(135deg, #EA001B 0%, #FF6B3D 100%);
        padding: 0.4651rem;
        border-radius: 0.3488rem;
        color: #fff;
        position: relative;
        overflow: hidden;
        box-shadow: 0 8px 24px rgba(234, 0, 27, 0.3);

        &::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            transform: rotate(45deg);
        }
    }

    &__header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.6977rem;
        position: relative;
        z-index: 2;
    }

    &__bank-name {
        font-size: 0.4651rem;
        font-weight: 700;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    &__type {
        font-size: 0.3023rem;
        opacity: 0.8;
        background: rgba(255, 255, 255, 0.2);
        padding: 0.1162rem 0.2326rem;
        border-radius: 0.1162rem;
    }

    &__number {
        font-size: 0.4186rem;
        font-weight: 600;
        letter-spacing: 0.0697rem;
        margin-bottom: 0.4651rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        position: relative;
        z-index: 2;
    }

    &__branch {
        font-size: 0.3023rem;
        opacity: 0.9;
        position: relative;
        z-index: 2;
    }
}

// 表单区域样式
.form-section {
    padding: 0 0.3488rem;
    box-sizing: border-box;

    .form-item {
        margin-bottom: 0.4651rem;
        animation: slideInUp 0.3s ease-out;

        &:nth-child(2) {
            animation-delay: 0.1s;
        }

        &:nth-child(3) {
            animation-delay: 0.2s;
        }

        &__label {
            display: flex;
            align-items: center;
            font-size: 0.3721rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.2326rem;
        }

        &__icon {
            margin-right: 0.1860rem;
            color: #EA001B;
        }

        &__input {
            position: relative;
            width: 100%;
            box-sizing: border-box;

            .input-field {
                width: 100%;
                height: 1.1627rem;
                padding: 0 0.3488rem;
                background: #fff;
                border: 2px solid #e1e8ed;
                border-radius: 0.2326rem;
                font-size: 0.3488rem;
                color: #2c3e50;
                transition: all 0.3s ease;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
                box-sizing: border-box;
                outline: none;

                &:focus {
                    outline: none;
                    border-color: #EA001B;
                    box-shadow: 0 4px 16px rgba(234, 0, 27, 0.2);
                    transform: translateY(-1px);
                }

                &::placeholder {
                    color: #bdc3c7;
                    font-size: 0.3256rem;
                }

                &--disabled {
                    background: #f8f9fa;
                    color: #7f8c8d;
                    cursor: not-allowed;
                    border-color: #e9ecef;

                    &:focus {
                        border-color: #e9ecef;
                        box-shadow: none;
                        transform: none;
                    }
                }

                &--error {
                    border-color: #e74c3c;
                    background: rgba(231, 76, 60, 0.05);

                    &:focus {
                        border-color: #e74c3c;
                        box-shadow: 0 4px 16px rgba(231, 76, 60, 0.2);
                    }
                }
            }
        }
    }
}

// 操作按钮样式
.action-section {
    padding: 0.4651rem 0.3488rem 0;

    .action-button {
        width: 100%;
        height: 1.2791rem;
        border: none;
        border-radius: 0.2326rem;
        font-size: 0.3721rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.1860rem;
        position: relative;
        overflow: hidden;
        animation: slideInUp 0.3s ease-out 0.3s both;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        &:hover::before {
            left: 100%;
        }

        &--primary {
            background: linear-gradient(135deg, #EA001B, #FF6B3D);
            color: #fff;
            box-shadow: 0 4px 12px rgba(234, 0, 27, 0.3);

            &:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(234, 0, 27, 0.4);
            }

            &:active {
                transform: translateY(0);
            }
        }

        &--secondary {
            background: linear-gradient(135deg, #d73a49, #EA001B);
            color: #fff;
            box-shadow: 0 4px 12px rgba(215, 58, 73, 0.3);

            &:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(215, 58, 73, 0.4);
            }

            &:active {
                transform: translateY(0);
            }
        }

        &--disabled {
            background: linear-gradient(135deg, #bdc3c7, #95a5a6);
            cursor: not-allowed;
            transform: none !important;
            box-shadow: 0 2px 8px rgba(189, 195, 199, 0.3);

            &:hover {
                transform: none;
                box-shadow: 0 2px 8px rgba(189, 195, 199, 0.3);
            }

            &::before {
                display: none;
            }
        }

        &--loading {
            pointer-events: none;

            &::before {
                display: none;
            }
        }
    }
}

// 安全提示样式
.security-tips {
    margin: 0.4651rem 0.3488rem 0;
    padding: 0.3488rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 0.2326rem;
    border-left: 4px solid #f39c12;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    animation: slideInUp 0.3s ease-out 0.4s both;

    &__header {
        display: flex;
        align-items: center;
        margin-bottom: 0.2326rem;
        color: #f39c12;
        font-weight: 600;
        font-size: 0.3488rem;

        .van-icon {
            margin-right: 0.1860rem;
        }
    }

    &__list {
        list-style: none;
        padding: 0;
        margin: 0;

        .security-tips__item {
            font-size: 0.3023rem;
            color: #7f8c8d;
            line-height: 1.6;
            margin-bottom: 0.0930rem;
            position: relative;
            padding-left: 0.3488rem;

            &:last-child {
                margin-bottom: 0;
            }

            &::before {
                content: '•';
                position: absolute;
                left: 0;
                color: #f39c12;
                font-weight: bold;
            }
        }
    }
}

// 加载遮罩样式
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(5px);

    .van-loading {
        color: #EA001B;
    }
}

// 动画定义
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(0.4651rem);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.7;
    }
}

// 响应式设计
@media (max-width: 375px) {
    .bank-card-page {
        &__content {
            margin-top: 0.1162rem;
        }
    }

    .info-card {
        margin: 0.2326rem;
        padding: 0.3488rem;

        &__icon {
            width: 1.1627rem;
            height: 1.1627rem;
            margin-right: 0.2326rem;
        }

        &__content {
            .info-card__title {
                font-size: 0.3721rem;
            }

            .info-card__description {
                font-size: 0.2791rem;
            }
        }
    }

    .bank-card {
        margin: 0.2326rem;

        &__background {
            padding: 0.3488rem;
        }

        &__bank-name {
            font-size: 0.4186rem;
        }

        &__number {
            font-size: 0.3721rem;
        }
    }

    .form-section {
        padding: 0 0.2326rem;

        .form-item {
            &__label {
                font-size: 0.3488rem;
            }

            &__input {
                .input-field {
                    height: 1.0465rem;
                    font-size: 0.3256rem;
                    padding: 0 0.2791rem;
                }
            }
        }
    }

    .action-section {
        padding: 0.3488rem 0.2326rem 0;

        .action-button {
            height: 1.1627rem;
            font-size: 0.3488rem;
        }
    }

    .security-tips {
        margin: 0.3488rem 0.2326rem 0;
        padding: 0.2791rem;

        &__header {
            font-size: 0.3256rem;
        }

        &__list {
            .security-tips__item {
                font-size: 0.2791rem;
                padding-left: 0.2791rem;
            }
        }
    }
}

// 暗色模式支持
@media (prefers-color-scheme: dark) {
    .bank-card-page {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);

        &__content {
            color: #ecf0f1;
        }
    }

    .info-card {
        background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
        color: #ecf0f1;

        &__content {
            .info-card__title {
                color: #ecf0f1;
            }

            .info-card__description {
                color: #bdc3c7;
            }
        }
    }

    .form-section {
        .form-item {
            &__label {
                color: #ecf0f1;
            }

            &__input {
                .input-field {
                    background: #34495e;
                    border-color: #4a5f7a;
                    color: #ecf0f1;

                    &::placeholder {
                        color: #95a5a6;
                    }

                    &--disabled {
                        background: #2c3e50;
                        color: #7f8c8d;
                    }
                }
            }
        }
    }

    .security-tips {
        background: rgba(52, 73, 94, 0.8);
        color: #ecf0f1;

        &__list {
            .security-tips__item {
                color: #bdc3c7;
            }
        }
    }
}

// 触摸设备优化
@media (pointer: coarse) {
    .action-button {
        height: 1.3953rem;
        font-size: 0.3953rem;
    }

    .form-section {
        .form-item {
            &__input {
                .input-field {
                    height: 1.2791rem;
                    font-size: 0.3721rem;
                }
            }
        }
    }
}

// 滚动条美化
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: rgba(241, 241, 241, 0.3);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #EA001B, #FF6B3D);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #d00019, #e55a37);
}

// 焦点可见性
.input-field:focus-visible,
.action-button:focus-visible {
    outline: 2px solid #EA001B;
    outline-offset: 2px;
}
</style>
