<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    <title>Webpack Bundle Analyzer</title>

    
  <!-- viewer.js -->
  <script>
    (function(e){function t(r){if(n[r])return n[r].exports;var o=n[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,t),o.l=!0,o.exports}var n={};t.m=e,t.c=n,t.d=function(e,n,r){t.o(e,n)||Object.defineProperty(e,n,{configurable:!1,enumerable:!0,get:r})},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="/",t(t.s=7)})([function(e,t,n){"use strict";function r(){}function o(e,t){var n,o,i,a,u=A;for(a=arguments.length;a-- >2;)O.push(arguments[a]);for(t&&null!=t.children&&(O.length||O.push(t.children),delete t.children);O.length;)if((o=O.pop())&&void 0!==o.pop)for(a=o.length;a--;)O.push(o[a]);else"boolean"==typeof o&&(o=null),(i="function"!=typeof e)&&(null==o?o="":"number"==typeof o?o=String(o):"string"!=typeof o&&(i=!1)),i&&n?u[u.length-1]+=o:u===A?u=[o]:u.push(o),n=i;var l=new r;return l.nodeName=e,l.children=u,l.attributes=null==t?void 0:t,l.key=null==t?void 0:t.key,void 0!==F.vnode&&F.vnode(l),l}function i(e,t){for(var n in t)e[n]=t[n];return e}function a(e,t){return o(e.nodeName,i(i({},e.attributes),t),arguments.length>2?[].slice.call(arguments,2):e.children)}function u(e){!e._dirty&&(e._dirty=!0)&&1==I.push(e)&&(F.debounceRendering||G)(l)}function l(){var e,t=I;for(I=[];e=t.pop();)e._dirty&&L(e)}function c(e,t,n){return"string"==typeof t||"number"==typeof t?void 0!==e.splitText:"string"==typeof t.nodeName?!e._componentConstructor&&s(e,t.nodeName):n||e._componentConstructor===t.nodeName}function s(e,t){return e.normalizedNodeName===t||e.nodeName.toLowerCase()===t.toLowerCase()}function f(e){var t=i({},e.attributes);t.children=e.children;var n=e.nodeName.defaultProps;if(void 0!==n)for(var r in n)void 0===t[r]&&(t[r]=n[r]);return t}function h(e,t){var n=t?document.createElementNS("http://www.w3.org/2000/svg",e):document.createElement(e);return n.normalizedNodeName=e,n}function p(e){var t=e.parentNode;t&&t.removeChild(e)}function d(e,t,n,r,o){if("className"===t&&(t="class"),"key"===t);else if("ref"===t)n&&n(null),r&&r(e);else if("class"!==t||o)if("style"===t){if(r&&"string"!=typeof r&&"string"!=typeof n||(e.style.cssText=r||""),r&&"object"==typeof r){if("string"!=typeof n)for(var i in n)i in r||(e.style[i]="");for(var i in r)e.style[i]="number"==typeof r[i]&&!1===E.test(i)?r[i]+"px":r[i]}}else if("dangerouslySetInnerHTML"===t)r&&(e.innerHTML=r.__html||"");else if("o"==t[0]&&"n"==t[1]){var a=t!==(t=t.replace(/Capture$/,""));t=t.toLowerCase().substring(2),r?n||e.addEventListener(t,v,a):e.removeEventListener(t,v,a),(e._listeners||(e._listeners={}))[t]=r}else if("list"!==t&&"type"!==t&&!o&&t in e)b(e,t,null==r?"":r),null!=r&&!1!==r||e.removeAttribute(t);else{var u=o&&t!==(t=t.replace(/^xlink\:?/,""));null==r||!1===r?u?e.removeAttributeNS("http://www.w3.org/1999/xlink",t.toLowerCase()):e.removeAttribute(t):"function"!=typeof r&&(u?e.setAttributeNS("http://www.w3.org/1999/xlink",t.toLowerCase(),r):e.setAttribute(t,r))}else e.className=r||""}function b(e,t,n){try{e[t]=n}catch(e){}}function v(e){return this._listeners[e.type](F.event&&F.event(e)||e)}function g(){for(var e;e=B.pop();)F.afterMount&&F.afterMount(e),e.componentDidMount&&e.componentDidMount()}function y(e,t,n,r,o,i){R++||(H=null!=o&&void 0!==o.ownerSVGElement,N=null!=e&&!("__preactattr_"in e));var a=m(e,t,n,r,i);return o&&a.parentNode!==o&&o.appendChild(a),--R||(N=!1,i||g()),a}function m(e,t,n,r,o){var i=e,a=H;if(null!=t&&"boolean"!=typeof t||(t=""),"string"==typeof t||"number"==typeof t)return e&&void 0!==e.splitText&&e.parentNode&&(!e._component||o)?e.nodeValue!=t&&(e.nodeValue=t):(i=document.createTextNode(t),e&&(e.parentNode&&e.parentNode.replaceChild(i,e),w(e,!0))),i.__preactattr_=!0,i;var u=t.nodeName;if("function"==typeof u)return _(e,t,n,r);if(H="svg"===u||"foreignObject"!==u&&H,u=String(u),(!e||!s(e,u))&&(i=h(u,H),e)){for(;e.firstChild;)i.appendChild(e.firstChild);e.parentNode&&e.parentNode.replaceChild(i,e),w(e,!0)}var l=i.firstChild,c=i.__preactattr_,f=t.children;if(null==c){c=i.__preactattr_={};for(var p=i.attributes,d=p.length;d--;)c[p[d].name]=p[d].value}return!N&&f&&1===f.length&&"string"==typeof f[0]&&null!=l&&void 0!==l.splitText&&null==l.nextSibling?l.nodeValue!=f[0]&&(l.nodeValue=f[0]):(f&&f.length||null!=l)&&x(i,f,n,r,N||null!=c.dangerouslySetInnerHTML),T(i,t.attributes,c),H=a,i}function x(e,t,n,r,o){var i,a,u,l,s,f=e.childNodes,h=[],d={},b=0,v=0,g=f.length,y=0,x=t?t.length:0;if(0!==g)for(var C=0;C<g;C++){var T=f[C],S=T.__preactattr_,z=x&&S?T._component?T._component.__key:S.key:null;null!=z?(b++,d[z]=T):(S||(void 0!==T.splitText?!o||T.nodeValue.trim():o))&&(h[y++]=T)}if(0!==x)for(var C=0;C<x;C++){l=t[C],s=null;var z=l.key;if(null!=z)b&&void 0!==d[z]&&(s=d[z],d[z]=void 0,b--);else if(!s&&v<y)for(i=v;i<y;i++)if(void 0!==h[i]&&c(a=h[i],l,o)){s=a,h[i]=void 0,i===y-1&&y--,i===v&&v++;break}s=m(s,l,n,r),u=f[C],s&&s!==e&&s!==u&&(null==u?e.appendChild(s):s===u.nextSibling?p(u):e.insertBefore(s,u))}if(b)for(var C in d)void 0!==d[C]&&w(d[C],!1);for(;v<=y;)void 0!==(s=h[y--])&&w(s,!1)}function w(e,t){var n=e._component;n?j(n):(null!=e.__preactattr_&&e.__preactattr_.ref&&e.__preactattr_.ref(null),!1!==t&&null!=e.__preactattr_||p(e),C(e))}function C(e){for(e=e.lastChild;e;){var t=e.previousSibling;w(e,!0),e=t}}function T(e,t,n){var r;for(r in n)t&&null!=t[r]||null==n[r]||d(e,r,n[r],n[r]=void 0,H);for(r in t)"children"===r||"innerHTML"===r||r in n&&t[r]===("value"===r||"checked"===r?e[r]:n[r])||d(e,r,n[r],n[r]=t[r],H)}function S(e){var t=e.constructor.name;(W[t]||(W[t]=[])).push(e)}function z(e,t,n){var r,o=W[e.name];if(e.prototype&&e.prototype.render?(r=new e(t,n),P.call(r,t,n)):(r=new P(t,n),r.constructor=e,r.render=k),o)for(var i=o.length;i--;)if(o[i].constructor===e){r.nextBase=o[i].nextBase,o.splice(i,1);break}return r}function k(e,t,n){return this.constructor(e,n)}function M(e,t,n,r,o){e._disable||(e._disable=!0,(e.__ref=t.ref)&&delete t.ref,(e.__key=t.key)&&delete t.key,!e.base||o?e.componentWillMount&&e.componentWillMount():e.componentWillReceiveProps&&e.componentWillReceiveProps(t,r),r&&r!==e.context&&(e.prevContext||(e.prevContext=e.context),e.context=r),e.prevProps||(e.prevProps=e.props),e.props=t,e._disable=!1,0!==n&&(1!==n&&!1===F.syncComponentUpdates&&e.base?u(e):L(e,1,o)),e.__ref&&e.__ref(e))}function L(e,t,n,r){if(!e._disable){var o,a,u,l=e.props,c=e.state,s=e.context,h=e.prevProps||l,p=e.prevState||c,d=e.prevContext||s,b=e.base,v=e.nextBase,m=b||v,x=e._component,C=!1;if(b&&(e.props=h,e.state=p,e.context=d,2!==t&&e.shouldComponentUpdate&&!1===e.shouldComponentUpdate(l,c,s)?C=!0:e.componentWillUpdate&&e.componentWillUpdate(l,c,s),e.props=l,e.state=c,e.context=s),e.prevProps=e.prevState=e.prevContext=e.nextBase=null,e._dirty=!1,!C){o=e.render(l,c,s),e.getChildContext&&(s=i(i({},s),e.getChildContext()));var T,S,k=o&&o.nodeName;if("function"==typeof k){var _=f(o);a=x,a&&a.constructor===k&&_.key==a.__key?M(a,_,1,s,!1):(T=a,e._component=a=z(k,_,s),a.nextBase=a.nextBase||v,a._parentComponent=e,M(a,_,0,s,!1),L(a,1,n,!0)),S=a.base}else u=m,T=x,T&&(u=e._component=null),(m||1===t)&&(u&&(u._component=null),S=y(u,o,s,n||!b,m&&m.parentNode,!0));if(m&&S!==m&&a!==x){var P=m.parentNode;P&&S!==P&&(P.replaceChild(S,m),T||(m._component=null,w(m,!1)))}if(T&&j(T),e.base=S,S&&!r){for(var D=e,O=e;O=O._parentComponent;)(D=O).base=S;S._component=D,S._componentConstructor=D.constructor}}if(!b||n?B.unshift(e):C||(e.componentDidUpdate&&e.componentDidUpdate(h,p,d),F.afterUpdate&&F.afterUpdate(e)),null!=e._renderCallbacks)for(;e._renderCallbacks.length;)e._renderCallbacks.pop().call(e);R||r||g()}}function _(e,t,n,r){for(var o=e&&e._component,i=o,a=e,u=o&&e._componentConstructor===t.nodeName,l=u,c=f(t);o&&!l&&(o=o._parentComponent);)l=o.constructor===t.nodeName;return o&&l&&(!r||o._component)?(M(o,c,3,n,r),e=o.base):(i&&!u&&(j(i),e=a=null),o=z(t.nodeName,c,n),e&&!o.nextBase&&(o.nextBase=e,a=null),M(o,c,1,n,r),e=o.base,a&&e!==a&&(a._component=null,w(a,!1))),e}function j(e){F.beforeUnmount&&F.beforeUnmount(e);var t=e.base;e._disable=!0,e.componentWillUnmount&&e.componentWillUnmount(),e.base=null;var n=e._component;n?j(n):t&&(t.__preactattr_&&t.__preactattr_.ref&&t.__preactattr_.ref(null),e.nextBase=t,p(t),S(e),C(t)),e.__ref&&e.__ref(null)}function P(e,t){this._dirty=!0,this.context=t,this.props=e,this.state=this.state||{}}function D(e,t,n){return y(n,e,{},!1,t,!1)}Object.defineProperty(t,"__esModule",{value:!0}),n.d(t,"h",function(){return o}),n.d(t,"createElement",function(){return o}),n.d(t,"cloneElement",function(){return a}),n.d(t,"Component",function(){return P}),n.d(t,"render",function(){return D}),n.d(t,"rerender",function(){return l}),n.d(t,"options",function(){return F});var F={},O=[],A=[],G="function"==typeof Promise?Promise.resolve().then.bind(Promise.resolve()):setTimeout,E=/acit|ex(?:s|g|n|p|$)|rph|ows|mnc|ntw|ine[ch]|zoo|^ord/i,I=[],B=[],R=0,H=!1,N=!1,W={};i(P.prototype,{setState:function(e,t){var n=this.state;this.prevState||(this.prevState=i({},n)),i(n,"function"==typeof e?e(n,this.props):e),t&&(this._renderCallbacks=this._renderCallbacks||[]).push(t),u(this)},forceUpdate:function(e){e&&(this._renderCallbacks=this._renderCallbacks||[]).push(e),L(this,2)},render:function(){}});var U={h:o,createElement:o,cloneElement:a,Component:P,render:D,rerender:l,options:F};t.default=U},function(e,t){function n(e,t){var n=e[1]||"",o=e[3];if(!o)return n;if(t&&"function"==typeof btoa){var i=r(o);return[n].concat(o.sources.map(function(e){return"/*# sourceURL="+o.sourceRoot+e+" */"})).concat([i]).join("\n")}return[n].join("\n")}function r(e){return"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(e))))+" */"}e.exports=function(e){var t=[];return t.toString=function(){return this.map(function(t){var r=n(t,e);return t[2]?"@media "+t[2]+"{"+r+"}":r}).join("")},t.i=function(e,n){"string"==typeof e&&(e=[[null,e,""]]);for(var r={},o=0;o<this.length;o++){var i=this[o][0];"number"==typeof i&&(r[i]=!0)}for(o=0;o<e.length;o++){var a=e[o];"number"==typeof a[0]&&r[a[0]]||(n&&!a[2]?a[2]=n:n&&(a[2]="("+a[2]+") and ("+n+")"),t.push(a))}},t}},function(e,t,n){function r(e,t){for(var n=0;n<e.length;n++){var r=e[n],o=d[r.id];if(o){o.refs++;for(var i=0;i<o.parts.length;i++)o.parts[i](r.parts[i]);for(;i<r.parts.length;i++)o.parts.push(s(r.parts[i],t))}else{for(var a=[],i=0;i<r.parts.length;i++)a.push(s(r.parts[i],t));d[r.id]={id:r.id,refs:1,parts:a}}}}function o(e,t){for(var n=[],r={},o=0;o<e.length;o++){var i=e[o],a=t.base?i[0]+t.base:i[0],u=i[1],l=i[2],c=i[3],s={css:u,media:l,sourceMap:c};r[a]?r[a].parts.push(s):n.push(r[a]={id:a,parts:[s]})}return n}function i(e,t){var n=g(e.insertInto);if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insertInto' parameter is invalid.");var r=x[x.length-1];if("top"===e.insertAt)r?r.nextSibling?n.insertBefore(t,r.nextSibling):n.appendChild(t):n.insertBefore(t,n.firstChild),x.push(t);else if("bottom"===e.insertAt)n.appendChild(t);else{if("object"!=typeof e.insertAt||!e.insertAt.before)throw new Error("[Style Loader]\n\n Invalid value for parameter 'insertAt' ('options.insertAt') found.\n Must be 'top', 'bottom', or Object.\n (https://github.com/webpack-contrib/style-loader#insertat)\n");var o=g(e.insertInto+" "+e.insertAt.before);n.insertBefore(t,o)}}function a(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e);var t=x.indexOf(e);t>=0&&x.splice(t,1)}function u(e){var t=document.createElement("style");return e.attrs.type="text/css",c(t,e.attrs),i(e,t),t}function l(e){var t=document.createElement("link");return e.attrs.type="text/css",e.attrs.rel="stylesheet",c(t,e.attrs),i(e,t),t}function c(e,t){Object.keys(t).forEach(function(n){e.setAttribute(n,t[n])})}function s(e,t){var n,r,o,i;if(t.transform&&e.css){if(!(i=t.transform(e.css)))return function(){};e.css=i}if(t.singleton){var c=m++;n=y||(y=u(t)),r=f.bind(null,n,c,!1),o=f.bind(null,n,c,!0)}else e.sourceMap&&"function"==typeof URL&&"function"==typeof URL.createObjectURL&&"function"==typeof URL.revokeObjectURL&&"function"==typeof Blob&&"function"==typeof btoa?(n=l(t),r=p.bind(null,n,t),o=function(){a(n),n.href&&URL.revokeObjectURL(n.href)}):(n=u(t),r=h.bind(null,n),o=function(){a(n)});return r(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;r(e=t)}else o()}}function f(e,t,n,r){var o=n?"":r.css;if(e.styleSheet)e.styleSheet.cssText=C(t,o);else{var i=document.createTextNode(o),a=e.childNodes;a[t]&&e.removeChild(a[t]),a.length?e.insertBefore(i,a[t]):e.appendChild(i)}}function h(e,t){var n=t.css,r=t.media;if(r&&e.setAttribute("media",r),e.styleSheet)e.styleSheet.cssText=n;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(n))}}function p(e,t,n){var r=n.css,o=n.sourceMap,i=void 0===t.convertToAbsoluteUrls&&o;(t.convertToAbsoluteUrls||i)&&(r=w(r)),o&&(r+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(o))))+" */");var a=new Blob([r],{type:"text/css"}),u=e.href;e.href=URL.createObjectURL(a),u&&URL.revokeObjectURL(u)}var d={},b=function(e){var t;return function(){return void 0===t&&(t=e.apply(this,arguments)),t}}(function(){return window&&document&&document.all&&!window.atob}),v=function(e){return document.querySelector(e)},g=function(e){var t={};return function(e){if("function"==typeof e)return e();if(void 0===t[e]){var n=v.call(this,e);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(e){n=null}t[e]=n}return t[e]}}(),y=null,m=0,x=[],w=n(17);e.exports=function(e,t){if("undefined"!=typeof DEBUG&&DEBUG&&"object"!=typeof document)throw new Error("The style-loader cannot be used in a non-browser environment");t=t||{},t.attrs="object"==typeof t.attrs?t.attrs:{},t.singleton||"boolean"==typeof t.singleton||(t.singleton=b()),t.insertInto||(t.insertInto="head"),t.insertAt||(t.insertAt="bottom");var n=o(e,t);return r(n,t),function(e){for(var i=[],a=0;a<n.length;a++){var u=n[a],l=d[u.id];l.refs--,i.push(l)}if(e){r(o(e,t),t)}for(var a=0;a<i.length;a++){var l=i[a];if(0===l.refs){for(var c=0;c<l.parts.length;c++)l.parts[c]();delete d[l.id]}}}};var C=function(){var e=[];return function(t,n){return e[t]=n,e.filter(Boolean).join("\n")}}()},function(e,t,n){var r,o;(function(){"use strict";function n(){for(var e=[],t=0;t<arguments.length;t++){var r=arguments[t];if(r){var o=typeof r;if("string"===o||"number"===o)e.push(r);else if(Array.isArray(r))e.push(n.apply(null,r));else if("object"===o)for(var a in r)i.call(r,a)&&r[a]&&e.push(a)}}return e.join(" ")}var i={}.hasOwnProperty;void 0!==e&&e.exports?e.exports=n:(r=[],void 0!==(o=function(){return n}.apply(t,r))&&(e.exports=o))})()},function(e,t,n){var r=n(20);"string"==typeof r&&(r=[[e.i,r,""]]);var o={hmr:!0};o.transform=void 0,o.insertInto=void 0;n(2)(r,o);r.locals&&(e.exports=r.locals)},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}function o(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}return Array.from(e)}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function u(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var l=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),c=n(0),s=n(24),f=r(s),h=n(6),p=r(h),d=Symbol("ALL_ITEM"),b=function(e){function t(e){i(this,t);var n=a(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return n.handleToggleAllCheck=function(){var e=n.isAllChecked()?[]:n.props.items;n.setState({checkedItems:e}),n.informAboutChange(e)},n.handleItemCheck=function(e){var t=void 0;t=n.isItemChecked(e)?n.state.checkedItems.filter(function(t){return t!==e}):[].concat(o(n.state.checkedItems),[e]),n.setState({checkedItems:t}),n.informAboutChange(t)},n.state={checkedItems:e.checkedItems||e.items},n}return u(t,e),l(t,[{key:"componentWillReceiveProps",value:function(e){var t=this;if(e.items!==this.props.items)if(this.isAllChecked())this.setState({checkedItems:e.items}),this.informAboutChange(e.items);else if(this.state.checkedItems.length){var n=e.items.filter(function(e){return t.state.checkedItems.find(function(t){return t.label===e.label})});this.setState({checkedItems:n}),this.informAboutChange(n)}}},{key:"render",value:function(){var e=this,t=this.props,n=t.label,r=t.items,o=t.renderLabel;return(0,c.h)("div",{className:p.default.container},(0,c.h)("div",{className:p.default.label},n,":"),(0,c.h)("div",null,(0,c.h)(f.default,{item:d,checked:this.isAllChecked(),onChange:this.handleToggleAllCheck},o),r.map(function(t){return(0,c.h)(f.default,{key:t.label,item:t,checked:e.isItemChecked(t),onChange:e.handleItemCheck},o)})))}},{key:"isItemChecked",value:function(e){return this.state.checkedItems.includes(e)}},{key:"isAllChecked",value:function(){return this.props.items.length===this.state.checkedItems.length}},{key:"informAboutChange",value:function(e){var t=this;setTimeout(function(){return t.props.onChange(e)})}}]),t}(c.Component);b.ALL_ITEM=d,t.default=b},function(e,t,n){var r=n(25);"string"==typeof r&&(r=[[e.i,r,""]]);var o={hmr:!0};o.transform=void 0,o.insertInto=void 0;n(2)(r,o);r.locals&&(e.exports=r.locals)},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}function o(e,t){s=(0,i.render)((0,i.h)(u.default,{chunks:e,defaultSizes:window.defaultSizes}),document.getElementById("app"),s)}var i=n(0),a=n(8),u=r(a),l=n(28),c=(r(l),void 0);try{c=new WebSocket("ws://"+location.host)}catch(e){console.warn("Couldn't connect to analyzer websocket server so you'll have to reload page manually to see updates in the treemap")}window.addEventListener("load",function(){o(window.chartData),c&&c.addEventListener("message",function(e){var t=JSON.parse(e.data);"chartDataUpdated"===t.event&&o(t.data)})},!1);var s=void 0},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}function o(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}return Array.from(e)}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function u(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var l=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),c=n(0),s=n(9),f=r(s),h=n(11),p=n(12),d=r(p),b=n(14),v=r(b),g=n(18),y=r(g),m=n(21),x=r(m),w=n(5),C=r(w),T=n(26),S=r(T),z=[{label:"Stat",prop:"statSize"},{label:"Parsed",prop:"parsedSize"},{label:"Gzipped",prop:"gzipSize"}],k=function(e){function t(e){i(this,t);var n=a(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return n.renderChunkItemLabel=function(e,t){var r=e===C.default.ALL_ITEM,o=r?"All":e.label,i=r?n.totalChunksSize:e[n.state.activeSizeItem.prop];return(0,c.h)("span",{className:t},o," (",(0,c.h)("strong",null,(0,f.default)(i)),")")},n.handleSizeSwitch=function(e){n.updateChunks(n.chunks,{activeSizeItem:e})},n.handleVisibleChunksChange=function(e){n.visibleChunkItems=e,n.updateVisibleChunks()},n.handleMouseLeaveTreemap=function(){n.setState({showTooltip:!1})},n.handleTreemapGroupHover=function(e){var t=e.group;t?n.setState({showTooltip:!0,tooltipContent:n.getTooltipContent(t)}):n.setState({showTooltip:!1})},n.updateChunks(e.chunks,{initial:!0}),n}return u(t,e),l(t,[{key:"componentWillReceiveProps",value:function(e){e.chunks!==this.props.chunks&&this.updateChunks(e.chunks)}},{key:"render",value:function(){var e=this.state,t=e.visibleChunks,n=e.showTooltip,r=e.tooltipContent,o=e.activeSizeItem;return(0,c.h)("div",{className:S.default.container},(0,c.h)(x.default,null,(0,c.h)("div",{className:S.default.sidebarGroup},(0,c.h)(y.default,{label:"Treemap sizes",items:this.sizeSwitchItems,activeItem:o,onSwitch:this.handleSizeSwitch})),this.state.chunkItems.length>1&&(0,c.h)("div",{className:S.default.sidebarGroup},(0,c.h)(C.default,{label:"Show chunks",items:this.state.chunkItems,checkedItems:this.visibleChunkItems,renderLabel:this.renderChunkItemLabel,onChange:this.handleVisibleChunksChange}))),(0,c.h)(d.default,{className:S.default.map,data:t,weightProp:o.prop,onMouseLeave:this.handleMouseLeaveTreemap,onGroupHover:this.handleTreemapGroupHover}),(0,c.h)(v.default,{visible:n},r))}},{key:"renderModuleSize",value:function(e,t){var n=t+"Size",r=e[n],o=z.find(function(e){return e.prop===n}).label,i=this.state.activeSizeItem.prop===n;return"number"==typeof r?(0,c.h)("div",{className:i?S.default.activeSize:""},o," size: ",(0,c.h)("strong",null,(0,f.default)(r))):null}},{key:"updateChunks",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.initial,r=t.activeSizeItem;this.chunks=e;var i=e.some(h.isChunkParsed);if(this.sizeSwitchItems=i?z:z.slice(0,1),!r){var a=n?this.props.defaultSizes+"Size":this.state.activeSizeItem.prop;r=this.sizeSwitchItems.find(function(e){return e.prop===a}),r||(r=this.sizeSwitchItems[0])}var u=[].concat(o(e));"statSize"!==r.prop&&(u=u.filter(h.isChunkParsed)),u.sort(function(e,t){return t[r.prop]-e[r.prop]}),n&&(this.visibleChunkItems=u),this.setState({showTooltip:!1,tooltipContent:null,activeSizeItem:r,chunkItems:u}),this.updateVisibleChunks()}},{key:"updateVisibleChunks",value:function(){var e=this;this.setState({visibleChunks:this.chunks.filter(function(t){return e.visibleChunkItems.find(function(e){return t.label===e.label})})})}},{key:"getTooltipContent",value:function(e){return e?(0,c.h)("div",null,(0,c.h)("div",null,(0,c.h)("strong",null,e.label)),(0,c.h)("br",null),this.renderModuleSize(e,"stat"),!e.inaccurateSizes&&this.renderModuleSize(e,"parsed"),!e.inaccurateSizes&&this.renderModuleSize(e,"gzip"),e.path&&(0,c.h)("div",null,"Path: ",(0,c.h)("strong",null,e.path))):null}},{key:"totalChunksSize",get:function(){var e=this.state.activeSizeItem.prop;return this.chunks.reduce(function(t,n){return t+(n[e]||0)},0)}}]),t}(c.Component);t.default=k},function(e,t,n){"use strict";(function(t){(function(t){function n(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=[],a=0,u=void 0,l=void 0,c=void 0,s=void 0,f=void 0,h=void 0,p=void 0,d=void 0,b=void 0,v=void 0,g=void 0,y=void 0,m=void 0,x=void 0;if(isNaN(e))throw new Error("Invalid arguments");return c=!0===t.bits,g=!0===t.unix,l=t.base||2,v=void 0!==t.round?t.round:g?1:2,y=void 0!==t.spacer?t.spacer:g?"":" ",x=t.symbols||t.suffixes||{},m=2===l?t.standard||"jedec":"jedec",b=t.output||"string",f=!0===t.fullform,h=t.fullforms instanceof Array?t.fullforms:[],u=void 0!==t.exponent?t.exponent:-1,d=Number(e),p=d<0,s=l>2?1e3:1024,p&&(d=-d),(-1===u||isNaN(u))&&(u=Math.floor(Math.log(d)/Math.log(s)))<0&&(u=0),u>8&&(u=8),0===d?(n[0]=0,n[1]=g?"":o[m][c?"bits":"bytes"][u]):(a=d/(2===l?Math.pow(2,10*u):Math.pow(1e3,u)),c&&(a*=8)>=s&&u<8&&(a/=s,u++),n[0]=Number(a.toFixed(u>0?v:0)),n[1]=10===l&&1===u?c?"kb":"kB":o[m][c?"bits":"bytes"][u],g&&(n[1]="jedec"===m?n[1].charAt(0):u>0?n[1].replace(/B$/,""):n[1],r.test(n[1])&&(n[0]=Math.floor(n[0]),n[1]=""))),p&&(n[0]=-n[0]),n[1]=x[n[1]]||n[1],"array"===b?n:"exponent"===b?u:"object"===b?{value:n[0],suffix:n[1],symbol:n[1]}:(f&&(n[1]=h[u]?h[u]:i[m][u]+(c?"bit":"byte")+(1===n[0]?"":"s")),n.join(y))}var r=/^(b|B)$/,o={iec:{bits:["b","Kib","Mib","Gib","Tib","Pib","Eib","Zib","Yib"],bytes:["B","KiB","MiB","GiB","TiB","PiB","EiB","ZiB","YiB"]},jedec:{bits:["b","Kb","Mb","Gb","Tb","Pb","Eb","Zb","Yb"],bytes:["B","KB","MB","GB","TB","PB","EB","ZB","YB"]}},i={iec:["","kibi","mebi","gibi","tebi","pebi","exbi","zebi","yobi"],jedec:["","kilo","mega","giga","tera","peta","exa","zetta","yotta"]};n.partial=function(e){return function(t){return n(t,e)}},e.exports=n})("undefined"!=typeof window&&window)}).call(t,n(10))},function(e,t){var n;n=function(){return this}();try{n=n||Function("return this")()||(0,eval)("this")}catch(e){"object"==typeof window&&(n=window)}e.exports=n},function(e,t,n){"use strict";function r(e){return"number"==typeof e.parsedSize}Object.defineProperty(t,"__esModule",{value:!0}),t.isChunkParsed=r},function(e,t,n){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function i(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}function a(e,t){return e.reduce(function(e,n){return n[t]&&(n.groups&&(n=Object.assign({},n,{groups:a(n.groups,t)})),n.weight=n[t],e.push(n)),e},[])}function u(e){e.preventDefault()}Object.defineProperty(t,"__esModule",{value:!0});var l=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},c=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),s=n(0),f=n(13),h=function(e){return e&&e.__esModule?e:{default:e}}(f),p=function(e){function t(e){r(this,t);var n=o(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return n.saveNode=function(e){return n.node=e},n.treemap=null,n.zoomOutDisabled=!1,n}return i(t,e),c(t,[{key:"componentDidMount",value:function(){this.updateData(this.props.weightProp),this.treemap=this.createTreemap(),window.addEventListener("resize",this.treemap.resize)}},{key:"componentWillReceiveProps",value:function(e){e.data===this.props.data&&e.weightProp===this.props.weightProp||(this.updateData(e.weightProp,e.data),this.treemap.set({dataObject:this.treemapDataObject}))}},{key:"shouldComponentUpdate",value:function(){return!1}},{key:"componentWillUnmount",value:function(){window.removeEventListener("resize",this.treemap.resize),this.treemap.dispose()}},{key:"render",value:function(){return(0,s.h)("div",l({},this.props,{ref:this.saveNode}))}},{key:"createTreemap",value:function(){var e=this,t=this.props,n=!1;return new h.default({element:this.node,layout:"squarified",stacking:"flattened",pixelRatio:window.devicePixelRatio||1,maxGroupLevelsDrawn:Number.MAX_VALUE,maxGroupLabelLevelsDrawn:Number.MAX_VALUE,groupLabelVerticalPadding:.2,rolloutDuration:0,pullbackDuration:0,fadeDuration:0,zoomMouseWheelDuration:300,openCloseDuration:200,dataObject:this.treemapDataObject,titleBarDecorator:function(e,t,n){n.titleBarShown=!1},onGroupClick:function(e){u(e),n=!1,this.zoom(e.group)},onGroupDoubleClick:u,onGroupHover:function(n){if(n.group&&n.group.attribution)return void n.preventDefault();t.onGroupHover&&t.onGroupHover.call(e,n)},onGroupMouseWheel:function(e){if(e.delta<0){if(n)return u(e);this.get("viewport").scale<1&&(n=!0,u(e))}else n=!1}})}},{key:"update",value:function(){this.treemap.update()}},{key:"updateData",value:function(e,t){t=t||this.props.data,this.data=a(t,e)}},{key:"treemapDataObject",get:function(){return{groups:this.data}}}]),t}(s.Component);t.default=p},function(e,t){(function(){function e(){function e(){if(!i)throw"AF0";var e=F.now();0!==a&&(n.Jd=e-a),a=e,o=o.filter(function(e){return null!==e}),n.frames++;for(var r=0;r<o.length;r++){var u=o[r];null!==u&&(!0===u.ze.call(u.Yg)?o[r]=null:H.Rc(u.repeat)&&(u.repeat=u.repeat-1,0>=u.repeat&&(o[r]=null)))}o=o.filter(function(e){return null!==e}),i=!1,t(),e=F.now()-e,0!==e&&(n.Id=e),n.totalTime+=e,n.Pe=1e3*n.frames/n.totalTime,a=0===o.length?0:F.now()}function t(){0<o.length&&!i&&(i=!0,r(e))}var n=this.rg={frames:0,totalTime:0,Id:0,Jd:0,Pe:0};P=n;var r=function(){return D.mf()?function(e){window.setTimeout(e,0)}:window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(){var e=F.create();return function(t){var n=0;window.setTimeout(function(){var r=e.now();t(),n=e.now()-r},16>n?16-n:0)}}()}(),o=[],i=!1,a=0;this.repeat=function(e,n,r){this.cancel(e),o.push({ze:e,Yg:r,repeat:n}),t()},this.d=function(e,t){this.repeat(e,1,t)},this.cancel=function(e){for(var t=0;t<o.length;t++){var n=o[t];null!==n&&n.ze===e&&(o[t]=null)}},this.k=function(){o=[]}}function t(e){function t(e){s[e].style.opacity=h*f[e]}function n(e){e.width=Math.round(i*e.n),e.height=Math.round(a*e.n)}function r(){return/relative|absolute|fixed/.test(window.getComputedStyle(o,null).getPropertyValue("position"))}var o,i,a,u,l,c=[],s={},f={},h=0;this.H=function(t){o=t,r()||(o.style.position="relative"),0!=o.clientWidth&&0!=o.clientHeight||$.Pa("element has zero dimensions: "+o.clientWidth+" x "+o.clientHeight+"."),o.innerHTML="",i=o.clientWidth,a=o.clientHeight,u=0!==i?i:void 0,l=0!==a?a:void 0,"embedded"===o.getAttribute("data-foamtree")&&$.Pa("visualization already embedded in the element."),o.setAttribute("data-foamtree","embedded"),e.c.p("stage:initialized",this,o,i,a)},this.kb=function(){o.removeAttribute("data-foamtree"),c=[],s={},e.c.p("stage:disposed",this,o)},this.k=function(){if(r()||(o.style.position="relative"),i=o.clientWidth,a=o.clientHeight,0!==i&&0!==a&&(i!==u||a!==l)){for(var t=c.length-1;0<=t;t--)n(c[t]);e.c.p("stage:resized",u,l,i,a),u=i,l=a}},this.ej=function(e,t){e.n=t,n(e)},this.oc=function(r,i,a){var u=document.createElement("canvas");return u.setAttribute("style","position: absolute; top: 0; bottom: 0; left: 0; right: 0; width: 100%; height: 100%; -webkit-touch-callout: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none;"),u.n=i,n(u),c.push(u),s[r]=u,f[r]=1,t(r),a||o.appendChild(u),e.c.p("stage:newLayer",r,u),u},this.kc=function(e,n){return H.V(n)||(f[e]=n,t(e)),f[e]},this.d=function(e){return H.V(e)||(h=e,H.Ga(s,function(e,n){t(n)})),h}}function n(e){function t(e,t,n){return y=!0,d.x=0,d.y=0,b.x=0,b.y=0,a=h,u.x=p.x,u.y=p.y,t(),l*=e,c=n?l/a:e,c=Math.max(.25/a,c),!0}function n(e,t){return t.x=e.x/h+p.x,t.y=e.y/h+p.y,t}function r(e,t,n,r,o,i,a,u,l){var c=(e-n)*(i-u)-(t-r)*(o-a);return!(1e-5>Math.abs(c))&&(l.x=((e*r-t*n)*(o-a)-(e-n)*(o*u-i*a))/c,l.y=((e*r-t*n)*(i-u)-(t-r)*(o*u-i*a))/c,!0)}var o,i,a=1,u={x:0,y:0},l=1,c=1,s=1,f={x:0,y:0},h=1,p={x:0,y:0},d={x:0,y:0},b={x:0,y:0},v={x:0,y:0,f:0,i:0},g={x:0,y:0,f:0,i:0,scale:1},y=!0;e.c.j("stage:initialized",function(e,t,n,r){o=n,i=r,v.x=0,v.y=0,v.f=n,v.i=r,g.x=0,g.y=0,g.f=n,g.i=r,g.scale=1}),e.c.j("stage:resized",function(e,t,n,r){function a(e){e.x*=c,e.y*=s}function l(e){a(e),e.f*=c,e.i*=s}o=n,i=r;var c=n/e,s=r/t;a(u),a(p),a(f),a(d),a(b),l(v),l(g)}),this.Yb=function(e,r){return t(r,function(){n(e,f)},!0)},this.Y=function(e,n){if(1==Math.round(1e4*n)/1e4){var o=v.x-p.x,i=v.y-p.y;return t(1,function(){},!0),this.d(-o,-i)}return t(n,function(){for(var t=!1;!t;)var t=Math.random(),n=Math.random(),o=Math.random(),i=Math.random(),t=r(e.x+t*e.f,e.y+n*e.i,v.x+t*v.f,v.y+n*v.i,e.x+o*e.f,e.y+i*e.i,v.x+o*v.f,v.y+i*v.i,f)},!0)},this.sc=function(e,n){var a,u,l,c;return a=e.f/e.i,u=o/i,a<u?(l=e.i*u,c=e.i,a=e.x-.5*(l-e.f),u=e.y):a>u?(l=e.f,c=e.f*i/o,a=e.x,u=e.y-.5*(c-e.i)):(a=e.x,u=e.y,l=e.f,c=e.i),a-=l*n,u-=c*n,l*=1+2*n,r(a,u,p.x,p.y,a+l,u,p.x+o/h,p.y,f)?t(o/h/l,H.ta,!1):(y=!1,this.d(h*(p.x-a),h*(p.y-u)))},this.d=function(e,t){var n=Math.round(1e4*e)/1e4,r=Math.round(1e4*t)/1e4;return b.x+=n/h,b.y+=r/h,0!==n||0!==r},this.reset=function(e){return e&&this.content(0,0,o,i),this.Y({x:v.x+p.x,y:v.y+p.y,f:v.f/h,i:v.i/h},s/l)},this.Pb=function(e){s=Math.min(1,Math.round(1e4*(e||l))/1e4)},this.k=function(){return p.x<v.x?(v.x-p.x)*h:p.x+o/h>v.x+v.f?-(p.x+o/h-v.x-v.f)*h:0},this.A=function(){return p.y<v.y?(v.y-p.y)*h:p.y+i/h>v.y+v.i?-(p.y+i/h-v.y-v.i)*h:0},this.update=function(e){var t=Math.abs(Math.log(c));6>t?t=2:(t/=4,t+=3*t*(1<c?e:1-e)),t=1<c?Math.pow(e,t):1-Math.pow(1-e,t),t=(y?t:1)*(c-1)+1,h=a*t,p.x=f.x-(f.x-u.x)/t,p.y=f.y-(f.y-u.y)/t,p.x-=d.x*(1-e)+b.x*e,p.y-=d.y*(1-e)+b.y*e,1===e&&(d.x=b.x,d.y=b.y),g.x=p.x,g.y=p.y,g.f=o/h,g.i=i/h,g.scale=h},this.S=function(e){return e.x=g.x,e.y=g.y,e.scale=g.scale,e},this.absolute=function(e,t){return n(e,t||{})},this.md=function(e,t){var n=t||{};return n.x=(e.x-p.x)*h,n.y=(e.y-p.y)*h,n},this.Gc=function(e){return this.scale()<s/e},this.Rd=function(){return H.Ed(h,1)},this.scale=function(){return Math.round(1e4*h)/1e4},this.content=function(e,t,n,r){v.x=e,v.y=t,v.f=n,v.i=r},this.Ic=function(e,t){var n;for(n=e.length-1;0<=n;n--){var r=e[n];r.save(),r.scale(h,h),r.translate(-p.x,-p.y)}for(t(g),n=e.length-1;0<=n;n--)r=e[n],r.restore()}}function r(){var e,t=!1,n=[],r=this,o=new function(){this.N=function(o){return o&&(t?o.apply(r,e):n.push(o)),this},this.ih=function(e){return r=e,{then:this.N}}};this.J=function(){e=arguments;for(var o=0;o<n.length;o++)n[o].apply(r,e);return t=!0,this},this.L=function(){return o}}function o(e){var t=new r,n=e.length;if(0<e.length)for(var o=e.length-1;0<=o;o--)e[o].N(function(){0==--n&&t.J()});else t.J();return t.L()}function i(e){var t=0;this.d=function(){t++},this.k=function(){0===--t&&e()},this.clear=function(){t=0},this.A=function(){return 0===t}}function a(){var e=document,t={};this.addEventListener=function(n,r){var o=t[n];o||(o=[],t[n]=o),o.push(r),e.addEventListener(n,r)},this.d=function(){H.Ga(t,function(t,n){for(var r=t.length-1;0<=r;r--)e.removeEventListener(n,t[r])})}}function u(e){function t(e){return function(t){n(t)&&e.apply(this,arguments)}}function n(t){for(t=t.target;t;){if(t===e)return!0;t=t.parentElement}return!1}function r(e,t,n){n=n||{},o(e,n);for(var r=0;r<t.length;r++)t[r].call(e.target,n);return(void 0===n.Lb&&n.yi||"prevent"===n.Lb)&&e.preventDefault(),n}function o(t,n){return B.Je(e,t.clientX,t.clientY,n),n.altKey=t.altKey,n.metaKey=t.metaKey,n.ctrlKey=t.ctrlKey,n.shiftKey=t.shiftKey,n.wb=3===t.which,n}var i=new a,u=[],l=[],c=[],s=[],f=[],h=[],p=[],d=[],b=[],v=[],g=[];this.d=function(e){u.push(e)},this.k=function(e){f.push(e)},this.ya=function(e){l.push(e)},this.Ba=function(e){c.push(e)},this.Pa=function(e){s.push(e)},this.Aa=function(e){g.push(e)},this.za=function(e){h.push(e)},this.Ja=function(e){p.push(e)},this.Y=function(e){d.push(e)},this.A=function(e){b.push(e)},this.S=function(e){v.push(e)},this.kb=function(){i.d()};var y,m,x,w,C={x:0,y:0},T={x:0,y:0},S=!1,z=!1;i.addEventListener("mousedown",t(function(t){if(t.target!==e){var n=r(t,c);T.x=n.x,T.y=n.y,C.x=n.x,C.y=n.y,S=!0,r(t,d),m=!1,y=window.setTimeout(function(){100>V.d(C,n)&&(window.clearTimeout(w),r(t,l),m=!0)},400)}})),i.addEventListener("mouseup",function(e){if(r(e,s),S){if(z&&r(e,v),window.clearTimeout(y),!m&&!z&&n(e)){var t=function(e){var t={};return t.x=e.pageX,t.y=e.pageY,t}(e);x&&100>V.d(t,x)?r(e,f):r(e,u),x=t,w=window.setTimeout(function(){x=null},350)}z=S=!1}}),i.addEventListener("mousemove",function(e){var t=o(e,{});n(e)&&r(e,h,{type:"move"}),C.x=t.x,C.y=t.y,S&&!z&&100<V.d(T,C)&&(z=!0),z&&r(e,b,t)}),i.addEventListener("mouseout",t(function(e){r(e,p,{type:"out"})})),i.addEventListener(void 0!==document.onmousewheel?"mousewheel":"MozMousePixelScroll",t(function(e){var t=e.wheelDelta,n=e.detail;r(e,g,{vd:(n?t?0<t/n/40*n?1:-1:-n/(D.nf()?40:19):t/40)/3,yi:!0})})),i.addEventListener("contextmenu",t(function(e){e.preventDefault()}))}function l(){var e={};this.j=function(t,n){var r=e[t];r||(r=[],e[t]=r),r.push(n)},this.p=function(t,n){var r=e[t];if(r)for(var o=Array.prototype.slice.call(arguments,1),i=0;i<r.length;i++)r[i].apply(this,o)}}function c(e){function t(t,n,r){var i,s=this,f=0;this.id=u++,this.name=r||"{unnamed on "+t+"}",this.target=function(){return t},this.Fb=function(){return-1!=c.indexOf(s)},this.start=function(){if(!s.Fb()){if(-1==c.indexOf(s)){var t=l.now();!0===s.xf(t)&&(c=c.slice(),c.push(s))}0<c.length&&e.repeat(o)}return this},this.stop=function(){for(a(s);i<n.length;i++){var e=n[i];e.ib&&e.Xa.call()}return this},this.eg=function(){i=void 0},this.xf=function(e){if(f++,0!==n.length){var t;for(H.V(i)?(i=0,t=n[i],t.W&&t.W.call(t,e,f,s)):t=n[i];i<n.length;){if(t.Xa&&t.Xa.call(t,e,f,s))return!0;t.Da&&t.Da.call(t,e,f,s),H.V(i)&&(i=-1),++i<n.length&&(t=n[i],t.W&&t.W.call(t,e,f,s))}}return!1}}function n(e){return H.V(e)?c.slice():c.filter(function(t){return t.target()===e})}function o(){i(),0==c.length&&e.cancel(o)}function i(){var e=l.now();c.forEach(function(t){!0!==t.xf(e)&&a(t)})}function a(e){c=c.filter(function(t){return t!==e})}var u=0,l=F.create(),c=[];this.d=function(){for(var e=c.length-1;0<=e;e--)c[e].stop();c=[]},this.D=function(){function e(){}function o(e){var t,n,r=e.target,o=e.duration,i=e.ca;this.W=function(){t={};for(var o in e.G)r.hasOwnProperty(o)&&(t[o]={start:H.V(e.G[o].start)?r[o]:H.Fd(e.G[o].start)?e.G[o].start.call(void 0):e.G[o].start,end:H.V(e.G[o].end)?r[o]:H.Fd(e.G[o].end)?e.G[o].end.call(void 0):e.G[o].end,P:H.V(e.G[o].P)?R.Ib:e.G[o].P});n=l.now()},this.Xa=function(){var e,a=l.now()-n,a=0===o?1:Math.min(o,a)/o;for(e in t){var u=t[e];r[e]=u.start+(u.end-u.start)*u.P(a)}return i&&i.call(r,a),1>a}}function i(e,t,n){this.ib=n,this.Xa=function(){return e.call(t),!1}}function a(e){var t;this.W=function(n,r){t=r+e},this.Xa=function(e,n){return n<t}}function u(e){var t;this.W=function(n){t=n+e},this.Xa=function(e){return e<t}}function c(e){this.W=function(){e.forEach(function(e){e.start()})},this.Xa=function(){for(var t=0;t<e.length;t++)if(e[t].Fb())return!0;return!1}}return e.m=function(e,n){return new function(){function l(t,n,r,o){return n?(H.V(r)&&(r=e),t.Ab(new i(n,r,o))):t}var s=[];this.Ab=function(e){return s.push(e),this},this.fb=function(e){return this.Ab(new u(e))},this.oe=function(e){return this.Ab(new a(e||1))},this.call=function(e,t){return l(this,e,t,!1)},this.ib=function(e,t){return l(this,e,t,!0)},this.ia=function(t){return H.V(t.target)&&(t.target=e),this.Ab(new o(t))},this.Ya=function(e){return this.Ab(new c(e))},this.eg=function(){return this.Ab({Xa:function(e,t){return t.eg(),!0}})},this.xa=function(){return new t(e,s,n)},this.start=function(){return this.xa().start()},this.Fg=function(){var e=new r;return this.oe().call(e.J).xa(),e.L()},this.bb=function(){var e=this.Fg();return this.start(),e}}},e.tc=function(t){return n(t).forEach(function(e){e.stop()}),e.m(t,void 0)},e}()}function s(e){var t,n={},r=e.Ud;e.c.j("model:loaded",function(e){t=e}),this.H=function(){e.c.p("api:initialized",this)},this.Cc=function(e,t,o,i){this.od(n,t),this.pd(n,t),this.nd(n,t,!1),i&&i(n),e(r,n,o)},this.td=function(e,n,r,o,i,a,u){if(e){for(e=n.length-1;0<=e;e--){var l=n[e],c=H.extend({group:l.group},i);c[r]=o(l),a(c)}0<n.length&&u(H.extend({groups:q.Lc(t,o).map(function(e){return e.group})},i))}},this.pd=function(e,t){return e.selected=t.selected,e.hovered=t.Db,e.open=t.open,e.openness=t.Kb,e.exposed=t.U,e.exposure=t.ka,e.transitionProgress=t.ua,e.revealed=!t.ba.Na(),e.browseable=t.Qa?t.M:void 0,e.visible=t.ea,e.labelDrawn=t.ra&&t.ra.la,e},this.od=function(e,t){var n=t.parent;return e.group=t.group,e.parent=n&&n.group,e.weightNormalized=t.xg,e.level=t.R-1,e.siblingCount=n&&n.e.length,e.hasChildren=!t.empty(),e.index=t.index,e.indexByWeight=t.Dd,e.description=t.description,e.attribution=t.na,e},this.nd=function(e,t,n){if(e.polygonCenterX=t.K.x,e.polygonCenterY=t.K.y,e.polygonArea=t.K.ja,e.boxLeft=t.q.x,e.boxTop=t.q.y,e.boxWidth=t.q.f,e.boxHeight=t.q.i,t.ra&&t.ra.la){var r=t.ra.da;e.labelBoxLeft=r.x,e.labelBoxTop=r.y,e.labelBoxWidth=r.f,e.labelBoxHeight=r.i,e.labelFontSize=t.ra.fontSize}return n&&t.aa&&(e.polygon=t.aa.map(function(e){return{x:e.x,y:e.y}}),e.neighbors=t.C&&t.C.map(function(e){return e&&e.group})),e}}function f(e){function t(t,r){t.e=[],t.La=!0;var i=o(r),a=0;if("flattened"==e.ab&&0<r.length&&0<t.R){var u=r.reduce(function(e,t){return e+H.B(t.weight,1)},0),l=n(t.group,!1);l.description=!0,l.T=u*e.cc,l.index=a++,l.parent=t,l.R=t.R+1,l.id=l.id+"_d",t.e.push(l)}for(u=0;u<r.length;u++){var c=r[u],l=H.B(c.weight,1);if(0>=l){if(!e.tj)continue;l=.9*i}c=n(c,!0),c.T=l,c.index=a,c.parent=t,c.R=t.R+1,t.e.push(c),a++}}function n(e,t){var n=new z;return r(e),n.id=e.__id,n.group=e,t&&(c[e.__id]=n),n}function r(e){H.Q(e,"__id")||(Object.defineProperty(e,"__id",{enumerable:!1,configurable:!1,writable:!1,value:l}),l++)}function o(e){for(var t=Number.MAX_VALUE,n=0;n<e.length;n++){var r=e[n].weight;0<r&&t>r&&(t=r)}return t===Number.MAX_VALUE&&(t=1),t}function i(e){if(!e.empty()){e=e.e;var t,n=0;for(t=e.length-1;0<=t;t--){var r=e[t].T;n<r&&(n=r)}for(t=e.length-1;0<=t;t--)r=e[t],r.xg=r.T/n}}function a(e){if(!e.empty()){e=e.e.slice(0).sort(function(e,t){return e.T<t.T?1:e.T>t.T?-1:e.index-t.index});for(var t=0;t<e.length;t++)e[t].Dd=t}}function u(){for(var t=d.e.reduce(function(e,t){return e+t.T},0),n=0;n<d.e.length;n++){var r=d.e[n];r.na&&(r.T=Math.max(.025,e.Ug)*t)}}var l,c,s,f,h,p=this,d=new z;this.H=function(){return d},this.S=function(n){var r=n.group.groups,o=e.pi;return!!(!n.e&&!n.description&&r&&0<r.length&&h+r.length<=o)&&(h+=r.length,t(n,r),i(n),a(n),!0)},this.Y=function(e){function o(e){var t=e.groups;if(t)for(var n=0;n<t.length;n++){var i=t[n];r(i);var a=i.__id;c[a]=null,f[a]=e,a=i.id,H.V(a)||(s[a]=i),o(i)}}function p(e,t){if(!e)return t;var n=Math.max(t,e.__id||0),r=e.groups;if(r&&0<r.length)for(var o=r.length-1;0<=o;o--)n=p(r[o],n);return n}d.group=e,d.Ca=!1,d.M=!1,d.Qa=!1,d.open=!0,d.Kb=1,l=p(e,0)+1,c={},s={},f={},h=0,e&&(r(e),c[e.__id]=d,H.V(e.id)||(s[e.id]=e),o(e)),t(d,e&&e.groups||[]),function(e){if(!e.empty()){var t=n({attribution:!0});t.index=e.e.length,t.parent=e,t.R=e.R+1,t.na=!0,e.e.push(t)}}(d),i(d),u(),a(d)},this.update=function(){q.Fa(d,function(e){if(!e.empty()){e=e.e;for(var t=o(e.map(function(e){return e.group})),n=0;n<e.length;n++){var r=e[n];r.T=0<r.group.weight?r.group.weight:.9*t}}}),i(d),u(),a(d)},this.A=function(e){return function(){if(H.V(e)||H.of(e))return[];if(Array.isArray(e))return e.map(p.d,p);if(H.jc(e)){if(H.Q(e,"__id"))return[p.d(e)];if(H.Q(e,"all")){var t=[];return q.F(d,function(e){t.push(e)}),t}if(H.Q(e,"groups"))return p.A(e.groups)}return[p.d(e)]}().filter(function(e){return void 0!==e})},this.d=function(e){if(H.jc(e)&&H.Q(e,"__id")){if(e=e.__id,H.Q(c,e)){if(null===c[e]){for(var t=f[e],n=[];t&&(t=t.__id,n.push(t),!c[t]);)t=f[t];for(t=n.length-1;0<=t;t--)this.S(c[n[t]])}return c[e]}}else if(H.Q(s,e))return this.d(s[e])},this.k=function(e,t,n){return{e:p.A(e),Ia:H.B(e&&e[t],!0),Ha:H.B(e&&e.keepPrevious,n)}}}function h(e,t,n){var r={};t.Ha&&q.F(e,function(e){n(e)&&(r[e.id]=e)}),e=t.e,t=t.Ia;for(var o=e.length-1;0<=o;o--){var i=e[o];r[i.id]=t?i:void 0}var a=[];return H.Ga(r,function(e){void 0!==e&&a.push(e)}),a}function p(e){function t(e,t){var n=e.ka;t.opacity=1,t.Ka=1,t.va=0>n?1-k.ei/100*n:1,t.wa=0>n?1-k.fi/100*n:1,t.fa=0>n?1+.5*n:1}function n(e){return e=e.ka,Math.max(.001,0===e?1:1+e*(k.Va-1))}function i(e,t){for(var n=e.reduce(function(e,t){return e[t.id]=t,e},{}),r=e.length-1;0<=r;r--)q.F(e[r],function(e){n[e.id]=void 0});var i=[];H.Ga(n,function(e){e&&q.He(e,function(e){e.open||i.push(e)})});var a=[];return H.Ga(n,function(e){e&&e.open&&a.push(e)}),r=[],0!==i.length&&r.push(w.Jb({e:i,Ia:!0,Ha:!0},t,!0)),o(r)}function a(t,n,i,a){var h=c();if(0===t.length&&!h)return(new r).J().L();var d=t.reduce(function(e,t){return e[t.id]=!0,e},{}),b=[];if(t=[],C.reduce(function(e,t){return e||d[t.id]&&(!t.U||1!==t.ka)||!d[t.id]&&!t.parent.U&&(t.U||-1!==t.ka)},!1)){var g=[],w={};C.forEach(function(e){d[e.id]&&(e.U||b.push(e),e.U=!0,q.Fa(e,function(e){g.push(f(e,1)),w[e.id]=!0}))}),0<g.length?(q.F(v,function(e){d[e.id]||(e.U&&b.push(e),e.U=!1),w[e.id]||g.push(f(e,-1))}),t.push(x.D.m({}).Ya(g).call(p).bb()),u(d),t.push(l(h)),i&&(m.sc(T,k.Pc,k.Ua,R.pa(k.gc)),m.Pb())):(t.push(s(i)),n&&q.F(v,function(e){e.U&&b.push(e)}))}return o(t).N(function(){y.td(n,b,"exposed",function(e){return e.U},{indirect:a},e.options.Ef,e.options.Df)})}function u(e){C.reduce(d(!0,void 0,function(t){return t.U||e[t.id]}),b(T)),T.x-=T.f*(k.Va-1)/2,T.y-=T.i*(k.Va-1)/2,T.f*=k.Va,T.i*=k.Va}function l(t){return t||!m.Rd()?x.D.m(g).ia({duration:.7*k.Ua,G:{x:{end:T.x+T.f/2,P:R.pa(k.gc)},y:{end:T.y+T.i/2,P:R.pa(k.gc)}},ca:function(){e.c.p("foamtree:dirty",!0)}}).bb():(g.x=T.x+T.f/2,g.y=T.y+T.i/2,(new r).J().L())}function c(){return!!C&&C.reduce(function(e,t){return e||0!==t.ka},!1)}function s(e){var t=[],n=[];return q.F(v,function(e){0!==e.ka&&n.push(f(e,0,function(){this.U=!1}))}),t.push(x.D.m({}).Ya(n).bb()),m.content(0,0,S,z),e&&(t.push(m.reset(k.Ua,R.pa(k.gc))),m.Pb()),o(t)}function f(n,r,o){var i=x.D.m(n);return 0===n.ka&&0!==r&&i.call(function(){this.Bc(M),this.zb(t)}),i.ia({duration:k.Ua,G:{ka:{end:r,P:R.pa(k.gc)}},ca:function(){v.I=!0,v.Ma=!0,e.c.p("foamtree:dirty",!0)}}),0===r&&i.call(function(){this.Md(),this.nc(),this.ed(M),this.dd(t)}),i.call(o).xa()}function p(){var e=v.e.reduce(d(!1,M.Tb,void 0),b({})).da,t=k.Pc,n=Math.min(e.x,T.x-T.f*t),r=Math.max(e.x+e.f,T.x+T.f*(1+t)),o=Math.min(e.y,T.y-T.i*t),e=Math.max(e.y+e.i,T.y+T.i*(1+t));m.content(n,o,r-n,e-o)}function d(e,t,n){var r={};return function(o,i){if(!n||n(i)){for(var a,u=e?i.aa||i.o:i.o,l=u.length-1;0<=l;l--)a=void 0!==t?t(i,u[l],r):u[l],o.Zc=Math.min(o.Zc,a.x),o.Od=Math.max(o.Od,a.x),o.$c=Math.min(o.$c,a.y),o.Pd=Math.max(o.Pd,a.y);o.da.x=o.Zc,o.da.y=o.$c,o.da.f=o.Od-o.Zc,o.da.i=o.Pd-o.$c}return o}}function b(e){return{Zc:Number.MAX_VALUE,Od:Number.MIN_VALUE,$c:Number.MAX_VALUE,Pd:Number.MIN_VALUE,da:e}}var v,g,y,m,x,w,C,T,S,z,k=e.options,M={sf:function(e,t){return t.scale=n(e),!1},Sb:function(e,t){var r=n(e),o=g.x,i=g.y;t.translate(o,i),t.scale(r,r),t.translate(-o,-i)},Ub:function(e,t,r){e=n(e);var o=g.x,i=g.y;r.x=(t.x-o)/e+o,r.y=(t.y-i)/e+i},Tb:function(e,t,r){e=n(e);var o=g.x,i=g.y;return r.x=(t.x-o)*e+o,r.y=(t.y-i)*e+i,r}};e.c.j("stage:initialized",function(e,t,n,r){g={x:n/2,y:r/2},S=n,z=r,T={x:0,y:0,f:S,i:z}}),e.c.j("stage:resized",function(e,t,n,r){g.x*=n/e,g.y*=r/t,S=n,z=r}),e.c.j("api:initialized",function(e){y=e}),e.c.j("zoom:initialized",function(e){m=e}),e.c.j("model:loaded",function(e,t){v=e,C=t}),e.c.j("model:childrenAttached",function(e){C=e}),e.c.j("timeline:initialized",function(e){x=e}),e.c.j("openclose:initialized",function(e){w=e});var L=["groupExposureScale","groupUnexposureScale","groupExposureZoomMargin"];e.c.j("options:changed",function(e){H.nb(e,L)&&c()&&(u({}),m.Aj(T,k.Pc),m.Pb())}),this.H=function(){e.c.p("expose:initialized",this)},this.fc=function(e,t,n,o){var u=e.e.reduce(function(e,t){for(var n=t;n=n.parent;)e[n.id]=!0;return e},{}),l=h(v,e,function(e){return e.U&&!e.open&&!u[e.id]}),c=new r;return i(l,t).N(function(){a(l.filter(function(e){return e.o&&e.aa}),t,n,o).N(c.J)}),c.L()}}function d(e){function t(t){function n(e,t){var n=Math.min(1,Math.max(0,e.ua));t.opacity=n,t.va=1,t.wa=n,t.Ka=n,t.fa=e.Gb}var i=e.options,a=i.oj,s=i.pj,f=i.lj,h=i.mj,p=i.nj,d=i.fe,b=a+s+f+h+p,v=0<b?d/b:0,g=[];if(c.gb(i.hg,i.gg,i.ig,i.jg,i.fg),0===v&&t.e&&t.M){for(d=t.e,b=0;b<d.length;b++){var y=d[b];y.ua=1,y.Gb=1,y.zb(n),y.nc(),y.dd(n)}return t.I=!0,e.c.p("foamtree:dirty",0<v),(new r).J().L()}if(t.e&&t.M){Y.Ja(t,Y.ya(t,e.options.he),function(t,r,u){t.Bc(c),t.zb(n),u="groups"===e.options.ge?u:r,r=o.D.m(t).fb(u*v*a).ia({duration:v*s,G:{ua:{end:1,P:R.pa(i.kj)}},ca:function(){this.I=!0,e.c.p("foamtree:dirty",0<v)}}).xa(),u=o.D.m(t).fb(l?v*(f+u*h):0).ia({duration:l?v*p:0,G:{Gb:{end:1,P:R.Ib}},ca:function(){this.I=!0,e.c.p("foamtree:dirty",0<v)}}).xa(),t=o.D.m(t).Ya([r,u]).oe().ib(function(){this.Md(),this.nc(),this.ed(c),this.dd(n)}).xa(),g.push(t)}),u.d();var m=new r;return o.D.m({}).Ya(g).call(function(){u.k(),m.J()}).start(),m.L()}return(new r).J().L()}var n,o,a=[],u=new i(H.ta);e.c.j("stage:initialized",function(){}),e.c.j("stage:resized",function(){}),e.c.j("stage:newLayer",function(e,t){a.push(t)}),e.c.j("model:loaded",function(e){n=e,u.clear()}),e.c.j("zoom:initialized",function(){}),e.c.j("timeline:initialized",function(e){o=e});var l=!1;e.c.j("render:renderers:resolved",function(e){l=e.labelPlainFill||!1});var c=new function(){var e=0,t=0,n=0,r=0,o=0,i=0;this.gb=function(a,u,l,c,s){e=1+u,t=1-e,n=l,r=c,o=s,i=a},this.sf=function(i,a){return a.scale=e+t*i.ua,0!==o||0!==n||0!==r},this.Sb=function(a,u){var l=e+t*a.ua,c=a.parent,s=i*a.x+(1-i)*c.x,f=i*a.y+(1-i)*c.y;u.translate(s,f),u.scale(l,l),l=1-a.ua,u.rotate(o*Math.PI*l),u.translate(-s,-f),u.translate(c.q.f*n*l,c.q.i*r*l)},this.Ub=function(o,a,u){var l=e+t*o.ua,c=i*o.x+(1-i)*o.parent.x,s=i*o.y+(1-i)*o.parent.y,f=1-o.ua;o=o.parent,u.x=(a.x-c)/l+c-o.q.f*n*f,u.y=(a.y-s)/l+s-o.q.i*r*f},this.Tb=function(o,a,u){var l=e+t*o.ua,c=i*o.x+(1-i)*o.parent.x,s=i*o.y+(1-i)*o.parent.y,f=1-o.ua;o=o.parent,u.x=(a.x-c)*l+c-o.q.f*n*f,u.y=(a.y-s)*l+s-o.q.i*r*f}};this.H=function(){},this.k=function(){function t(e,t){var n=Math.min(1,Math.max(0,e.ua));t.opacity=n,t.va=1,t.wa=n,t.Ka=n,t.fa=e.Gb}function r(e,t){var n=Math.min(1,Math.max(0,e.Zd));t.opacity=n,t.Ka=n,t.va=1,t.wa=1,t.fa=e.Gb}var i=e.options,a=i.Yd,s=i.Hi,f=i.Ii,h=i.Ji,p=i.Di,d=i.Ei,b=i.Fi,v=i.zi,g=i.Ai,y=i.Bi,m=p+d+b+v+g+y+s+f+h,x=0<m?a/m:0,w=[];return u.A()?c.gb(i.Ni,i.Li,i.Oi,i.Pi,i.Ki):c.gb(i.hg,i.gg,i.ig,i.jg,i.fg),Y.Ja(n,Y.ya(n,e.options.Mi),function(n,a,u){var m="groups"===e.options.Gi?u:a;w.push(o.D.m(n).call(function(){this.zb(t)}).fb(l?x*(p+m*d):0).ia({duration:l?x*b:0,G:{Gb:{end:0,P:R.Ib}},ca:function(){this.I=!0,e.c.p("foamtree:dirty",!0)}}).xa()),q.F(n,function(t){w.push(o.D.m(t).call(function(){this.Bc(c),this.zb(r)}).fb(x*(v+g*m)).ia({duration:x*y,G:{Zd:{end:0,P:R.Ib}},ca:function(){this.I=!0,e.c.p("foamtree:dirty",!0)}}).ib(function(){this.selected=!1,this.ed(c)}).xa())}),w.push(o.D.m(n).call(function(){this.Bc(c)}).fb(x*(s+f*m)).ia({duration:x*h,G:{ua:{end:0,P:R.pa(i.Ci)}},ca:function(){this.I=!0,e.c.p("foamtree:dirty",!0)}}).ib(function(){this.selected=!1,this.ed(c)}).xa())}),o.D.m({}).Ya(w).bb()},this.d=function(e){return t(e)}}function b(e){function t(e,t){var n=[];if(q.F(u,function(t){if(t.e){var r=H.Q(e,t.id);t.open!==r&&(r||t.U||q.F(t,function(e){if(e.U)return n.push(t),!1}))}}),0===n.length)return(new r).J().L();var o;for(o=n.length-1;0<=o;o--)n[o].open=!1;var i=a.fc({e:n,Ia:!0,Ha:!0},t,!0,!0);for(o=n.length-1;0<=o;o--)n[o].open=!0;return i}function n(t,n,a){function c(t,n){t.zb(s);var r=i.D.m(t).ia({duration:e.options.bd,G:{Kb:{end:n?1:0,P:R.Ae}},ca:function(){this.I=!0,e.c.p("foamtree:dirty",!0)}}).call(function(){this.open=n,t.Vb=!1}).ib(function(){this.nc(),this.dd(s),delete o[this.id]}).xa();return o[t.id]=r}function s(e,t){t.opacity=1-e.Kb,t.va=1,t.wa=1,t.fa=1,t.Ka=1}var f=[],h=[];return q.F(u,function(e){if(e.M&&e.X){var n=H.Q(t,e.id),r=o[e.id];if(r&&r.Fb())r.stop();else if(e.open===n)return;e.Vb=n,n||(e.open=n,e.Td=!1),h.push(e),f.push(c(e,n))}}),0<f.length?(e.c.p("openclose:changing"),i.D.m({}).Ya(f).bb().N(function(){l.td(n,h,"open",function(e){return e.open},{indirect:a},e.options.Mf,e.options.Lf)})):(new r).J().L()}var o,i,a,u,l;e.c.j("api:initialized",function(e){l=e}),e.c.j("model:loaded",function(e){u=e,o={}}),e.c.j("timeline:initialized",function(e){i=e}),e.c.j("expose:initialized",function(e){a=e}),this.H=function(){e.c.p("openclose:initialized",this)},this.Jb=function(o,i,a){if("flattened"==e.options.ab)return(new r).J().L();o=h(u,o,function(e){return e.open||e.Vb});for(var l=new r,c=0;c<o.length;c++)o[c].Vb=!0;0<o.length&&e.c.p("foamtree:attachChildren");var s=o.reduce(function(e,t){return e[t.id]=!0,e},{});return t(s,i).N(function(){n(s,i,a).N(l.J)}),l.L()}}function v(e){function t(t,o){var i=h(n,t,function(e){return e.selected});q.F(n,function(e){!0===e.selected&&(e.selected=!e.selected,e.I=!e.I,e.$a=!e.$a)});var a;for(a=i.length-1;0<=a;a--){var u=i[a];u.selected=!u.selected,u.I=!u.I,u.$a=!u.$a}var l=[];q.F(n,function(e){e.I&&l.push(e)}),0<l.length&&e.c.p("foamtree:dirty",!1),r.td(o,l,"selected",function(e){return e.selected},{},e.options.Of,e.options.Nf)}var n,r;e.c.j("api:initialized",function(e){r=e}),e.c.j("model:loaded",function(e){n=e}),this.H=function(){e.c.p("select:initialized",this)},this.select=function(e,n){return t(e,n)}}function g(e){function t(e){return function(t){e.call(this,{x:t.x,y:t.y,scale:t.scale,vd:t.delta,ctrlKey:t.ctrlKey,metaKey:t.metaKey,altKey:t.altKey,shiftKey:t.shiftKey,wb:t.secondary,touches:t.touches})}}function n(){function t(e){return function(t){return t.x*=L/f.clientWidth,t.y*=_/f.clientHeight,e(t)}}"external"!==F.hf&&("hammerjs"===F.hf&&H.Q(window,"Hammer")&&(I.H(f),I.m("tap",t(P.d),!0),I.m("doubletap",t(P.k),!0),I.m("hold",t(P.ya),!0),I.m("touch",t(P.Aa),!1),I.m("release",t(P.Ba),!1),I.m("dragstart",t(P.Y),!0),I.m("drag",t(P.A),!0),I.m("dragend",t(P.S),!0),I.m("transformstart",t(P.Ta),!0),I.m("transform",t(P.Ja),!0),I.m("transformend",t(P.cb),!0)),k=new u(f),M=new a,k.d(t(P.d)),k.k(t(P.k)),k.ya(t(P.ya)),k.Ba(t(P.Aa)),k.Pa(t(P.Ba)),k.Y(t(P.Y)),k.A(t(P.A)),k.S(t(P.S)),k.za(t(P.za)),k.Ja(t(P.za)),k.Aa(t(P.Pa)),M.addEventListener("keyup",function(t){var n=!1,r=void 0,o=F.Sf({keyCode:t.keyCode,preventDefault:function(){n=!0},preventOriginalEventDefault:function(){r="prevent"},allowOriginalEventDefault:function(){r="allow"}});"prevent"===r&&t.preventDefault(),(n=n||0<=o.indexOf(!1))||27===t.keyCode&&e.c.p("interaction:reset")}))}function r(){h.Gc(2)?e.c.p("interaction:reset"):h.normalize(F.wc,R.pa(F.xc))}function o(e){return function(){y.empty()||e.apply(this,arguments)}}function i(e,t,n){var r={},o={};return function(i){var a;switch(e){case"click":a=F.yf;break;case"doubleclick":a=F.zf;break;case"hold":a=F.Ff;break;case"hover":a=F.Gf;break;case"mousemove":a=F.If;break;case"mousewheel":a=F.Kf;break;case"mousedown":a=F.Hf;break;case"mouseup":a=F.Jf;break;case"dragstart":a=F.Cf;break;case"drag":a=F.Af;break;case"dragend":a=F.Bf;break;case"transformstart":a=F.Rf;break;case"transform":a=F.Pf;break;case"transformend":a=F.Qf}var u=!1,l=!a.empty(),f=h.absolute(i,r),p=(t||l)&&c(f),d=(t||l)&&s(f);l&&(l=p?p.group:null,f=p?p.Ub(f,o):f,i.Lb=void 0,a=a({type:e,group:l,topmostClosedGroup:l,bottommostOpenGroup:d?d.group:null,x:i.x,y:i.y,xAbsolute:f.x,yAbsolute:f.y,scale:H.B(i.scale,1),secondary:i.wb,touches:H.B(i.touches,1),delta:H.B(i.vd,0),ctrlKey:i.ctrlKey,metaKey:i.metaKey,altKey:i.altKey,shiftKey:i.shiftKey,preventDefault:function(){u=!0},preventOriginalEventDefault:function(){i.Lb="prevent"},allowOriginalEventDefault:function(){i.Lb="allow"}}),u=u||0<=a.indexOf(!1),p&&p.na&&"click"===e&&(u=!1)),u||n&&n({Dc:p,Wg:d},i)}}function l(e){function t(e,n){var r=n.e;if(r){for(var o,i=-Number.MAX_VALUE,a=0;a<r.length;a++){var u=r[a];!u.description&&u.ea&&N(u,e)&&u.scale>i&&(o=u,i=u.scale)}var l;return o&&(l=t(e,o)),l||o}}return t(e,y)}function c(e,t){var n;if("flattened"==F.ab)n=l(e);else{n=t||0;for(var r=A.length,o=void 0,i=0;i<r;i++){var a=A[i];a.scale>n&&!1===a.open&&a.ea&&N(a,e)&&(o=a,n=a.scale)}n=o}return n}function s(e){var t=void 0,n=0;return q.Jc(y,function(r){!0===r.open&&r.ea&&r.scale>n&&N(r,e)&&(t=r,n=r.scale)}),t}var f,h,p,d,b,v,g,y,m,x,w,C,T,S,z,k,M,L,_,j=D.nf(),P=this,F=e.options,O=!1;e.c.j("stage:initialized",function(e,t,r,o){f=t,L=r,_=o,n()}),e.c.j("stage:resized",function(e,t,n,r){L=n,_=r}),e.c.j("stage:disposed",function(){k.kb(),I.kb(),M.d()}),e.c.j("expose:initialized",function(e){d=e}),e.c.j("zoom:initialized",function(e){h=e}),e.c.j("openclose:initialized",function(e){b=e}),e.c.j("select:initialized",function(e){v=e}),e.c.j("titlebar:initialized",function(e){g=e}),e.c.j("timeline:initialized",function(e){p=e});var A;e.c.j("model:loaded",function(e,t){y=e,A=t}),e.c.j("model:childrenAttached",function(e){A=e}),this.H=function(){},this.Aa=o(i("mousedown",!1,function(){h.ui()})),this.Ba=o(i("mouseup",!1,void 0)),this.d=o(i("click",!0,function(e,t){if(!t.wb&&!t.shiftKey){var n=e.Dc;n&&(n.na?document.location.href=U.kg("iuuq;..b`ssnurd`sbi/bnl.gn`lusdd"):v.select({e:[n],Ia:!n.selected,Ha:t.metaKey||t.ctrlKey},!0))}})),this.k=o(i("doubleclick",!0,function(t,n){var r,o;n.wb||n.shiftKey?(r=t.Dc)&&(r.parent.U&&(r=r.parent),o={e:r.parent!==y?[r.parent]:[],Ia:!0,Ha:!1},v.select(o,!0),d.fc(o,!0,!0,!1)):(r=t.Dc)&&(o={e:[r],Ia:!0,Ha:!1},r.Vb=!0,e.c.p("foamtree:attachChildren"),d.fc(o,!0,!0,!1)),r&&p.D.m({}).fb(F.Ua/2).call(function(){b.Jb({e:q.Lc(y,function(e){return e.Td&&!q.ki(r,e)}),Ia:!1,Ha:!0},!0,!0),r.Td=!0,b.Jb({e:[r],Ia:!(n.wb||n.shiftKey),Ha:!0},!0,!0)}).start()})),this.ya=o(i("hold",!0,function(e,t){var n,r=!(t.metaKey||t.ctrlKey||t.shiftKey||t.wb);(n=r?e.Dc:e.Wg)&&n!==y&&!n.empty()&&b.Jb({e:[n],Ia:r,Ha:!0},!0,!1)})),this.Y=o(i("dragstart",!1,function(e,t){m=t.x,x=t.y,w=Date.now(),O=!0})),this.A=o(i("drag",!1,function(e,t){if(O){var n=Date.now();S=Math.min(1,n-w),w=n;var n=t.x-m,r=t.y-x;h.si(n,r),C=n,T=r,m=t.x,x=t.y}})),this.S=o(i("dragend",!1,function(){if(O){O=!1;var e=Math.sqrt(C*C+T*T)/S;4<=e?h.ti(e,C,T):h.wf()}})),this.Ta=o(i("transformstart",!1,function(e,t){z=1,m=t.x,x=t.y}));var G=1,E=!1;this.Ja=o(i("transform",!1,function(e,t){var n=t.scale-.01;h.Qg(t,n/z,t.x-m,t.y-x),z=n,m=t.x,x=t.y,G=z,E=E||2<t.touches})),this.cb=o(i("transformend",!1,function(){E&&.8>G?e.c.p("interaction:reset"):r(),E=!1})),this.Pa=o(i("mousewheel",!1,function(){var e=H.ah(function(){r()},300);return function(t,n){var o=F.Gj;1!==o&&(o=Math.pow(o,n.vd),j?(h.Rg(n,o),e()):h.Yb(n,o,F.wc,R.pa(F.xc)).N(r))}}())),this.za=o(function(){var t,n=void 0,r={},o=!1,a=i("hover",!1,function(){n&&(n.Db=!1,n.I=!0),t&&(t.Db=!0,t.I=!0),g.update(t),e.c.p("foamtree:dirty",!1)}),u=i("mousemove",!1,void 0);return function(e){if("out"===e.type)t=void 0,o=t!==n;else if(h.absolute(e,r),n&&!n.open&&N(n,r)){var i=c(r,n.scale);i&&i!=n?(o=!0,t=i):o=!1}else t=c(r),o=t!==n;o&&(a(e),n=t,o=!1),n&&u(e)}}()),this.gb={click:t(this.d),doubleclick:t(this.k),hold:t(this.ya),mouseup:t(this.Ba),mousedown:t(this.Aa),dragstart:t(this.Y),drag:t(this.A),dragend:t(this.S),transformstart:t(this.Ta),transform:t(this.Ja),transformend:t(this.cb),hover:t(this.za),mousewheel:t(this.Pa)};var I=function(){function e(e,t){return function(n){n=n.gesture;var r=n.center,r=B.Je(f,r.pageX,r.pageY,{});r.scale=n.scale,r.wb=1<n.touches.length,r.touches=n.touches.length,e.call(f,r),(void 0===r.Lb&&t||"prevent"===r.Lb)&&n.preventDefault()}}var t,n={};return{H:function(e){t=window.Hammer(e,{doubletap_interval:350,hold_timeout:400,doubletap_distance:10})},m:function(r,o,i){n[r]=o,t.on(r,e(o,i))},kb:function(){t&&H.Ga(n,function(e,n){t.off(n,e)})}}}(),N=function(){var e={};return function(t,n){return t.Ub(n,e),t.aa&&V.Ta(t.aa,e)}}()}function y(e){function t(e,t,n,r){var o,i=0,a=[];for(o=0;o<t.length;o++){var u=Math.sqrt(V.d(t[o],t[(o+1)%t.length]));a.push(u),i+=u}for(o=0;o<a.length;o++)a[o]/=i;e[0].x=n.x,e[0].y=n.y;var l=u=i=0;for(o=1;o<e.length;o++){for(var c=e[o],s=.95*Math.pow(o/e.length,r),i=i+.3819;u<i;)u+=a[l],l=(l+1)%a.length;var f=(l-1+a.length)%a.length,h=1-(u-i)/a[f],p=t[f].x,f=t[f].y,d=t[l].x,b=t[l].y,p=(p-n.x)*s+n.x,f=(f-n.y)*s+n.y,d=(d-n.x)*s+n.x,b=(b-n.y)*s+n.y;c.x=p*(1-h)+d*h,c.y=f*(1-h)+b*h}}var n={random:{Eb:function(e,t){for(var n=0;n<e.length;n++){var r=e[n];r.x=t.x+Math.random()*t.f,r.y=t.y+Math.random()*t.i}},Zb:"box"},ordered:{Eb:function(e,t){var n=e.slice(0);r.lc&&n.sort(k),Q.Xb(n,t,!1,r.ce)},Zb:"box"},squarified:{Eb:function(e,t){var n=e.slice(0);r.lc&&n.sort(k),Q.ue(n,t,!1,r.ce)},Zb:"box"},fisheye:{Eb:function(e,n,o){e=e.slice(0),r.lc&&e.sort(k),t(e,n,o,.25)},Zb:"polygon"},blackhole:{Eb:function(e,n,o){e=e.slice(0),r.lc&&e.sort(k).reverse(),t(e,n,o,1)},Zb:"polygon"}};n.order=n.ordered,n.treemap=n.squarified;var r=e.options;this.d=function(e,t,o){if(0<e.length){if(o=n[o.relaxationInitializer||o.initializer||r.fj||"random"],"box"===o.Zb){var i=V.q(t,{});o.Eb(e,i),V.qe(e,V.A(i),t)}else o.Eb(e,t,V.k(t,{}));for(i=e.length-1;0<=i;i--){if(o=e[i],o.description){e=V.re(t,r.Hc,r.bh),o.x=e.x,o.y=e.y;break}if(o.na){e=V.re(t,r.we,r.Sg),o.x=e.x,o.y=e.y;break}}}}}function m(e){var t,n=e.options,r=new x(e,this),o=new w(e,this),i={relaxed:r,ordered:o,squarified:o},a=i[e.options.Vc]||r;this.Bg=5e-5,e.c.j("model:loaded",function(e){t=e}),e.c.j("options:changed",function(e){e.layout&&H.Q(i,n.Vc)&&(a=i[n.Vc])}),this.step=function(e,t,n,r){return a.step(e,t,n,r)},this.complete=function(e){a.complete(e)},this.lf=function(e){return e===t||2*Math.sqrt(e.K.ja/(Math.PI*e.e.length))>=Math.max(n.We,5e-5)},this.xd=function(e,t){for(var r=Math.pow(n.Ra,e.R),o=n.lb*r,r=n.zd*r,i=e.e,u=i.length-1;0<=u;u--){var l=i[u];a.xe(l,r);var c=l;c.aa=0<o?X.cb(c.o,o):c.o,c.aa&&(V.q(c.aa,c.q),V.se(c.aa,c.K)),l.e&&t.push(l)}},this.qc=function(e){a.qc(e)},this.Mb=function(e){a.Mb(e)}}function x(e,t){function n(e){if(e.e){e=e.e;for(var t=0;t<e.length;t++){var n=e[t];n.uc=n.rc*f.Rh}}}function r(e,r){t.lf(e)&&(e.u||(e.u=X.cb(e.o,f.zd*Math.pow(f.Ra,e.R-1)),e.u&&"flattened"==f.ab&&"stab"==f.dc&&u(e)),e.u&&(s.Mb(e),h.d(o(e),e.u,e.group),e.M=!0,r(e)),n(e))}function o(e){return"stab"==f.dc&&0<e.e.length&&e.e[0].description?e.e.slice(1):e.e}function i(e){var t=o(e);return J.S(t,e.u),J.zc(t,e.u),Y.Dg(e)*Math.sqrt(c.K.ja/e.K.ja)}function a(e){return e<f.bg||1e-4>e}function u(e){var t=f.cc/(1+f.cc),n=V.q(e.u,{}),r={x:n.x,y:0},o=n.y,i=n.i,a=f.De*Math.pow(f.Ra,e.R-1),u=i*f.Ce,l=f.Hc;"bottom"==l||0<=l&&180>l?(l=Math.PI,o+=i,i=-1):(l=0,i=1);for(var c,s=e.u,h=l,p=0,d=1,b=V.k(s,{}),v=b.ja,t=v*t,g=0;p<d&&20>g++;){var y=(p+d)/2;r.y=n.y+n.i*y,c=V.Wb(s,r,h),V.k(c[0],b);var m=b.ja-t;if(.01>=Math.abs(m)/v)break;0<(0==h?1:-1)*m?d=y:p=y}V.q(c[0],n),(n.i<a||n.i>u)&&(r.y=n.i<a?o+i*Math.min(a,u):o+i*u,c=V.Wb(e.u,r,l)),e.e[0].o=c[0],e.u=c[1]}function l(e){e!==c&&2*Math.sqrt(e.K.ja/(Math.PI*e.e.length))<Math.max(.85*f.We,t.Bg)&&(e.M=!1,e.Ca=!1,e.Qa=!0,e.u=null)}var c,s=this,f=e.options,h=new y(e),p=0;e.c.j("model:loaded",function(e){c=e,p=0}),this.step=function(e,n,u,s){for(var h=0,d=[c];0<d.length;)h=Math.max(h,function(n){if(n.M&&n.Ca?l(n):n.Qa&&n.o&&r(n,function(){var t=o(n);J.S(t,n.u),J.zc(t,n.u),e(n)}),!n.u||!n.M)return 0;var u;return n.parent&&n.parent.Z||n.La?(u=i(n),s&&s(n),n.La=!a(u),n.Z=!0):u=0,t.xd(n,d),u}(d.shift()));var b=a(h);return n&&function(e,t,n){p<e&&(p=e);var r=f.bg;f.Sd(t?1:1-(e-r)/(p-r||1),t,n),t&&(p=0)}(h,b,u),b},this.complete=function(e){for(var n=[c];0<n.length;){var o=n.shift();if(!o.M&&o.Qa&&o.o&&r(o,e),o.u){if(o.parent&&o.parent.Z||o.La){for(var u=1e-4>o.K.ja,l=0;!(a(i(o))||u&&32<l++););o.Z=!0,o.La=!1}t.xd(o,n)}}},this.qc=function(e){q.F(e,n)},this.xe=function(e,t){if(e.M){var n=e.u;n&&(e.Xd=n),e.u=X.cb(e.o,t),e.u&&"flattened"==f.ab&&"stab"==f.dc&&u(e),n&&!e.u&&(e.Z=!0),e.u&&e.Xd&&V.qe(o(e),e.Xd,e.u)}},this.Mb=function(e){for(var t,n=o(e),r=e.ja,i=t=0;i<n.length;i++)t+=n[i].T;for(e.$j=t,e=0;e<n.length;e++)i=n[e],i.qg=i.f,i.rc=r/Math.PI*(0<t?i.T/t:1/n.length)}}function w(e,t){function n(e,n){if(t.lf(e)){if(!e.u||e.parent&&e.parent.Z){var r=u.zd*Math.pow(u.Ra,e.R-1);e.u=V.A(o(V.q(e.o,{}),r))}e.u&&(e.M=!0,n(e))}else e.M=!1,q.Fa(e,function(e){e.u=null})}function r(e){var t;"stab"==u.dc&&0<e.e.length&&e.e[0].description?(t=e.e.slice(1),function(e){function t(){r.o=V.A(o),r.x=o.x+o.f/2,r.y=o.y+o.i/2}var n=u.cc/(1+u.cc),r=e.e[0],o=V.q(e.u,{}),i=o.i,n=Math.min(Math.max(i*n,u.De*Math.pow(u.Ra,e.R-1)),i*u.Ce),a=u.Hc;"bottom"==a||0<=a&&180>a?(o.i=i-n,e.u=V.A(o),o.y+=i-n,o.i=n,t()):(o.i=n,t(),o.y+=n,o.i=i-n,e.u=V.A(o))}(e)):t=e.e,u.lc&&t.sort(k),"floating"==u.dc&&i(t,u.Hc,function(e){return e.description}),i(t,u.we,function(e){return e.na});var n=V.q(e.u,{});(l[u.Vc]||Q.Xb)(t,n,!0,u.ce),e.La=!1,e.Z=!0,e.I=!0,e.Ma=!0}function o(e,t){var n=2*t;return e.x+=t,e.y+=t,e.f-=n,e.i-=n,e}function i(e,t,n){for(var r=0;r<e.length;r++){var o=e[r];if(n(o)){e.splice(r,1),"topleft"==t||135<=t&&315>t?e.unshift(o):e.push(o);break}}}var a,u=e.options,l={squarified:Q.ue,ordered:Q.Xb};e.c.j("model:loaded",function(e){a=e}),this.step=function(e,t,n){return this.complete(e),t&&u.Sd(1,!0,n),!0},this.complete=function(e){for(var o=[a];0<o.length;){var i=o.shift();(!i.M||i.parent&&i.parent.Z)&&i.Qa&&i.o&&n(i,e),i.u&&((i.parent&&i.parent.Z||i.La)&&r(i),t.xd(i,o))}},this.Mb=this.qc=this.xe=H.ta}function C(e,t){function n(e,t){var n=e.K.Nb,r=n/15,o=.5*n/15,n=n/5,i=e.K.x,a=e.K.y;t.fillRect(i-o,a-o,r,r),t.fillRect(i-o-n,a-o,r,r),t.fillRect(i-o+n,a-o,r,r)}function r(e,t,n,r){null===e&&n.clearRect(0,0,z,k);var o,i=Array(ie.length);for(o=ie.length-1;0<=o;o--)i[o]=ie[o].qa(n,r);for(o=ie.length-1;0<=o;o--)i[o]&&ie[o].W(n,r);for(L.Ic([n,C],function(r){var o;if(null!==e){for(n.save(),n.globalCompositeOperation="destination-out",n.fillStyle=n.strokeStyle="rgba(255, 255, 255, 1)",o=e.length-1;0<=o;o--){var a=e[o],u=a.o;u&&(n.save(),n.beginPath(),a.Sb(n),E.le(n,u),n.fill(),a=Y.lb*Math.pow(Y.Ra,a.R-1),0<a&&(n.lineWidth=a/2,n.stroke()),n.restore())}n.restore()}if(r=r.scale,0!==t.length){for(o={},u=ie.length-1;0<=u;u--)ie[u].Og(o);for(a=$.length-1;0<=a;a--)if(u=$[a],o[u.id])for(var l=u.be,u=0;u<t.length;u++){var c=t[u];!c.parent||c.parent.Ca&&c.parent.M?l(c,r):c.ba.clear()}}for(o=ie.length-1;0<=o;o--)a=ie[o],i[o]&&a.ee(t,n,r)}),o=ie.length-1;0<=o;o--)i[o]&&ie[o].Da(n);Y.qd&&(n.canvas.style.opacity=.99,setTimeout(function(){n.canvas.style.opacity=1},1))}function o(e){d===v?e<.9*K&&(d=b,y=m,l()):e>=K&&(d=v,y=x,l())}function i(){function e(t,n,r){t.Bb=Math.floor(1e3*t.scale)-r*n,0<t.opacity&&!t.open&&n++;var o=t.e;if(o)for(var i=o.length-1;0<=i;i--)t.$&&e(o[i],n,r)}var t=null,n=null,r=null;return L.Ic([],function(i){o(i.scale);var u=!1;q.F(F,function(e){e.$&&(u=e.Md()||u,e.nc(),e.Wa=X.d(e)||e.Wa)}),u&&(F.I=!0);var l="onSurfaceDirty"===Y.oh;q.wd(F,function(e){e.parent&&e.parent.Z&&(e.ba.clear(),e.Wa=!0,l||(e.Ec=!0,e.ac.clear())),l&&(e.Ec=!0,e.ac.clear())});var c=i.scale*i.scale;if(q.wd(F,function(e){if(e.M){for(var t=e.e,n=0;n<t.length;n++)if(5<t[n].K.ja*c)return void(e.X=!0);e.X=!1}}),f(i),r=[],q.Kc(F,function(e){if(e.parent.X&&e.ea&&e.$){r.push(e);for(var t=e.parent;t!==F&&(t.open||0===t.opacity);)t=t.parent;t!==F&&.02>Math.abs(t.scale-e.scale)&&(e.scale=Math.min(e.scale,t.scale))}}),e(F,0,"flattened"==Y.ab?-1:1),r.sort(function(e,t){return e.Bb-t.Bb}),a())t=r,n=null;else{var s={},h={},p="none"!=Y.Bd&&Y.lb<Y.mb/2,d=Y.lb<Y.Qc/2+Y.Ad*Y.Ye.a;q.F(F,function(e){if(e.$&&!e.description&&(e.Z||e.I||e.Xc&&e.parent.X&&e.Wa)){var t,n,r=[e],o=e.C||e.parent.e;if(p)for(t=0;t<o.length;t++)(n=o[t])&&r.push(n);else if(d)if(!e.selected&&e.$a){for(n=!0,t=0;t<o.length;t++)o[t]?r.push(o[t]):n=!1;!n&&1<e.R&&r.push(e.parent)}else for(t=0;t<o.length;t++)(n=o[t])&&n.selected&&r.push(n);var i;for(t=e.parent;t!=F;)t.selected&&(i=t),t=t.parent;for(i&&r.push(i),t=0;t<r.length;t++){for(i=r[t],e=i.parent;e&&e!==F;)0<e.opacity&&(i=e),e=e.parent;h[i.id]=!0,q.Fa(i,function(e){s[e.id]=!0})}}}),t=r.filter(function(e){return s[e.id]}),n=t.filter(function(e){return h[e.id]})}}),function(){var e=!1;Y.ag&&q.F(F,function(t){if(t.$&&0!==t.sa.a&&1!==t.sa.a)return e=!0,!1}),e?(q.Jc(F,function(e){if(e.$&&(e.opacity!==e.ad||e.Ma)){var t=e.e;if(t){for(var n=0,r=t.length-1;0<=r;r--)n=Math.max(n,t[r].Wc);e.Wc=n+e.opacity*e.sa.a}else e.Wc=e.opacity*e.sa.a}}),q.F(F,function(e){if(e.$&&(e.opacity!==e.ad||e.Ma)){for(var t=e.Wc,n=e;(n=n.parent)&&n!==F;)t+=n.opacity*n.sa.a*Y.Zf;e.rd=0<t?1-Math.pow(1-e.sa.a,1/t):0,e.ad=e.opacity}})):q.F(F,function(e){e.$&&(e.rd=1,e.ad=-1)})}(),{wg:t,vg:n,ea:r}}function a(){var e=F.Z||F.I||"none"==Y.ff;if(!e&&!F.empty()){var t=F.e[0].scale;q.F(F,function(n){if(n.$&&n.ea&&n.scale!==t)return e=!0,!1})}return!e&&0<Y.Se&&1!=Y.Va&&q.F(F,function(t){if(t.$&&0<t.ka)return e=!0,!1}),"accurate"==Y.ff&&!(e=(e=e||0===Y.lb)||"none"!=Y.Bd&&Y.lb<Y.mb/2)&&Y.lb<Y.Qc/2+Y.Ad*Y.Ye.a&&q.F(F,function(t){if(t.$&&(t.selected&&!t.$a||!t.selected&&t.$a))return e=!0,!1}),e}function u(){if(Y.n!==Y.xb)return!0;var e="polygonPlainFill polygonPlainStroke polygonGradientFill polygonGradientStroke labelPlainFill contentDecoration".split(" ");q.F(F,function(t){if(t.$&&t.U)return e.push("polygonExposureShadow"),!1});for(var t=e.length-1;0<=t;t--){var n=e[t];if(!!B[n]!=!!U[n])return!0}return!1}function l(){function e(e,n,r,o,i){function a(e,t,n,r,o){return e[r]&&(t-=n*h[r],e[r]=!1,o&&(t+=n*h[o],e[o]=!0)),t}switch(e=H.extend({},e),r){case"never":e.labelPlainFill=!1;break;case"always":case"auto":e.labelPlainFill=!0}if(Y.Oc)switch(o){case"never":e.contentDecoration=!1;break;case"always":case"auto":e.contentDecoration=!0}else e.contentDecoration=!1;var u=0;return H.Ga(e,function(e,t){e&&(u+=n*h["contentDecoration"===t?"labelPlainFill":t])}),e.polygonExposureShadow=t,(u+=2*h.polygonExposureShadow)<=i||(u=a(e,u,2,"polygonExposureShadow"))<=i||(u=a(e,u,n,"polygonGradientFill","polygonPlainFill"))<=i||(u=a(e,u,n,"polygonGradientStroke"))<=i||(u=a(e,u,n,"polygonPlainStroke"))<=i||"auto"===o&&(u=a(e,u,n,"contentDecoration"))<=i?e:("auto"===r&&(u=a(e,u,n,"labelPlainFill")),e)}var t=d===b,n=0,r=0;q.Ie(F,function(e){var t=1;q.F(e,function(){t++}),n+=t,r=Math.max(r,t)});var o={};switch(Y.xh){case"plain":o.polygonPlainFill=!0;break;case"gradient":o.polygonPlainFill=!t,o.polygonGradientFill=t}switch(Y.Bd){case"plain":o.polygonPlainStroke=!0;break;case"gradient":o.polygonPlainStroke=!t,o.polygonGradientStroke=t}B=e(o,n,Y.Ej,Y.Cj,Y.Dj),U=e(o,2*r,"always","always",Y.hh),W=e(o,n,"always","always",Y.gh)}function c(e){return function(t,n){return t===d?!0===B[e]:!0===(n?U:W)[e]}}function s(e,t){return function(n,r){return e(n,r)&&t(n,r)}}function f(e){F.ea=!0,q.wd(F,function(t){if(t.$&&t.X&&t.Ca&&t.M&&(F.I||t.Z||t.me)){t.me=!1;var n=t.e,r={x:0,y:0,f:0,i:0},o=!!t.u;if(1<z/e.f){var i;for(i=n.length-1;0<=i;i--)n[i].ea=!1;if(t.ea&&o)for(i=n.length-1;0<=i;i--)if(t=n[i],1!==t.scale&&(t.Ub(e,r),r.f=e.f/t.scale,r.i=e.i/t.scale),!1===t.ea&&t.o){var o=t.o,a=o.length;if(V.Ta(t.o,1===t.scale?e:r))t.ea=!0;else for(var u=0;u<a;u++)if(V.Mg(o[u],o[(u+1)%a],1===t.scale?e:r)){t.ea=!0,t.C&&(t=t.C[u])&&(n[t.index].ea=!0);break}}}else for(i=0;i<n.length;i++)n[i].ea=o}})}var h,p,d,b,v,g,y,m,x,w,C,z,k,M,L,_,P,F,O,B,W,U,K=D.pf()?50:1e4,X=new T(e),J=new S(e),Y=e.options;e.c.j("stage:initialized",function(e,t,n,r){M=e,z=n,k=r,p=M.oc("wireframe",Y.xb,!1),b=p.getContext("2d"),v=new A(b),g=M.oc("hifi",Y.n,!1),m=g.getContext("2d"),x=new A(m),d=b,y=m,b.n=Y.xb,v.n=Y.xb,m.n=Y.n,x.n=Y.n,w=M.oc("tmp",Math.max(Y.n,Y.xb),!0),C=w.getContext("2d"),C.n=1,[b,m,C].forEach(function(e){e.scale(e.n,e.n)})}),e.c.j("stage:resized",function(e,t,n,r){z=n,k=r,[b,m,C].forEach(function(e){e.scale(e.n,e.n)})}),e.c.j("model:loaded",function(t){function n(e){var t=0;if(!e.empty()){for(var r=e.e,o=r.length-1;0<=o;o--)t=Math.max(t,n(r[o]));t+=1}return e.ng=t}F=t,O=!0,n(F),l(),e.c.p("render:renderers:resolved",B,U,W)});var Z="groupFillType groupStrokeType wireframeDrawMaxDuration wireframeLabelDrawing wireframeContentDecorationDrawing finalCompleteDrawMaxDuration finalIncrementalDrawMaxDuration groupContentDecorator".split(" "),Q=["groupLabelLightColor","groupLabelDarkColor","groupLabelColorThreshold","groupUnexposureLabelColorThreshold"];e.c.j("options:changed",function(e){function t(e,t,n,r){M.ej(e,n),t.n=n,r&&t.scale(n,n)}e.dataObject||(H.nb(e,Z)&&l(),H.nb(e,Q)&&q.F(F,function(e){e.yd=-1}));var n=H.Q(e,"pixelRatio");e=H.Q(e,"wireframePixelRatio"),(n||e)&&(n&&t(g,y,Y.n,!0),e&&t(p,d,Y.xb,!0),t(w,C,Math.max(Y.n,Y.xb),!1))}),e.c.j("zoom:initialized",function(e){L=e}),e.c.j("timeline:initialized",function(e){_=e}),e.c.j("api:initialized",function(e){P=e});var $=[{id:"offsetPolygon",be:function(e){if((e.selected||0<e.opacity&&!1===e.open||!e.X)&&e.ba.Na()){var t=e.ba;if(t.clear(),e.aa){var n=e.aa,r=Y.jh;0<r?(r=Math.min(1,r*Math.pow(1-Y.kh*r,e.ng)),E.qj(t,n,e.parent.K.Nb/32,r)):E.le(t,n)}e.Vd=!0}}},{id:"label",be:function(e){e.Wa&&e.Xc&&X.k(e)}},{id:"custom",be:function(t,n){if(t.aa&&(0<t.opacity&&(!1===t.open||!0===t.selected)||!t.X)&&t.Ec&&e.options.Oc&&!t.na){var r={};P.od(r,t),P.pd(r,t),P.nd(r,t,!0),r.context=t.ac,r.polygonContext=t.ba,r.labelContext=t.Tc,r.shapeDirty=t.Vd,r.viewportScale=n;var o={groupLabelDrawn:!0,groupPolygonDrawn:!0};e.options.nh(e.Ud,r,o),o.groupLabelDrawn||(t.qf=!1),o.groupPolygonDrawn||(t.Wd=!1),t.Vd=!1,t.Ec=!1}}}].reverse(),ie=[new function(e){var t=Array(e.length);this.ee=function(n,r,o){if(0!==n.length){var i,a,u=[],l=n[0].Bb;for(i=0;i<n.length;i++)a=n[i],a.Bb!==l&&(u.push(i),l=a.Bb);u.push(i);for(var c=l=0;c<u.length;c++){for(var s=u[c],f=e.length-1;0<=f;f--)if(t[f]){var h=e[f];for(r.save(),i=l;i<s;i++)a=n[i],r.save(),a.Sb(r),h.vb.call(h,a,r,o),r.restore();h.hb.call(h,r,o),r.restore()}l=s}}},this.qa=function(n,r){for(var o=!1,i=e.length-1;0<=i;i--)t[i]=e[i].qa(n,r),o|=t[i];return o},this.W=function(n,r){for(var o=e.length-1;0<=o;o--)if(t[o]){var i=e[o];i.W.call(i,n,r)}},this.Da=function(n){for(var r=e.length-1;0<=r;r--)if(t[r]){var o=e[r];o.Da.call(o,n)}},this.Og=function(n){for(var r=e.length-1;0<=r;r--){var o=e[r];if(t[r])for(var i=o.Za.length-1;0<=i;i--)n[o.Za[i]]=!0}}}([{Za:["offsetPolygon"],qa:c("polygonExposureShadow"),W:function(e){C.save(),C.scale(e.n,e.n)},Da:function(){C.restore()},d:function(){},hb:function(e){this.mg&&(this.mg=!1,e.save(),e.setTransform(1,0,0,1,0,0),e.drawImage(w,0,0,e.canvas.width,e.canvas.height,0,0,e.canvas.width,e.canvas.height),e.restore(),C.save(),C.setTransform(1,0,0,1,0,0),C.clearRect(0,0,w.width,w.height),C.restore())},vb:function(e,t,n){if(!(e.open&&e.X||e.ba.Na())){var r=Y.Se*e.opacity*e.ka*("flattened"==Y.ab?1-e.parent.ka:(1-e.Kb)*e.parent.Kb)*(1.1<=Y.Va?1:(Y.Va-1)/.1);0<r&&(C.save(),C.beginPath(),e.Sb(C),e.ba.Sa(C),C.shadowBlur=n*t.n*r,C.shadowColor=Y.ph,C.fillStyle="rgba(0, 0, 0, 1)",C.globalCompositeOperation="source-over",C.globalAlpha=e.opacity,C.fill(),C.shadowBlur=0,C.shadowColor="transparent",C.globalCompositeOperation="destination-out",C.fill(),C.restore(),this.mg=!0)}}},{Za:["offsetPolygon"],qa:function(){return!0},W:function(){function e(e){var n=e.sa,r=e.Db,o=e.selected,i=t(n.l*e.va+(r?Y.Ch:0)+(o?Y.Th:0)),a=t(n.s*e.wa+(r?Y.Dh:0)+(o?Y.Uh:0));return e=e.Re,e.h=(n.h+(r?Y.Bh:0)+(o?Y.Sh:0))%360,e.s=a,e.l=i,e}function t(e){return 100<e?100:0>e?0:e}var n=[{type:"fill",qa:c("polygonPlainFill"),gd:function(t,n){n.fillStyle=I.Ac(e(t))}},{type:"fill",qa:c("polygonGradientFill"),gd:function(n,r){var o=n.K.Nb,i=e(n),o=r.createRadialGradient(n.x,n.y,0,n.x,n.y,o*Y.th);o.addColorStop(0,I.Y((i.h+Y.qh)%360,t(i.s+Y.sh),t(i.l+Y.rh))),o.addColorStop(1,I.Y((i.h+Y.uh)%360,t(i.s+Y.wh),t(i.l+Y.vh))),n.ba.Sa(r),r.fillStyle=o}},{type:"stroke",qa:s(c("polygonPlainStroke"),function(){return 0<Y.mb}),gd:function(e,n){var r=e.sa,o=e.Db,i=e.selected;n.strokeStyle=I.Y((r.h+Y.bf+(o?Y.Te:0)+(i?Y.Ze:0))%360,t(r.s*e.wa+Y.df+(o?Y.Ve:0)+(i?Y.af:0)),t(r.l*e.va+Y.cf+(o?Y.Ue:0)+(i?Y.$e:0))),n.lineWidth=Y.mb*Math.pow(Y.Ra,e.R-1)}},{type:"stroke",qa:s(c("polygonGradientStroke"),function(){return 0<Y.mb}),gd:function(e,n){var r=e.K.Nb*Y.$h,o=e.sa,i=Math.PI*Y.Wh/180,r=n.createLinearGradient(e.x+r*Math.cos(i),e.y+r*Math.sin(i),e.x+r*Math.cos(i+Math.PI),e.y+r*Math.sin(i+Math.PI)),a=e.Db,u=e.selected,i=(o.h+Y.bf+(a?Y.Te:0)+(u?Y.Ze:0))%360,l=t(o.s*e.wa+Y.df+(a?Y.Ve:0)+(u?Y.af:0)),o=t(o.l*e.va+Y.cf+(a?Y.Ue:0)+(u?Y.$e:0));r.addColorStop(0,I.Y((i+Y.Xh)%360,t(l+Y.Zh),t(o+Y.Yh))),r.addColorStop(1,I.Y((i+Y.ai)%360,t(l+Y.ci),t(o+Y.bi))),n.strokeStyle=r,n.lineWidth=Y.mb*Math.pow(Y.Ra,e.R-1)}}],r=Array(n.length);return function(e,t){for(var o=n.length-1;0<=o;o--)r[o]=n[o].qa(e,t);this.uj=n,this.Xg=r}}(),Da:function(){},d:function(){},hb:function(){},vb:function(e,t){if(e.Wd&&!((0===e.opacity||e.open)&&e.X||e.ba.Na()||!Y.Ee&&e.description)){var n=this.uj,r=this.Xg;t.beginPath(),e.ba.Sa(t);for(var o=!1,i=!1,a=n.length-1;0<=a;a--){var u=n[a];if(r[a])switch(u.gd(e,t),u.type){case"fill":o=!0;break;case"stroke":i=!0}}n=(e.X?e.opacity:1)*e.sa.a,r=!e.empty(),a=Y.ag?e.rd:1,o&&(o=r&&e.X&&e.M&&e.e[0].$?1-e.e.reduce(function(e,t){return e+t.ua*t.Zd},0)/e.e.length*(1-Y.Zf):1,t.globalAlpha=n*o*a,te(t)),i&&(t.globalAlpha=n*(r?Y.vi:1)*a,t.closePath(),ne(t),t.stroke())}}},{Za:["offsetPolygon"],qa:function(){return 0<Y.Qc},W:function(){},Da:function(){},d:function(){},hb:function(){},vb:function(e,t,n){if(e.Wd&&e.selected&&!e.ba.Na()){t.globalAlpha=e.Ka,t.beginPath();var r=Math.pow(Y.Ra,e.R-1);t.lineWidth=Y.Qc*r,t.strokeStyle=Y.Vh;var o=Y.Ad;0<o&&(t.shadowBlur=o*r*n*t.n,t.shadowColor=Y.Xe),e.ba.Sa(t),t.closePath(),t.stroke()}}},{Za:[],qa:function(){return!0},W:function(){},Da:function(){},d:function(){},hb:function(){},vb:function(e,t){e.na&&!e.ba.Na()&&function(n){var r=ee.yc.width,o=ee.yc.height,i=V.te(e.aa,e.K,r/o),i=Math.min(Math.min(.9*i,.5*e.q.i)/o,.5*e.q.f/r);t.save(),t.translate(e.x,e.y),t.globalAlpha=e.opacity*e.fa,t.scale(i,i),t.translate(-r/2,-o/2),n(t),t.restore()}(function(e){ee.Gg(e)})}},{Za:[],qa:function(e,t){return function(n,r){return e(n,r)||t(n,r)}}(c("labelPlainFill"),s(c("contentDecoration"),function(){return Y.Oc})),W:function(){},Da:function(){},d:function(){},hb:function(){},vb:function(e,t,n){(0<e.opacity&&0<e.fa&&!e.open||!e.X)&&!e.ba.Na()&&(e.Uc=e.ra&&e.ra.la&&Y.n*e.ra.fontSize*e.scale*n>=Y.Ph,!Y.Ee&&e.description?e.qb=e.parent.qb:"auto"===e.Gd?(t=e.Re,n=t.h+(t.s<<9)+(t.l<<16),e.yd!==n&&(e.qb=I.Cg(t)>(0>e.ka?Y.di:Y.Eh)?Y.Fh:Y.Oh,e.yd=n)):e.qb=e.Gd)}},{Za:["custom"],qa:s(c("contentDecoration"),function(){return Y.Oc}),W:function(){},Da:function(){},d:function(){},hb:function(){},vb:function(e,t){!(0<e.opacity&&0<e.fa&&!e.open||!e.X)||e.ac.Na()||e.ba.Na()||(e.Uc||void 0===e.ra?(t.globalAlpha=e.fa*(e.X?e.opacity:1)*(e.empty()?1:Y.$f),t.fillStyle=e.qb,t.strokeStyle=e.qb,e.ac.Sa(t)):n(e,t))}},{Za:["label"],qa:c("labelPlainFill"),W:function(){},Da:function(){},d:function(){},hb:function(){},vb:function(e,t,r){e.qf&&e.Xc&&(0<e.opacity&&0<e.fa&&!e.open||!e.X)&&!e.ba.Na()&&e.ra&&(t.fillStyle=e.qb,t.globalAlpha=e.fa*(e.X?e.opacity:1)*(e.empty()?1:Y.$f),e.Uc?oe(e,t,r):n(e,t))}}].reverse())];this.H=function(){h=N.ji(function(){return G.eh()},"CarrotSearchFoamTree",12096e5)(j()),J.H()},this.clear=function(){d.clearRect(0,0,z,k),y.clearRect(0,0,z,k)},this.ee=function(){function e(){window.clearTimeout(n),n=setTimeout(function(){if(u()){var e=!a();r(null,o.ea,y,e),H.defer(function(){ae.rj()})}},Math.max(Y.Fj,3*t.rg.Jd,3*t.rg.Id))}var n,o;return function(t){re(J),o=i();var n=null!==o.vg,a=0<M.kc("hifi"),u=a&&(n||!t);t=n||O||!t,O=!1,a&&!u&&ae.sj(),r(o.vg,o.wg,u?y:d,t),q.Fa(F,function(e){e.Z=!1,e.I=!1,e.$a=!1}),u||e(),Y.Vf(n)}}(),this.d=function(e){e=e||{},re(J),F.I=!0;var t=i(),n=Y.n;try{var o=H.B(e.pixelRatio,Y.n);Y.n=o;var a=M.oc("export",o,!0),u=a.getContext("2d");d===v&&(u=new A(u)),u.scale(o,o);var l=H.Q(e,"backgroundColor");l&&(u.save(),u.fillStyle=e.backgroundColor,u.fillRect(0,0,z,k),u.restore()),r(l?[]:null,t.wg,u,!0)}finally{Y.n=n}return a.toDataURL(H.B(e.format,"image/png"),H.B(e.quality,.8))};var ae=function(){function e(e,t,r,o){function i(e,t,n,r){return _.D.m({opacity:M.kc(e)}).ia({duration:n,G:{opacity:{end:t,P:r}},ca:function(){M.kc(e,this.opacity)}}).xa()}var a=H.Ed(M.kc(e),1),u=H.Ed(M.kc(r),0);if(!a||!u){for(var l=n.length-1;0<=l;l--)n[l].stop();return n=[],a||n.push(i(e,1,t,R.Qb)),u||n.push(i(r,0,o,R.og)),_.D.m({}).Ya(n).start()}}var t,n=[];return{sj:function(){Y.qd?1!==p.style.opacity&&(p.style.visibility="visible",g.style.visibility="hidden",p.style.opacity=1,g.style.opacity=0):t&&t.Fb()||(t=e("wireframe",Y.Ne,"hifi",Y.Ne))},rj:function(){Y.qd?(g.style.visibility="visible",p.style.visibility="hidden",p.style.opacity=0,g.style.opacity=1):e("hifi",Y.yg,"wireframe",Y.yg)}}}();return re=function(e){e.apply()},te=function(e){e.fill()},ne=function(e){e.stroke()},this}function T(e){function t(e){return o.Nh?(l.fontFamily=i.fontFamily,l.fontStyle=i.fontStyle,l.fontVariant=i.fontVariant,l.fontWeight=i.fontWeight,l.lineHeight=i.lineHeight,l.horizontalPadding=i.ob,l.verticalPadding=i.eb,l.maxTotalTextHeight=i.sb,l.maxFontSize=i.rb,r.Cc(o.Mh,e,l),a.fontFamily=l.fontFamily,a.fontStyle=l.fontStyle,a.fontVariant=l.fontVariant,a.fontWeight=l.fontWeight,a.lineHeight=l.lineHeight,a.ob=l.horizontalPadding,a.eb=l.verticalPadding,a.sb=l.maxTotalTextHeight,a.rb=l.maxFontSize,a):i}function n(e){void 0!==e.groupLabelFontFamily&&(i.fontFamily=e.groupLabelFontFamily),void 0!==e.groupLabelFontStyle&&(i.fontStyle=e.groupLabelFontStyle),void 0!==e.groupLabelFontVariant&&(i.fontVariant=e.groupLabelFontVariant),void 0!==e.groupLabelFontWeight&&(i.fontWeight=e.groupLabelFontWeight),void 0!==e.groupLabelLineHeight&&(i.lineHeight=e.groupLabelLineHeight),void 0!==e.groupLabelHorizontalPadding&&(i.ob=e.groupLabelHorizontalPadding),void 0!==e.groupLabelVerticalPadding&&(i.eb=e.groupLabelVerticalPadding),void 0!==e.groupLabelMaxTotalHeight&&(i.sb=e.groupLabelMaxTotalHeight),void 0!==e.groupLabelMaxFontSize&&(i.rb=e.groupLabelMaxFontSize)}var r,o=e.options,i={},a={},u={groupLabel:""},l={};e.c.j("api:initialized",function(e){r=e}),e.c.j("options:changed",n),n(e.Ud),this.d=function(e){if(!e.aa)return!1;var t=e.group.label;return o.Hh&&!e.na&&(u.labelText=t,r.Cc(o.Gh,e,u),t=u.labelText),e.rf=t,e.Hd!=t},this.k=function(e){var n=e.rf;e.Hd=n,e.Tc.clear(),e.ra=void 0,!e.aa||H.kf(n)||"flattened"==o.ab&&!e.empty()&&e.M||(e.ra=Z.ye(t(e),e.Tc,n,e.aa,e.q,e.K,!1,!1,e.li,e.K.ja,o.Qh,e.Wa)),e.Wa=!1},oe=this.A=function(e,t){e.Tc.Sa(t)}}function S(e){function t(e,t){var n,r,o=e.e,a=o.length,u=i.K.Nb;for(n=0;n<a;n++)r=o[n],r.Cb=(180*(Math.atan2(r.x-e.x,r.y-e.y)+t)/Math.PI+180)/360,r.Nc=Math.min(1,Math.sqrt(V.d(r,e))/u)}function n(e,t){var n=e.e,r=n.length;if(1===r||2===r&&n[0].description)n[0].Cb=.5;else{var o,i,a=0,u=Number.MAX_VALUE,l=Math.sin(t),c=Math.cos(t);for(o=0;o<r;o++){i=n[o];var s=i.x*l+i.y*c;a<s&&(a=s),u>s&&(u=s),i.Cb=s,i.Nc=1}for(o=0;o<r;o++)i=n[o],i.Cb=(i.Cb-u)/(a-u)}}function r(e,t,n,r){return(t=t[r])+(n[r]-t)*e}var o,i,a={radial:t,linear:n},u=e.options,l={groupColor:null,labelColor:null};return e.c.j("model:loaded",function(e){i=e}),e.c.j("api:initialized",function(e){o=e}),this.H=function(){},this.apply=function(){function e(t){if(t.M&&t.Ca){var n,i,a=t.e;if(t.Z||t.Ma||b){for(0===t.R?s(t,u.Ui*Math.PI/180):f(t,u.Yi*Math.PI/180),n=a.length-1;0<=n;n--){i=a[n],i.Ma=!0;var y,m,x,w,C=i.Cb,T=i.Qe;0===t.R?(y=r(C,h,p,"h"),m=(g+(1-g)*i.Nc)*r(C,h,p,"s"),x=(1+(0>i.ka?v*(i.ka+1):v)*(1-i.Nc))*r(C,h,p,"l"),w=r(C,h,p,"a")):(x=t.sa,y=x.h,m=x.s,x=c(x.l,C,u.Zi,u.$i),w=t.Qe.a),T.h=y,T.s=m,T.l=x,T.a=w,y=i.sa,i.na?(y.h=0,y.s=0,y.l="light"==u.Tg?90:10,y.a=1):(y.h=T.h,y.s=T.s,y.l=T.l,y.a=T.a),b&&!i.na&&(l.groupColor=y,l.labelColor="auto",o.Cc(d,i,l,function(e){e.ratio=C}),i.sa=I.Ba(l.groupColor),i.sa.a=H.Q(l.groupColor,"a")?l.groupColor.a:1,"auto"!==l.labelColor&&(i.Gd=I.Ng(l.labelColor)))}t.Ma=!1}for(n=a.length-1;0<=n;n--)e(a[n])}}function c(e,t,n,r){var o=0>e+n*r?0:100<e+n*r?100:e+n*r;return o+t*((0>e-n*(1-r)?0:100<e-n*(1-r)?100:e-n*(1-r))-o)}var s=a[u.Ti]||t,f=n,h=u.cj,p=u.Wi,d=u.lh,b=u.mh,v=u.Xi,g=u.aj;e(i)},this}function z(){this.uc=this.pe=this.rc=this.qg=this.f=this.xg=this.T=this.y=this.x=this.id=0,this.o=this.parent=this.e=null,this.q={x:0,y:0,f:0,i:0},this.C=null,this.Hd=this.rf=void 0,this.kd=!1,this.Nc=this.Cb=0,this.Qe={h:0,s:0,l:0,a:0,model:"hsla"},this.sa={h:0,s:0,l:0,a:0,model:"hsla"},this.Re={h:0,s:0,l:0,model:"hsl"},this.yd=-1,this.Gd="auto",this.qb="#000",this.ng=this.R=this.Dd=this.index=0,this.na=!1,this.ja=this.vf=0,this.ea=!1,this.aa=null,this.K={x:0,y:0,ja:0,Nb:0},this.Xd=this.u=null,this.Xc=this.$=this.$a=this.Ec=this.me=this.Vd=this.Wa=this.Ma=this.I=this.Z=this.La=this.Ca=this.M=this.Qa=!1,this.wa=this.va=this.Ka=this.fa=this.opacity=this.scale=1,this.ua=0,this.Zd=1,this.Kb=this.ka=this.Gb=0,this.description=this.selected=this.Db=this.Td=this.open=this.U=!1,this.Bb=0,this.qf=this.Wd=this.X=!0,this.ra=void 0,this.Uc=!1,this.Tc=new O,this.ba=new O,this.ac=new O,this.li=Z.xi(),this.Wc=0,this.rd=1,this.ad=-1,this.empty=function(){return!this.e||0===this.e.length};var e=[];this.Bc=function(t){e.push(t)},this.ed=function(t){H.cg(e,t)};var t={scale:1};this.Md=function(){var n=!1;this.scale=1;for(var r=0;r<e.length;r++)n=e[r].sf(this,t)||n,this.scale*=t.scale;return n},this.Sb=function(t){for(var n=0;n<e.length;n++)e[n].Sb(this,t)},this.Tb=function(t,n){n.x=t.x,n.y=t.y;for(var r=0;r<e.length;r++)e[r].Tb(this,n,n);return n},this.Ub=function(t,n){n.x=t.x,n.y=t.y;for(var r=0;r<e.length;r++)e[r].Ub(this,n,n);return n};var n=[];this.zb=function(e){n.push(e)},this.dd=function(e){H.cg(n,e)};var r={opacity:1,wa:1,va:1,fa:1,Ka:1};this.nc=function(){if(0!==n.length){this.Ka=this.fa=this.va=this.wa=this.opacity=1;for(var e=n.length-1;0<=e;e--)(0,n[e])(this,r),this.opacity*=r.opacity,this.va*=r.va,this.wa*=r.wa,this.fa*=r.fa,this.Ka*=r.Ka}}}function k(e,t){return t.T>e.T?1:t.T<e.T?-1:e.index-t.index}function M(e){var t,n,r,o,i,a,u=this,l=e.options;e.c.j("stage:initialized",function(i,a,c,s){r=c,o=s,t=i.oc("titlebar",l.n,!1),n=t.getContext("2d"),n.n=l.n,n.scale(n.n,n.n),e.c.p("titlebar:initialized",u)}),e.c.j("stage:resized",function(e,t,i,a){r=i,o=a,n.scale(n.n,n.n)}),e.c.j("zoom:initialized",function(e){a=e}),e.c.j("api:initialized",function(e){i=e}),e.c.j("model:loaded",function(){n.clearRect(0,0,r,o)}),this.update=function(e){if(n.clearRect(0,0,r,o),e){!e.empty()&&e.e[0].description&&(e=e.e[0]);var t=l.zj,u=l.yj,c=Math.min(o/2,l.ne+2*t),s=c-2*t,f=r-2*u;if(!(0>=s||0>=f)){var h,p=e.Uc?e.ra.fontSize*e.scale*a.scale():0,d={titleBarText:e.Hd,titleBarTextColor:l.ug,titleBarBackgroundColor:l.tg,titleBarMaxFontSize:l.ne,titleBarShown:p<l.qi};e.na?h=U.kg("B`ssnu!Rd`sbi!Gn`lUsdd!whrt`mh{`uhno/!Bmhbj!uid!mnfn!un!fn!un!iuuq;..b`ssnurd`sbi/bnl.gn`lusdd!gns!lnsd!edu`hmr/"):(i.Cc(l.vj,e,d,function(e){e.titleBarWidth=f,e.titleBarHeight=s,e.labelFontSize=p,e.viewportScale=a.scale()}),h=d.titleBarText),h&&0!==h.length&&d.titleBarShown&&(e=a.md(e.Tb(e,{}),{}).y>o/2,t={x:u,y:e?t:o-c+t,f:f,i:s},u=V.A(t),n.fillStyle=l.tg,n.fillRect(0,e?0:o-c,r,c),n.fillStyle=l.ug,Z.Me({fontFamily:l.wj||l.Ih,fontStyle:l.Xj||l.Jh,fontWeight:l.Zj||l.Lh,fontVariant:l.Yj||l.Kh,rb:l.ne,Yc:l.xj,ob:0,eb:0,sb:1},n,h,u,t,{x:t.x+t.f/2,y:t.y+t.i/2},!0,!0).la||n.clearRect(0,0,r,o))}}}}function L(e){function t(e,t,n){return m=!0,s&&s.stop(),f&&f.stop(),l(d.reset(e),t,n).N(function(){m=!1})}function o(t){d.update(t),h.I=!0,e.c.p("foamtree:dirty",!0)}function i(e,t){return d.d((0!==d.k()?.35:1)*e,(0!==d.A()?.35:1)*t)}function a(){if(1===b.Ob){var e=Math.round(1e4*d.k())/1e4;0!==e&&(v.$d=e,s=p.D.tc(v).ia({duration:500,G:{x:{start:e,end:0,P:R.Qb}},ca:function(){d.d(v.x-v.$d,0),o(1),v.$d=v.x}}).start())}}function u(){if(1===b.Ob){var e=Math.round(1e4*d.A())/1e4;0!==e&&(g.ae=e,f=p.D.tc(g).ia({duration:500,G:{y:{start:e,end:0,P:R.Qb}},ca:function(){d.d(0,g.y-g.ae),o(1),g.ae=g.y}}).start())}}function l(e,t,n){return e?p.D.tc(b).ia({duration:void 0===t?700:t,G:{Ob:{start:0,end:1,P:n||R.pg}},ca:function(){o(b.Ob)}}).bb():(new r).J().L()}function c(e){return function(){return m?(new r).J().L():e.apply(this,arguments)}}var s,f,h,p,d=new n(e),b={Ob:1},v={Fe:0,x:0,$d:0},g={Ge:0,y:0,ae:0},y=this,m=!1;e.c.j("model:loaded",function(e){h=e,d.reset(!1),d.update(1)}),e.c.j("timeline:initialized",function(e){p=e}),this.H=function(){e.c.p("zoom:initialized",this)},this.reset=function(e,n){return d.Pb(1),t(!0,e,n)},this.normalize=c(function(e,n){d.Gc(1)?t(!1,e,n):y.wf()}),this.wf=function(){a(),u()},this.k=c(function(e,t,n,r){return y.sc(e.q,t,n,r)}),this.Yb=c(function(e,t,n,r){return l(d.Yb(e,t),n,r)}),this.sc=c(function(e,t,n,r){return l(d.sc(e,t),n,r)}),this.Aj=c(function(e,t){d.sc(e,t)&&o(1)}),this.si=c(function(e,t){1===b.Ob&&i(e,t)&&o(1)}),this.Rg=c(function(e,t){d.Yb(e,t)&&o(1)}),this.Qg=c(function(e,t,n,r){e=0|d.Yb(e,t),(e|=i(n,r))&&o(1)}),this.ti=c(function(e,t,n){1===b.Ob&&(s=p.D.tc(v).ia({duration:e/.03,G:{Fe:{start:t,end:0,P:R.Qb}},ca:function(){d.d(v.Fe,0)&&o(1),a()}}).start(),f=p.D.tc(g).ia({duration:e/.03,G:{Ge:{start:n,end:0,P:R.Qb}},ca:function(){i(0,g.Ge)&&o(1),u()}}).start())}),this.ui=function(){s&&0===d.k()&&s.stop(),f&&0===d.A()&&f.stop()},this.Ic=function(e,t){d.Ic(e,t)},this.Pb=function(e){return d.Pb(e)},this.Gc=function(e){return d.Gc(e)},this.Rd=function(){return d.Rd()},this.absolute=function(e,t){return d.absolute(e,t)},this.md=function(e,t){return d.md(e,t)},this.scale=function(){return d.scale()},this.d=function(e){return d.S(e)},this.content=function(e,t,n,r){d.content(e,t,n,r)}}function _(n,a,u){function h(e){var t=[];return q.F(z,function(n){e(n)&&t.push(n.group)}),{groups:t}}function y(e,t){var n=_.options,r=n.jj,o=n.ij,n=n.fe,i=0<r+o?n:0,a=[];return Y.Ja(e,Y.ya(e,_.options.he),function(e,n,u){n="groups"===_.options.ge?u:n,e.e&&(e=D.D.m(e).fb(i*(o+r*n)).call(t).xa(),a.push(e))}),D.D.m({}).Ya(a).bb()}function x(e){ce||(ce=!0,j.d(function(){ce=!1,_.c.p("repaint:before"),N.ee(this.Pg)},{Pg:e}))}function w(){function e(r,o){var i=r.$;if(r.$=o<=t,r.Xc=o<=n,r.$!=i&&q.He(r,function(e){e.me=!0}),r.open||r.Vb||o++,i=r.e)for(var a=0;a<i.length;a++)e(i[a],o)}var t=_.options.Nd,n=Math.min(_.options.Nd,_.options.oi);e(z,1)}function T(){var e=[],t=S();for(t.ri&&_.c.p("model:childrenAttached",q.Lc(z)),t.dj&&B.complete(function(t){ue.pb(t),e.push(t)}),t=0;t<e.length;t++){var n=e[t];n.Ca=!0,K.d(n)}}function S(){var e=_.options.Nd,t=!1,n=!1;G.scale(),G.scale();for(var r=[z,1];0<r.length;){var o=r.shift(),i=r.shift(),a=!o.na&&i<e,n=n||a;o.Qa=o.Qa||a,o.open||o.Vb||i++;var u=o.e;if(!u&&a&&(t=I.S(o)||t,u=o.e),u)for(o=0;o<u.length;o++)r.push(u[o],i)}return{ri:t,dj:n}}var z,k=this,_={c:new l,options:a,Ud:u},j=new e,D=new c(j),O=F.create(),A=new t(_),G=new L(_),E=new s(_),I=new f(_.options),B=new m(_),N=new C(_,j),W=new g(_);new M(_);var U=new p(_),K=new d(_),X=new b(_),J=new v(_);_.c.j("stage:initialized",function(e,t,n,r){re.gf(n,r)}),_.c.j("stage:resized",function(e,t,n,r){re.hj(e,t,n,r)}),_.c.j("foamtree:attachChildren",T),_.c.j("openclose:changing",w),_.c.j("interaction:reset",function(){le(!0)}),_.c.j("foamtree:dirty",x),this.H=function(){_.c.p("timeline:initialized",D),z=I.H(),A.H(n),E.H(),N.H(),W.H(),U.H(),K.H(),G.H(),X.H(),J.H()},this.kb=function(){D.d(),ae.stop(),j.k(),A.kb()};var Z="groupLabelFontFamily groupLabelFontStyle groupLabelFontVariant groupLabelFontWeight groupLabelLineHeight groupLabelHorizontalPadding groupLabelVerticalPadding groupLabelDottingThreshold groupLabelMaxTotalHeight groupLabelMinFontSize groupLabelMaxFontSize groupLabelDecorator".split(" "),Q="rainbowColorDistribution rainbowLightnessDistribution rainbowColorDistributionAngle rainbowLightnessDistributionAngle rainbowColorModelStartPoint rainbowLightnessCorrection rainbowSaturationCorrection rainbowStartColor rainbowEndColor rainbowHueShift rainbowHueShiftCenter rainbowSaturationShift rainbowSaturationShiftCenter rainbowLightnessShift rainbowLightnessShiftCenter attributionTheme".split(" "),$=!1,ee=["groupBorderRadius","groupBorderRadiusCorrection","groupBorderWidth","groupInsetWidth","groupBorderWidthScaling"],te=["maxGroupLevelsDrawn","maxGroupLabelLevelsDrawn"];this.Wb=function(e){_.c.p("options:changed",e),H.nb(e,Z)&&q.F(z,function(e){e.Wa=!0}),H.nb(e,Q)&&(z.Ma=!0),H.nb(e,ee)&&($=!0),H.nb(e,te)&&(w(),T())},this.reload=function(){oe.reload()},this.Xb=function(e,t){H.defer(function(){if($)re.mi(e),$=!1;else{if(t)for(var n=I.A(t),r=n.length-1;0<=r;r--)n[r].I=!0;else z.I=!0;x(e)}})},this.Y=function(){A.k()},this.update=function(){I.update(),re.Bj()},this.reset=function(){return le(!1)},this.S=N.d,this.Ba=function(){var e={};return function(t,n){var r=I.d(t);return r?E.nd(e,r,n):null}}(),this.Aa=function(){var e={x:0,y:0},t={x:0,y:0};return function(n,r){var o=I.d(n);return o?(e.x=r.x,e.y=r.y,o.Tb(e,e),G.md(e,e),t.x=e.x,t.y=e.y,t):null}}(),this.ya=function(){var e={};return function(t){return(t=I.d(t))?E.pd(e,t):null}}(),this.gb=function(){var e={};return function(t){return(t=I.d(t))?E.od(e,t):null}}(),this.za=function(){var e={};return function(){return G.d(e)}}(),this.yc=function(){this.A({groups:h(function(e){return e.group.selected}),newState:!0,keepPrevious:!1}),this.k({groups:h(function(e){return e.group.open}),newState:!0,keepPrevious:!1}),this.d({groups:h(function(e){return e.group.exposed}),newState:!0,keepPrevious:!1})},this.Ja=function(){return h(function(e){return e.U})},this.d=function(e){return oe.submit(function(){return U.fc(I.k(e,"exposed",!1),!1,!0,!1)})},this.Pa=function(){return h(function(e){return e.open})},this.k=function(e){return oe.submit(function(){return X.Jb(I.k(e,"open",!0),!1,!1)})},this.cb=function(){return h(function(e){return e.selected})},this.A=function(e){return oe.submit(function(){return J.select(I.k(e,"selected",!0),!1),(new r).J().L()})},this.Ac=function(e){return(e=I.d(e))?e===z?G.reset(a.wc,R.pa(a.xc)):G.k(e,a.Pc,a.wc,R.pa(a.xc)):(new r).J().L()},this.Ta=function(e){return W.gb[e]},this.zc=function(){var e=P;return{frames:e.frames,totalTime:e.totalTime,lastFrameTime:e.Id,lastInterFrameTime:e.Jd,fps:e.Pe}};var ne,re=function(){function e(e,o){var i=e||n,u=o||r;n=i,r=u;var l=a.bc&&a.bc.boundary;l&&2<l.length?z.o=l.map(function(e){return{x:i*e.x,y:u*e.y}}):z.o=[{x:0,y:0},{x:i,y:0},{x:i,y:u},{x:0,y:u}],t()}function t(){z.Z=!0,z.u=z.o,z.q=V.q(z.o,z.q),z.K=z,V.se(z.o,z.K)}var n,r;return{gf:e,hj:function(t,n,r,o){ue.stop();var i=r/t,a=o/n;q.Ie(z,function(e){e.x=e.x*i+(Math.random()-.5)*r/1e3,e.y=e.y*a+(Math.random()-.5)*o/1e3}),e(r,o),z.La=!0,B.step(ue.pb,!0,!1,function(e){var t=e.e;if(t){B.Mb(e);for(var n=t.length-1;0<=n;n--){var r=t[n];r.f=r.rc}e.La=!0}})?x(!1):(B.qc(z),_.options.de?(x(!1),ae.dg(),ae.fd()):(B.complete(ue.pb),z.Ma=!0,x(!1)))},mi:function(e){var n=!1;return z.empty()||(t(),ae.Fb()||(n=B.step(ue.pb,!1,!1),x(e))),n},Bj:function(){q.Fa(z,function(e){e.empty()||B.Mb(e)}),B.qc(z),_.options.de?(ae.dg(),q.Fa(z,function(e){e.empty()||ue.ef(e)})):(q.Fa(z,function(e){e.empty()||ue.pb(z)}),B.complete(ue.pb),z.Ma=!0,x(!1))}}}(),oe=function(){function e(){0===a.Yd&&G.reset(0),_.options.Uf(a.bc),re.gf(),I.Y(a.bc),S(),w(),_.c.p("model:loaded",z,q.Lc(z));var e;z.empty()||(z.open=!0,z.Qa=!0,a.de?e=ae.fd():(ae.wi(),e=u()),t(),0<a.fe?(N.clear(),A.d(1)):e=o([e,n(1)])),_.options.Tf(a.bc),e&&(_.options.Xf(),e.N(function(){j.d(_.options.Wf)}))}function t(){var e=a.Ua,t=a.bd;a.Ua=0,a.bd=0,k.yc(),a.Ua=e,a.bd=t}function n(e,t){return 0===a.Le||t?(A.d(e),(new r).J().L()):D.D.m({opacity:A.d()}).oe(2).ia({duration:a.Le,G:{opacity:{end:e,P:R.pa(a.fh)}},ca:function(){A.d(this.opacity)}}).bb()}function u(){q.Fa(z,function(e){e.Ca=!1});var e=new r,t=new i(e.J);return t.d(),z.Ca=!0,K.d(z).N(t.k),y(z,function e(){this.M&&this.o&&(this.Z=this.Ca=!0,t.d(),K.d(this).N(t.k),t.d(),y(this,e).N(t.k))}),e.L()}function l(){for(var e=0;e<s.length;e++){var t=s[e],n=t.action();H.Q(n,"then")?n.N(t.Be.J):t.Be.J()}s=[]}var c=!1,s=[];return{reload:function(){c||(z.empty()?e():(ue.stop(),D.d(),ae.stop(),c=!0,o(0<a.Yd?[K.k(),le(!1)]:[n(0)]).N(function(){n(0,!0),c=!1,e(),H.defer(l)})))},submit:function(e){if(c){var t=new r;return s.push({action:e,Be:t}),t.L()}return e()}}}(),ie=new i(function(){ne.J()}),ae=function(){function e(){return i||(ie.A()&&(ne=new r),ie.d(),t(),i=!0,j.repeat(n)),ne.L()}function t(){o=O.now()}function n(){var t=O.now()-o>a.gj,t=B.step(function(t){t.Ca=!0,ue.ef(t),ie.d(),K.d(t).N(ie.k),ie.d(),y(t,function(){this.Qa=!0,e()}).N(ie.k)},!0,t)||t;return x(!0),t&&(i=!1,ie.k()),t}var o,i=!1;return{wi:function(){B.complete(ue.pb)},fd:e,dg:t,Fb:function(){return!ie.A()},stop:function(){j.cancel(n),i=!1,ie.clear()}}}(),ue=function(){function e(e){var t=!e.empty();if(e.Ca=!0,t){for(var n=e.e,r=n.length-1;0<=r;r--){var o=n[r];o.f=o.rc}e.La=!0}return t}var t=[];return{ef:function(n){var r=_.options,o=r.zh;0<o?Y.Ja(n,Y.ya(n,_.options.he),function(e,n,i){n="groups"===_.options.ge?i:n,ie.d(),t.push(D.D.m(e).fb(n*r.yh*o).ia({duration:o,G:{f:{start:e.qg,end:e.rc,P:R.pa(r.Ah)}},ca:function(){this.f=Math.max(0,this.f),this.parent.La=!0,ae.fd()}}).ib(ie.k).start())}):e(n)&&ae.fd()},pb:e,stop:function(){for(var e=t.length-1;0<=e;e--)t[e].stop();t=[]}}}(),le=function(){var e=!1;return function(t){if(e)return(new r).J().L();e=!0;var n=[];n.push(G.reset(a.wc,R.pa(a.xc)));var i=new r;return U.fc({e:[],Ia:!1,Ha:!1},t,!1,!0).N(function(){X.Jb({e:[],Ia:!1,Ha:!1},t,!1).N(i.J)}),n.push(i.L()),o(n).N(function(){e=!1,t&&a.Yf()})}}(),ce=!1}function j(){return{version:"3.4.4",build:"36955f78f6b79223438db3b18b9b64b5aad799bb/36955f78",brandingAllowed:!1}}var P,D=function(){var e,t=window.navigator.userAgent;try{window.localStorage.setItem("ftap5caavc","ftap5caavc"),window.localStorage.removeItem("ftap5caavc"),e=!0}catch(t){e=!1}return{pf:function(){return/webkit/i.test(t)},nf:function(){return/Mac/.test(t)},mf:function(){return/iPad|iPod|iPhone/.test(t)},jf:function(){return/Android/.test(t)},ii:function(){return"ontouchstart"in window||!!window.DocumentTouch&&document instanceof window.DocumentTouch},hi:function(){return e},gi:function(){var e=document.createElement("canvas");return!(!e.getContext||!e.getContext("2d"))},Cd:function(e,t){return[].forEach&&D.gi()?e&&e():t&&t()}}}(),F=function(){function e(){return window.performance&&(window.performance.now||window.performance.mozNow||window.performance.msNow||window.performance.oNow||window.performance.webkitNow)||Date.now}var t=e();return{create:function(){return{now:function(){var t=e();return function(){return t.call(window.performance)}}()}},now:function(){return t.call(window.performance)}}}(),O=D.Cd(function(){function e(){this.buffer=[],this.oa=0,this.Fc=H.extend({},a)}function t(e){return function(){var t,n=this.buffer,r=this.oa;for(n[r++]="call",n[r++]=e,n[r++]=arguments.length,t=0;t<arguments.length;t++)n[r++]=arguments[t];this.oa=r}}function n(e){return function(){return o[e].apply(o,arguments)}}var r=document.createElement("canvas");r.width=1,r.height=1;var o=r.getContext("2d"),r=["font"],i="fillStyle globalAlpha globalCompositeOperation lineCap lineDashOffset lineJoin lineWidth miterLimit shadowBlur shadowColor shadowOffsetX shadowOffsetY strokeStyle textAlign textBaseline".split(" "),a={};return i.concat(r).forEach(function(e){a[e]=o[e]}),e.prototype.clear=function(){this.oa=0},e.prototype.Na=function(){return 0===this.oa},e.prototype.Sa=function(e){e instanceof O?function(e,t,n){for(var r=0,o=e.oa,i=e.buffer;r<n;)i[o++]=t[r++];e.oa=o}(e,this.buffer,this.oa):function(e,t,n,r){for(var o=0;o<n;)switch(t[o++]){case"set":e[t[o++]]=t[o++];break;case"setGlobalAlpha":e[t[o++]]=t[o++]*r;break;case"call":var i=t[o++];switch(t[o++]){case 0:e[i]();break;case 1:e[i](t[o++]);break;case 2:e[i](t[o++],t[o++]);break;case 3:e[i](t[o++],t[o++],t[o++]);break;case 4:e[i](t[o++],t[o++],t[o++],t[o++]);break;case 5:e[i](t[o++],t[o++],t[o++],t[o++],t[o++]);break;case 6:e[i](t[o++],t[o++],t[o++],t[o++],t[o++],t[o++]);break;case 7:e[i](t[o++],t[o++],t[o++],t[o++],t[o++],t[o++],t[o++]);break;case 8:e[i](t[o++],t[o++],t[o++],t[o++],t[o++],t[o++],t[o++],t[o++]);break;case 9:e[i](t[o++],t[o++],t[o++],t[o++],t[o++],t[o++],t[o++],t[o++],t[o++]);break;default:throw"CB0"}}}(e,this.buffer,this.oa,H.B(e.globalAlpha,1))},e.prototype.replay=e.prototype.Sa,e.prototype.d=function(){return new e},e.prototype.scratch=e.prototype.d,"arc arcTo beginPath bezierCurveTo clearRect clip closePath drawImage fill fillRect fillText lineTo moveTo putImageData quadraticCurveTo rect rotate scale setLineDash setTransform stroke strokeRect strokeText transform translate".split(" ").forEach(function(n){e.prototype[n]=t(n)}),["measureText","createLinearGradient","createRadialGradient","createPattern","getLineDash"].forEach(function(t){e.prototype[t]=n(t)}),["save","restore"].forEach(function(r){e.prototype[r]=function(e,t){return function(){e.apply(this,arguments),t.apply(this,arguments)}}(t(r),n(r))}),r.forEach(function(t){Object.defineProperty(e.prototype,t,{set:function(e){o[t]=e,this.Fc[t]=e;var n=this.buffer;n[this.oa++]="set",n[this.oa++]=t,n[this.oa++]=e},get:function(){return this.Fc[t]}})}),i.forEach(function(t){Object.defineProperty(e.prototype,t,{set:function(e){this.Fc[t]=e;var n=this.buffer;n[this.oa++]="globalAlpha"===t?"setGlobalAlpha":"set",n[this.oa++]=t,n[this.oa++]=e},get:function(){return this.Fc[t]}})}),e.prototype.roundRect=function(e,t,n,r,o){this.beginPath(),this.moveTo(e+o,t),this.lineTo(e+n-o,t),this.quadraticCurveTo(e+n,t,e+n,t+o),this.lineTo(e+n,t+r-o),this.quadraticCurveTo(e+n,t+r,e+n-o,t+r),this.lineTo(e+o,t+r),this.quadraticCurveTo(e,t+r,e,t+r-o),this.lineTo(e,t+o),this.quadraticCurveTo(e,t,e+o,t),this.closePath()},e.prototype.fillPolygonWithText=function(e,t,n,r,o){o||(o={});var i={rb:H.B(o.maxFontSize,Z.Ea.rb),Yc:H.B(o.minFontSize,Z.Ea.Yc),lineHeight:H.B(o.lineHeight,Z.Ea.lineHeight),ob:H.B(o.horizontalPadding,Z.Ea.ob),eb:H.B(o.verticalPadding,Z.Ea.eb),sb:H.B(o.maxTotalTextHeight,Z.Ea.sb),fontFamily:H.B(o.fontFamily,Z.Ea.fontFamily),fontStyle:H.B(o.fontStyle,Z.Ea.fontStyle),fontVariant:H.B(o.fontVariant,Z.Ea.fontVariant),fontWeight:H.B(o.fontWeight,Z.Ea.fontWeight),verticalAlign:H.B(o.verticalAlign,Z.Ea.verticalAlign)},a=o.cache;if(a&&H.Q(o,"area")){a.hd||(a.hd=new O);var u=o.area,l=H.B(o.cacheInvalidationThreshold,.05);e=Z.ye(i,this,r,e,V.q(e,{}),{x:t,y:n},o.allowForcedSplit||!1,o.allowEllipsis||!1,a,u,l,o.invalidateCache)}else e=Z.Me(i,this,r,e,V.q(e,{}),{x:t,y:n},o.allowForcedSplit||!1,o.allowEllipsis||!1);return e.la?{fit:!0,lineCount:e.mc,fontSize:e.fontSize,box:{x:e.da.x,y:e.da.y,w:e.da.f,h:e.da.i},ellipsis:e.ec}:{fit:!1}},e}),A=D.Cd(function(){function e(e){this.O=e,this.d=[],this.Hb=[void 0],this.Mc=["#SIZE#px sans-serif"],this.Kd=[0],this.Ld=[1],this.ie=[0],this.je=[0],this.ke=[0],this.Qd=[10],this.hc=[10],this.Rb=[this.Hb,this.Mc,this.hc,this.Kd,this.Ld,this.ie,this.Qd,this.je,this.ke],this.ga=[1,0,0,1,0,0]}function t(e){var t=e.O,n=e.Rb[0].length-1;e.Hb[n]&&(t.setLineDash(e.Hb[n]),t.Sj=e.Kd[n]),t.miterLimit=e.Qd[n],t.lineWidth=e.Ld[n],t.shadowBlur=e.ie[n],t.shadowOffsetX=e.je[n],t.shadowOffsetY=e.ke[n],t.font=e.Mc[n].replace("#SIZE#",e.hc[n].toString())}function n(e){return function(){return this.O[e].apply(this.O,arguments)}}function r(e){return function(t,n){var r=this.ga;return this.O[e].call(this.O,i(t,n,r),a(t,n,r))}}function o(e){return function(t,n,r,o){var u=this.ga;return this.O[e].call(this.O,i(t,n,u),a(t,n,u),r*u[0],o*u[3])}}function i(e,t,n){return e*n[0]+t*n[2]+n[4]}function a(e,t,n){return e*n[1]+t*n[3]+n[5]}function u(e,t){for(var n=0;n<e.length;n++)e[n]*=t[0];return e}e.prototype.save=function(){this.d.push(this.ga.slice(0));for(var e=0;e<this.Rb.length;e++){var t=this.Rb[e];t.push(t[t.length-1])}this.O.save()},e.prototype.restore=function(){this.ga=this.d.pop();for(var e=0;e<this.Rb.length;e++)this.Rb[e].pop();this.O.restore(),t(this)},e.prototype.scale=function(e,n){var r=this.ga;r[0]*=e,r[1]*=e,r[2]*=n,r[3]*=n;var r=this.ga,o=this.Rb,i=o[0].length-1,a=this.Hb[i];for(a&&u(a,r),a=2;a<o.length;a++){o[a][i]*=r[0]}t(this)},e.prototype.translate=function(e,t){var n=this.ga;n[4]+=n[0]*e+n[2]*t,n[5]+=n[1]*e+n[3]*t},["moveTo","lineTo"].forEach(function(t){e.prototype[t]=r(t)}),["clearRect","fillRect","strokeRect","rect"].forEach(function(t){e.prototype[t]=o(t)}),"fill stroke beginPath closePath clip createImageData createPattern getImageData putImageData getLineDash setLineDash".split(" ").forEach(function(t){e.prototype[t]=n(t)}),[{ub:"lineDashOffset",yb:function(e){return e.Kd}},{ub:"lineWidth",yb:function(e){return e.Ld}},{ub:"miterLimit",yb:function(e){return e.Qd}},{ub:"shadowBlur",yb:function(e){return e.ie}},{ub:"shadowOffsetX",yb:function(e){return e.je}},{ub:"shadowOffsetY",yb:function(e){return e.ke}}].forEach(function(t){Object.defineProperty(e.prototype,t.ub,{set:function(e){var n=t.yb(this);e*=this.ga[0],n[n.length-1]=e,this.O[t.ub]=e}})});var l=/(\d+(?:\.\d+)?)px/;return Object.defineProperty(e.prototype,"font",{set:function(e){var t=l.exec(e);if(1<t.length){var n=this.hc.length-1;this.hc[n]=parseFloat(t[1]),this.Mc[n]=e.replace(l,"#SIZE#px"),this.O.font=this.Mc[n].replace("#SIZE#",(this.hc[n]*this.ga[0]).toString())}}}),"fillStyle globalAlpha globalCompositeOperation lineCap lineJoin shadowColor strokeStyle textAlign textBaseline".split(" ").forEach(function(t){Object.defineProperty(e.prototype,t,{set:function(e){this.O[t]=e}})}),e.prototype.arc=function(e,t,n,r,o,u){var l=this.ga;this.O.arc(i(e,t,l),a(e,t,l),n*l[0],r,o,u)},e.prototype.arcTo=function(e,t,n,r,o){var u=this.ga;this.O.arc(i(e,t,u),a(e,t,u),i(n,r,u),a(n,r,u),o*u[0])},e.prototype.bezierCurveTo=function(e,t,n,r,o,u){var l=this.ga;this.O.bezierCurveTo(i(e,t,l),a(e,t,l),i(n,r,l),a(n,r,l),i(o,u,l),a(o,u,l))},e.prototype.drawImage=function(e,t,n,r,o,u,l,c,s){function f(t,n,r,o){p.push(i(t,n,h)),p.push(a(t,n,h)),r=H.V(r)?e.width:r,o=H.V(o)?e.height:o,p.push(r*h[0]),p.push(o*h[3])}var h=this.ga,p=[e];H.V(u)?f(t,n,r,o):f(u,l,c,s),this.O.drawImage.apply(this.O,p)},e.prototype.quadraticCurveTo=function(e,t,n,r){var o=this.ga;this.O.quadraticCurveTo(i(e,t,o),a(e,t,o),i(n,r,o),a(n,r,o))},e.prototype.fillText=function(e,t,n,r){var o=this.ga;this.O.fillText(e,i(t,n,o),a(t,n,o),H.Rc(r)?r*o[0]:1e20)},e.prototype.setLineDash=function(e){e=u(e.slice(0),this.ga),this.Hb[this.Hb.length-1]=e,this.O.setLineDash(e)},e}),G=function(){var e=!D.pf()||D.mf()||D.jf()?1:7;return{eh:function(){function t(e){e.beginPath(),E.le(e,u)}var n=document.createElement("canvas");n.width=800,n.height=600;var r,o=n.getContext("2d"),i=n.width,n=n.height,a=0,u=[{x:0,y:100}];for(r=1;6>=r;r++)a=2*r*Math.PI/6,u.push({x:0+100*Math.sin(a),y:0+100*Math.cos(a)});r={polygonPlainFill:[t,function(e){e.fillStyle="rgb(255, 0, 0)",e.fill()}],polygonPlainStroke:[t,function(e){e.strokeStyle="rgb(128, 0, 0)",e.lineWidth=2,e.closePath(),e.stroke()}],polygonGradientFill:[t,function(e){var t=e.createRadialGradient(0,0,10,0,0,60);t.addColorStop(0,"rgb(255, 0, 0)"),t.addColorStop(1,"rgb(255, 255, 0)"),e.fillStyle=t,e.fill()}],polygonGradientStroke:[t,function(e){var t=e.createLinearGradient(-100,-100,100,100);t.addColorStop(0,"rgb(224, 0, 0)"),t.addColorStop(1,"rgb(32, 0, 0)"),e.strokeStyle=t,e.lineWidth=2,e.closePath(),e.stroke()}],polygonExposureShadow:[t,function(e){e.shadowBlur=50,e.shadowColor="rgba(0, 0, 0, 1)",e.fillStyle="rgba(0, 0, 0, 1)",e.globalCompositeOperation="source-over",e.fill(),e.shadowBlur=0,e.shadowColor="transparent",e.globalCompositeOperation="destination-out",e.fill()}],labelPlainFill:[function(e){e.fillStyle="#000",e.font="24px sans-serif",e.textAlign="center"},function(e){e.fillText("Some text",0,-16),e.fillText("for testing purposes",0,16)}]};var l,a=100/Object.keys(r).length,c=F.now(),s={};for(l in r){var f,h=r[l],p=F.now(),d=0;do{for(o.save(),o.translate(Math.random()*i,Math.random()*n),f=3*Math.random()+.5,o.scale(f,f),f=0;f<h.length;f++)h[f](o);o.restore(),d++,f=F.now()}while(f-p<a);s[l]=e*(f-p)/d}return s.total=F.now()-c,s}}}(),E={le:function(e,t){var n=t[0];e.moveTo(n.x,n.y);for(var r=t.length-1;0<r;r--)n=t[r],e.lineTo(n.x,n.y)},qj:function(e,t,n,r){var o,i,a,u=[],l=0,c=t.length;for(a=0;a<c;a++)o=t[a],i=t[(a+1)%c],o=V.d(o,i),o=Math.sqrt(o),u.push(o),l+=o;n=r*(n+.5*r*l/c);var s,f;r={};var l={},h={},p=0;for(a=0;a<c;a++)o=t[a],i=t[(a+1)%c],s=t[(a+2)%c],f=u[(a+1)%c],f=Math.min(.5,n/f),V.Aa(1-f,i,s,l),V.Aa(f,i,s,h),p++,0==a&&(s=Math.min(.5,n/u[0]),V.Aa(s,o,i,r),p++,e.moveTo(r.x,r.y)),e.quadraticCurveTo(i.x,i.y,l.x,l.y),e.lineTo(h.x,h.y);return!0}},I=new function(){function e(e){if("hsl"==e.model||"hsla"==e.model)return e;var t,n=e.r/=255,r=e.g/=255,o=e.b/=255,i=Math.max(n,r,o),a=Math.min(n,r,o),u=(i+a)/2;if(i==a)t=a=0;else{var l=i-a,a=.5<u?l/(2-i-a):l/(i+a);switch(i){case n:t=(r-o)/l+(r<o?6:0);break;case r:t=(o-n)/l+2;break;case o:t=(n-r)/l+4}t/=6}return e.h=360*t,e.s=100*a,e.l=100*u,e.model="hsl",e}var t={h:0,s:0,l:0,a:1,model:"hsla"};this.Ba=function(n){return H.Sc(n)?e(I.Hg(n)):H.jc(n)?e(n):t},this.Hg=function(e){var n;return(n=/rgba\(\s*([^,\s]+)\s*,\s*([^,\s]+)\s*,\s*([^,\s]+)\s*,\s*([^,\s]+)\s*\)/.exec(e))&&5==n.length?{r:parseFloat(n[1]),g:parseFloat(n[2]),b:parseFloat(n[3]),a:parseFloat(n[4]),model:"rgba"}:(n=/hsla\(\s*([^,\s]+)\s*,\s*([^,%\s]+)%\s*,\s*([^,\s%]+)%\s*,\s*([^,\s]+)\s*\)/.exec(e))&&5==n.length?{h:parseFloat(n[1]),s:parseFloat(n[2]),l:parseFloat(n[3]),a:parseFloat(n[4]),model:"hsla"}:(n=/rgb\(\s*([^,\s]+)\s*,\s*([^,\s]+)\s*,\s*([^,\s]+)\s*\)/.exec(e))&&4==n.length?{r:parseFloat(n[1]),g:parseFloat(n[2]),b:parseFloat(n[3]),a:1,model:"rgb"}:(n=/hsl\(\s*([^,\s]+)\s*,\s*([^,\s%]+)%\s*,\s*([^,\s%]+)%\s*\)/.exec(e))&&4==n.length?{h:parseFloat(n[1]),s:parseFloat(n[2]),l:parseFloat(n[3]),a:1,model:"hsl"}:(n=/#([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})/.exec(e))&&4==n.length?{r:parseInt(n[1],16),g:parseInt(n[2],16),b:parseInt(n[3],16),a:1,model:"rgb"}:(n=/#([0-9a-fA-F])([0-9a-fA-F])([0-9a-fA-F])/.exec(e))&&4==n.length?{r:17*parseInt(n[1],16),g:17*parseInt(n[2],16),b:17*parseInt(n[3],16),a:1,model:"rgb"}:t},this.Cg=function(e){function t(e,t,n){return 0>n&&(n+=1),1<n&&(n-=1),n<1/6?e+6*(t-e)*n:.5>n?t:n<2/3?e+(t-e)*(2/3-n)*6:e}if("rgb"==e.model||"rgba"==e.model)return Math.sqrt(e.r*e.r*.241+e.g*e.g*.691+e.b*e.b*.068)/255;var n,r;n=e.l/100;var o=e.s/100;if(r=e.h/360,0==e.Vj)n=e=r=n;else{var o=.5>n?n*(1+o):n+o-n*o,i=2*n-o;n=t(i,o,r+1/3),e=t(i,o,r),r=t(i,o,r-1/3)}return Math.sqrt(65025*n*n*.241+65025*e*e*.691+65025*r*r*.068)/255},this.Ng=function(e){if(H.Sc(e))return e;if(!H.jc(e))return"#000";switch(e.model){case"hsla":return I.Ig(e);case"hsl":return I.Ac(e);case"rgba":return I.Lg(e);case"rgb":return I.Kg(e);default:return"#000"}},this.Lg=function(e){return"rgba("+(.5+e.r|0)+","+(.5+e.g|0)+","+(.5+e.b|0)+","+e.a+")"},this.Kg=function(e){return"rgba("+(.5+e.r|0)+","+(.5+e.g|0)+","+(.5+e.b|0)+")"},this.Ig=function(e){return"hsla("+(.5+e.h|0)+","+(.5+e.s|0)+"%,"+(.5+e.l|0)+"%,"+e.a+")"},this.Ac=function(e){return"hsl("+(.5+e.h|0)+","+(.5+e.s|0)+"%,"+(.5+e.l|0)+"%)"},this.Y=function(e,t,n){return"hsl("+(.5+e|0)+","+(.5+t|0)+"%,"+(.5+n|0)+"%)"}},B={Je:function(e,t,n,r){return r=r||{},e=e.getBoundingClientRect(),r.x=t-e.left,r.y=n-e.top,r}},R=function(){function e(e){return function(t){return Math.pow(t,e)}}function t(e){return function(t){return 1-Math.pow(1-t,e)}}function n(e){return function(t){return 1>(t*=2)?.5*Math.pow(t,e):1-.5*Math.abs(Math.pow(2-t,e))}}function r(e){return function(t){for(var n=0;n<e.length;n++)t=(0,e[n])(t);return t}}return{pa:function(e){switch(e){case"linear":return R.Ib;case"bounce":return R.Vg;case"squareIn":return R.og;case"squareOut":return R.Qb;case"squareInOut":return R.pg;case"cubicIn":return R.Zg;case"cubicOut":return R.Ae;case"cubicInOut":return R.$g;case"quadIn":return R.Qi;case"quadOut":return R.Si;case"quadInOut":return R.Ri;default:return R.Ib}},Ib:function(e){return e},Vg:r([n(2),function(e){return 0===e?0:1===e?1:e*(e*(e*(e*(25.9425*e-85.88)+105.78)-58.69)+13.8475)}]),og:e(2),Qb:t(2),pg:n(2),Zg:e(3),Ae:t(3),$g:n(3),Qi:e(2),Si:t(2),Ri:n(2),d:r}}(),H={V:function(e){return void 0===e},of:function(e){return null===e},Rc:function(e){return"[object Number]"===Object.prototype.toString.call(e)},Sc:function(e){return"[object String]"===Object.prototype.toString.call(e)},Fd:function(e){return"function"==typeof e},jc:function(e){return e===Object(e)},Ed:function(e,t){return 1e-6>e-t&&-1e-6<e-t},kf:function(e){return H.V(e)||H.of(e)||H.Sc(e)&&!/\S/.test(e)},Q:function(e,t){return e&&e.hasOwnProperty(t)},nb:function(e,t){if(e)for(var n=t.length-1;0<=n;n--)if(e.hasOwnProperty(t[n]))return!0;return!1},extend:function(e){return H.dh(Array.prototype.slice.call(arguments,1),function(t){if(t)for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])}),e},A:function(e,t){return e.map(function(e){return e[t]},[])},dh:function(e,t,n){null!=e&&(e.forEach?e.forEach(t,n):H.Ga(e,t,n))},Ga:function(e,t,n){for(var r in e)if(e.hasOwnProperty(r)&&!1===t.call(n,e[r],r,e))break},B:function(){for(var e=0;e<arguments.length;e++){var t=arguments[e];if(!(H.V(t)||H.Rc(t)&&isNaN(t)||H.Sc(t)&&H.kf(t)))return t}},cg:function(e,t){var n=e.indexOf(t);0<=n&&e.splice(n,1)},ah:function(e,t,n){var r;return function(){var o=this,i=arguments,a=n&&!r;clearTimeout(r),r=setTimeout(function(){r=null,n||e.apply(o,i)},t),a&&e.apply(o,i)}},defer:function(e){setTimeout(e,1)},k:function(e){return e},ta:function(){}},N={ji:function(e,t,n){return D.hi()?function(){var r=t+":"+JSON.stringify(arguments),o=window.localStorage.getItem(r);return o&&(o=JSON.parse(o)),o&&Date.now()-o.t<n?o.v:(o=e.apply(this,arguments),window.localStorage.setItem(r,JSON.stringify({v:o,t:Date.now()})),o)}:e}},W={m:function(e,t){function n(){var n=[];if(Array.isArray(e))for(var r=0;r<e.length;r++){var o=e[r];o&&n.push(o.apply(t,arguments))}else e&&n.push(e.apply(t,arguments));return n}return n.empty=function(){return 0===e.length&&!H.Fd(e)},n}},U={kg:function(e){for(var t="",n=0;n<e.length;n++)t+=String.fromCharCode(1^e.charCodeAt(n));return t}},q=function(){var e={Ie:function(e,t){if(e.e)for(var n=e.e,r=0;r<n.length;r++)t(n[r],r)},Jc:function(t,n){if(t.e)for(var r=t.e,o=0;o<r.length;o++)if(!1===e.Jc(r[o],n)||!1===n(r[o],o))return!1}};return e.F=e.Jc,e.Kc=function(t,n){if(t.e)for(var r=t.e,o=0;o<r.length;o++)if(!1===n(r[o],o)||!1===e.Kc(r[o],n))return!1},e.Fa=function(t,n){if(t.e)for(var r=t.e,o=0;o<r.length;o++)if(!1===e.Fa(r[o],n))return!1;return n(t)},e.Mj=e.Fa,e.wd=function(t,n){!1!==n(t)&&e.Kc(t,n)},e.Lc=function(t,n){var r=[];return e.Kc(t,function(e){r.push(e)}),n?r.filter(n):r},e.He=function(e,t){for(var n=e.parent;n&&!1!==t(n);)n=n.parent},e.ki=function(e,t){for(var n=e.parent;n&&n!==t;)n=n.parent;return!!n},e}(),V=new function(){function e(e,t){var n=e.x-t.x,r=e.y-t.y;return n*n+r*r}function t(e,t,n){for(var r=0;r<e.length;r++){var o=V.za(e[r],e[r+1]||e[0],t,n,!0);if(o)return o}}return this.za=function(e,t,n,r,o){var i=e.x;e=e.y;var a=t.x-i;t=t.y-e;var u=n.x,l=n.y;n=r.x-u;var c=r.y-l;if(!(1e-12>=(r=a*c-n*t)&&-1e-12<=r)&&(u-=i,l-=e,n=(u*c-n*l)/r,0<=(r=(u*t-a*l)/r)&&(o||1>=r)&&0<=n&&1>=n))return{x:i+a*n,y:e+t*n}},this.Jg=function(e,t,n,r){var o=e.x;e=e.y;var i=t.x-o;t=t.y-e;var a=n.x;n=n.y;var u=r.x-a;r=r.y-n;var l=i*r-u*t;if(!(1e-12>=l&&-1e-12<=l)&&0<=(r=((a-o)*r-u*(n-e))/l)&&1>=r)return{x:o+i*r,y:e+t*r}},this.qe=function(e,n,r){for(var o,i=V.k(n,{}),a=V.k(r,{}),u=a.x-i.x,l=a.y-i.y,c=[],a=0;a<r.length;a++)o=r[a],c.push({x:o.x-u,y:o.y-l});for(r=[],o=[],a=0;a<e.length;a++){var s=e[a],f=t(n,i,s);f?(r.push(f),o.push(t(c,i,s))):(r.push(null),o.push(null))}for(a=0;a<e.length;a++)if(f=r[a],s=o[a],f&&s){n=e[a];var c=i,h=f.x-i.x,f=f.y-i.y,f=Math.sqrt(h*h+f*f);if(1e-12<f){var h=n.x-i.x,p=n.y-i.y,f=Math.sqrt(h*h+p*p)/f;n.x=c.x+f*(s.x-c.x),n.y=c.y+f*(s.y-c.y)}else n.x=c.x,n.y=c.y}for(a=0;a<e.length;a++)o=e[a],o.x+=u,o.y+=l},this.q=function(e,t){if(0!==e.length){var n,r,o,i;n=r=e[0].x,o=i=e[0].y;for(var a=e.length;0<--a;)n=Math.min(n,e[a].x),r=Math.max(r,e[a].x),o=Math.min(o,e[a].y),i=Math.max(i,e[a].y);return t.x=n,t.y=o,t.f=r-n,t.i=i-o,t}},this.A=function(e){return[{x:e.x,y:e.y},{x:e.x+e.f,y:e.y},{x:e.x+e.f,y:e.y+e.i},{x:e.x,y:e.y+e.i}]},this.k=function(e,t){for(var n=0,r=0,o=e.length,i=e[0],a=0,u=1;u<o-1;u++)var l=e[u],c=e[u+1],s=i.y+l.y+c.y,f=(l.x-i.x)*(c.y-i.y)-(c.x-i.x)*(l.y-i.y),n=n+f*(i.x+l.x+c.x),r=r+f*s,a=a+f;return t.x=n/(3*a),t.y=r/(3*a),t.ja=a/2,t},this.se=function(e,t){this.k(e,t),t.Nb=Math.sqrt(t.ja/Math.PI)},this.Ta=function(e,t){for(var n=0;n<e.length;n++){var r=e[n],o=e[n+1]||e[0];if(0>(t.y-r.y)*(o.x-r.x)-(t.x-r.x)*(o.y-r.y))return!1}return!0},this.Mg=function(e,t,n){var r=e.x,o=t.x;if(e.x>t.x&&(r=t.x,o=e.x),o>n.x+n.f&&(o=n.x+n.f),r<n.x&&(r=n.x),r>o)return!1;var i=e.y,a=t.y,u=t.x-e.x;return 1e-7<Math.abs(u)&&(a=(t.y-e.y)/u,e=e.y-a*e.x,i=a*r+e,a=a*o+e),i>a&&(r=a,a=i,i=r),a>n.y+n.i&&(a=n.y+n.i),i<n.y&&(i=n.y),i<=a},this.te=function(n,r,o,i,a){function u(o,i,a){if(r.x===h.x&&r.y===h.y)return a;var u=t(n,r,h),f=Math.sqrt(e(u,r)/(o*o+i*i));return f<s?(s=f,l=u.x,c=u.y,0!==i?Math.abs(c-r.y)/Math.abs(i):Math.abs(l-r.x)/Math.abs(o)):a}var l,c;i=H.B(i,.5),a=H.B(a,.5),o=H.B(o,1);var s=Number.MAX_VALUE;c=l=0;var f,h={x:0,y:0},p=i*o;return o*=1-i,i=1-a,h.x=r.x-p,h.y=r.y-a,f=u(p,a,f),h.x=r.x+o,h.y=r.y-a,f=u(o,a,f),h.x=r.x-p,h.y=r.y+i,f=u(p,i,f),h.x=r.x+o,h.y=r.y+i,f=u(o,i,f)},this.Eg=function(e,t){function n(e,t,n){var r=t.x,o=n.x;t=t.y,n=n.y;var i=o-r,a=n-t;return Math.abs(a*e.x-i*e.y-r*n+o*t)/Math.sqrt(i*i+a*a)}for(var r=e.length,o=n(t,e[r-1],e[0]),i=0;i<r-1;i++){var a=n(t,e[i],e[i+1]);a<o&&(o=a)}return o},this.Wb=function(e,t,n){var r;n={x:t.x+Math.cos(n),y:t.y-Math.sin(n)};var o=[],i=[],a=e.length;for(r=0;r<a;r++){var u=V.Jg(e[r],e[(r+1)%a],t,n);if(u&&(o.push(u),2==i.push(r)))break}if(2==o.length){var u=o[0],o=o[1],l=i[0],i=i[1],c=[o,u];for(r=l+1;r<=i;r++)c.push(e[r]);for(r=[u,o];i!=l;)i=(i+1)%a,r.push(e[i]);return e=[c,r],a=n.x-t.x,r=o.x-u.x,0===a&&(a=n.y-t.y,r=o.y-u.y),(0>a?-1:0<a?1:0)!=(0>r?-1:0<r?1:0)&&e.reverse(),e}},this.Aa=function(e,t,n,r){return r.x=e*(t.x-n.x)+n.x,r.y=e*(t.y-n.y)+n.y,r},this.d=e,this.re=function(e,n,r){if(H.Rc(n))n=2*Math.PI*n/360;else{var o=V.q(e,{});switch(n){case"random":n=Math.random()*Math.PI*2;break;case"top":n=Math.atan2(-o.i,0);break;case"bottom":n=Math.atan2(o.i,0);break;case"topleft":n=Math.atan2(-o.i,-o.f);break;default:n=Math.atan2(o.i,o.f)}}return o=V.k(e,{}),V.Aa(r,t(e,o,{x:o.x+Math.cos(n),y:o.y+Math.sin(n)}),o,{})},this},K=new function(){function e(e,t){this.face=e,this.jd=t,this.pc=this.cd=null}function t(e,t,n){this.ma=[e,t,n],this.C=Array(3);var r=t.y-e.y,o=n.z-e.z,i=t.x-e.x;t=t.z-e.z;var a=n.x-e.x;e=n.y-e.y,this.Oa={x:r*o-t*e,y:t*a-i*o,z:i*e-r*a},this.jb=[],this.sd=this.visible=!1}this.S=function(r){var i,a,u,l,c=r.length;for(i=0;i<c;i++)r[i].index=i,r[i].$b=null;var s,f=[];if(0<(s=function(){function n(e,n,r,o){var i=(n.y-e.y)*(r.z-e.z)-(n.z-e.z)*(r.y-e.y),a=(n.z-e.z)*(r.x-e.x)-(n.x-e.x)*(r.z-e.z),u=(n.x-e.x)*(r.y-e.y)-(n.y-e.y)*(r.x-e.x);return i*o.x+a*o.y+u*o.z>i*e.x+a*e.y+u*e.z?new t(e,n,r):new t(r,n,e)}function o(e,t,n,r){function o(e,t,n){return e=e.ma,t=e[0]==t?0:e[1]==t?1:2,e[(t+1)%3]!=n?(t+2)%3:t}t.C[o(t,n,r)]=e,e.C[o(e,r,n)]=t}if(4>c)return 0;var i=r[0],a=r[1],u=r[2],l=r[3],s=n(i,a,u,l),h=n(i,u,l,a),p=n(i,a,l,u),d=n(a,u,l,i);for(o(s,h,u,i),o(s,p,i,a),o(s,d,a,u),o(h,p,l,i),o(h,d,u,l),o(p,d,l,a),f.push(s,h,p,d),i=4;i<c;i++)for(a=r[i],u=0;4>u;u++)l=f[u],s=l.ma[0],h=l.Oa,0>h.x*(a.x-s.x)+h.y*(a.y-s.y)+h.z*(a.z-s.z)&&e.d(l,a);return 4}())){for(;s<c;){if(u=r[s],u.$b){for(i=u.$b;null!==i;)i.face.visible=!0,i=i.pc;var h,p;i=0;e:for(;i<f.length;i++)if(l=f[i],!1===l.visible){var d=l.C;for(a=0;3>a;a++)if(!0===d[a].visible){h=l,p=a;break e}}l=[];var d=[],b=h,v=p;do{if(l.push(b),d.push(v),v=(v+1)%3,!1===b.C[v].visible)do{for(i=b.ma[v],b=b.C[v],a=0;3>a;a++)b.ma[a]==i&&(v=a)}while(!1===b.C[v].visible&&(b!==h||v!==p))}while(b!==h||v!==p);var g=null,y=null;for(i=0;i<l.length;i++){var m,b=l[i],v=d[i],x=b.C[v],w=b.ma[(v+1)%3],C=b.ma[v],T=w.y-u.y,S=C.z-u.z,z=w.x-u.x,k=w.z-u.z,M=C.x-u.x,L=C.y-u.y;0<o.length?(m=o.pop(),m.ma[0]=u,m.ma[1]=w,m.ma[2]=C,m.Oa.x=T*S-k*L,m.Oa.y=k*M-z*S,m.Oa.z=z*L-T*M,m.jb.length=0,m.visible=!1,m.sd=!0):m={ma:[u,w,C],C:Array(3),Oa:{x:T*S-k*L,y:k*M-z*S,z:z*L-T*M},jb:[],visible:!1},f.push(m),b.C[v]=m,m.C[1]=b,null!==y&&(y.C[0]=m,m.C[2]=y),y=m,null===g&&(g=m),function(t,n,r){var o,i,u=t.ma[0],l=t.Oa,s=l.x,f=l.y,l=l.z,h=Array(c);for(n=n.jb,o=n.length,a=0;a<o;a++)i=n[a].jd,h[i.index]=!0,0>s*(i.x-u.x)+f*(i.y-u.y)+l*(i.z-u.z)&&e.d(t,i);for(n=r.jb,o=n.length,a=0;a<o;a++)i=n[a].jd,!0!==h[i.index]&&0>s*(i.x-u.x)+f*(i.y-u.y)+l*(i.z-u.z)&&e.d(t,i)}(m,b,x)}for(y.C[0]=g,g.C[2]=y,i=[],a=0;a<f.length;a++)if(l=f[a],!0===l.visible){for(d=l.jb,b=d.length,u=0;u<b;u++)v=d[u],g=v.cd,y=v.pc,null!==g&&(g.pc=y),null!==y&&(y.cd=g),null===g&&(v.jd.$b=y),n.push(v);l.sd&&o.push(l)}else i.push(l);f=i}s++}for(i=0;i<f.length;i++)l=f[i],l.sd&&o.push(l)}return{Ke:f}},e.d=function(t,r){var o;0<n.length?(o=n.pop(),o.face=t,o.jd=r,o.pc=null,o.cd=null):o=new e(t,r),t.jb.push(o);var i=r.$b;null!==i&&(i.cd=o),o.pc=i,r.$b=o};for(var n=Array(2e3),r=0;r<n.length;r++)n[r]=new e(null,null);for(var o=Array(1e3),r=0;r<o.length;r++)o[r]={ma:Array(3),C:Array(3),Oa:{x:0,y:0,z:0},jb:[],visible:!1}},X=new function(){function e(e,n,r,o,i,a,u,l){var c=(e-r)*(a-l)-(n-o)*(i-u);return Math.abs(c)<t?void 0:{x:((e*o-n*r)*(i-u)-(e-r)*(i*l-a*u))/c,y:((e*o-n*r)*(a-l)-(n-o)*(i*l-a*u))/c}}var t=1e-12;return this.cb=function(n,r){for(var o=n[0],i=o.x,a=o.y,u=o.x,l=o.y,c=n.length-1;0<c;c--)o=n[c],i=Math.min(i,o.x),a=Math.min(a,o.y),u=Math.max(u,o.x),l=Math.max(l,o.y);if(u-i<3*r||l-a<3*r)o=void 0;else{e:{for(o=!0,void 0==o&&(o=!1),i=[],a=n.length,u=0;u<=a;u++){var s,f,h,l=n[u%a],c=n[(u+1)%a],p=n[(u+2)%a];s=c.x-l.x,f=c.y-l.y,h=Math.sqrt(s*s+f*f);var d=r*s/h,b=r*f/h;if(s=p.x-c.x,f=p.y-c.y,h=Math.sqrt(s*s+f*f),s=r*s/h,f=r*f/h,(l=e(l.x-b,l.y+d,c.x-b,c.y+d,c.x-f,c.y+s,p.x-f,p.y+s))&&(i.push(l),p=i.length,o&&3<=p&&(l=i[p-3],c=i[p-2],p=i[p-1],0>(c.x-l.x)*(p.y-l.y)-(p.x-l.x)*(c.y-l.y)))){o=void 0;break e}}i.shift(),o=3>i.length?void 0:i}if(!o)e:{for(i=n.slice(0),o=0;o<n.length;o++){if(u=n[o%n.length],c=n[(o+1)%n.length],p=c.x-u.x,a=c.y-u.y,l=Math.sqrt(p*p+a*a),p=r*p/l,l=r*a/l,a=u.x-l,u=u.y+p,l=c.x-l,c=c.y+p,0!=i.length){for(b=a-l,f=u-c,p=[],s=h=!0,d=void 0,d=0;d<i.length;d++){var v=b*(u-i[d].y)-(a-i[d].x)*f;v<=t&&v>=-t&&(v=0),p.push(v),0<v&&(h=!1),0>v&&(s=!1)}if(h)i=[];else if(!s){for(b=[],d=0;d<i.length;d++)f=(d+1)%i.length,h=p[d],s=p[f],0<=h&&b.push(i[d]),(0<h&&0>s||0>h&&0<s)&&b.push(e(i[d].x,i[d].y,i[f].x,i[f].y,a,u,l,c));i=b}}if(3>i.length){o=void 0;break e}}o=i}}return o},this},J=new function(){function e(e){for(var t=e[0].x,n=e[0].y,r=t,o=n,i=1;i<e.length;i++)var a=e[i],t=Math.min(t,a.x),n=Math.min(n,a.y),r=Math.max(r,a.x),o=Math.max(o,a.y);return e=r-t,o-=n,[{x:t+2*e,y:n+2*o,f:0},{x:t+2*e,y:n-2*o,f:0},{x:t-2*e,y:n+2*o,f:0}]}var t=1e-12;this.S=function(n,r){if(1===n.length)n[0].o=r.slice(0),n[0].C=[];else{var o,i;i=e(r);var a,u=[];for(o=0;o<i.length;o++)a=i[o],u.push({x:a.x,y:a.y,z:a.x*a.x+a.y*a.y-a.f});for(o=0;o<n.length;o++)a=n[o],a.o=null,u.push({x:a.x,y:a.y,z:a.x*a.x+a.y*a.y-a.f});var l=K.S(u).Ke;for(function(){for(o=0;o<l.length;o++){var e=l[o],t=e.ma,n=t[0],r=t[1],i=t[2],t=n.x,a=n.y,n=n.z,u=r.x,c=r.y,r=r.z,s=i.x,f=i.y,i=i.z,h=t*(c-f)+u*(f-a)+s*(a-c);e.ha={x:-(a*(r-i)+c*(i-n)+f*(n-r))/h/2,y:-(n*(u-s)+r*(s-t)+i*(t-u))/h/2}}}(),function(e){for(o=0;o<l.length;o++){var t=l[o];t.tb=!V.Ta(e,t.ha)}}(r),u=function(e,t){var n,r=Array(t.length);for(n=0;n<r.length;n++)r[n]=[];for(n=0;n<e.length;n++){var o=e[n];if(!(0>o.Oa.z))for(var i=o.C,a=0;a<i.length;a++){var u=i[a];if(!(0>u.Oa.z)){var l=o.ma,c=l[(a+1)%3].index,l=l[a].index;2<c&&r[c-3].push([o,u,2<l?t[l-3]:null])}}}return r}(l,n),o=0;o<n.length;o++)if(a=u[o],0!==a.length){var c=n[o];a=function(e){var t=[e[0]],n=e[0][0],r=e[0][1],o=e.length,i=1;e:for(;i<o;i++)for(var a=1;a<o;a++){var u=e[a];if(null!==u){if(u[1]===n){if(t.unshift(u),n=u[0],e[a]=null,t.length===o)break e;continue}if(u[0]===r&&(t.push(u),r=u[1],e[a]=null,t.length===o))break e}}return t[0][0]!=t[o-1][1]&&t.push([t[o-1][1],t[0][0]]),t}(a);var s=a.length,f=-1;for(i=0;i<s;i++)a[i][0].tb&&(f=i);if(0<=f)(function(e,n,r,o){var i,a=[],u=[],l=r.length,c=n.length,s=0,f=-1,h=-1,p=-1,d=null,b=o;for(o=0;o<l;o++){var v=(b+1)%l,g=r[b][0],y=r[v][0];if(V.d(g.ha,y.ha)>t)if(g.tb&&y.tb){var m=[],x=[];for(i=0;i<c&&(f=(s+1)%c,!(d=V.za(n[s],n[f],g.ha,y.ha,!1))||(x.push(s),2!==m.push(d)));i++)s=f;if(2===m.length){if(f=m[1],d=V.d(g.ha,m[0]),f=V.d(g.ha,f),g=d<f?0:1,d=d<f?1:0,f=x[g],-1===h&&(h=f),-1!==p)for(;f!=p;)p=(p+1)%c,a.push(n[p]),u.push(null);a.push(m[g],m[d]),u.push(r[b][2],null),p=x[d]}}else if(g.tb&&!y.tb)for(i=0;i<c;i++){if(f=(s+1)%c,d=V.za(n[s],n[f],g.ha,y.ha,!1)){if(-1!==p)for(m=p;s!=m;)m=(m+1)%c,a.push(n[m]),u.push(null);a.push(d),u.push(r[b][2]),-1===h&&(h=s);break}s=f}else if(!g.tb&&y.tb)for(i=0;i<c;i++){if(f=(s+1)%c,d=V.za(n[s],n[f],g.ha,y.ha,!1)){a.push(g.ha,d),u.push(r[b][2],null),p=s;break}s=f}else a.push(g.ha),u.push(r[b][2]);b=v}if(0==a.length)u=a=null;else if(-1!==p)for(;h!=p;)p=(p+1)%c,a.push(n[p]),u.push(null);e.o=a,e.C=u})(c,r,a,f);else{var f=[],h=[];for(i=0;i<s;i++)V.d(a[i][0].ha,a[(i+1)%s][0].ha)>t&&(f.push(a[i][0].ha),h.push(a[i][2]));c.o=f,c.C=h}c.o&&3>c.o.length&&(c.o=null,c.C=null)}}},this.zc=function(t,n){var r,o,i=!1,a=t.length;for(o=0;o<a;o++)r=t[o],null===r.o&&(i=!0),r.pe=r.f;if(i){var u,l,i=e(n),c=[];for(o=t.length,r=0;r<i.length;r++)u=i[r],c.push({x:u.x,y:u.y,z:u.x*u.x+u.y*u.y});for(r=0;r<o;r++)u=t[r],c.push({x:u.x,y:u.y,z:u.x*u.x+u.y*u.y});for(u=K.S(c).Ke,i=Array(o),r=0;r<o;r++)i[r]={};for(c=u.length,r=0;r<c;r++)if(l=u[r],0<l.Oa.z){var s=l.ma,f=s.length;for(l=0;l<f-1;l++){var h=s[l].index-3,p=s[l+1].index-3;0<=h&&0<=p&&(i[h][p]=!0,i[p][h]=!0)}l=s[0].index-3,0<=p&&0<=l&&(i[p][l]=!0,i[l][p]=!0)}for(r=0;r<o;r++){l=i[r],u=t[r];var d,p=Number.MAX_VALUE,c=null;for(d in l)l=t[d],s=V.d(u,l),p>s&&(p=s,c=l);u.Tj=c,u.vf=Math.sqrt(p)}for(o=0;o<a;o++)r=t[o],d=Math.min(Math.sqrt(r.f),.95*r.vf),r.f=d*d;for(this.S(t,n),o=0;o<a;o++)r=t[o],r.pe!==r.f&&0<r.uc&&(d=Math.min(r.uc,r.pe-r.f),r.f+=d,r.uc-=d)}}},Y=new function(){this.Dg=function(e){e=e.e;for(var t=0,n=e.length,r=0;r<n;r++){var o=e[r];if(o.o){var i=o.x,a=o.y;V.k(o.o,o),i-=o.x,o=a-o.y,o=(0<i?i:-i)+(0<o?o:-o),t<o&&(t=o)}}return t},this.ya=function(e,t){var n,r,o,i,a=e.e;switch(t){case"random":return e.e[Math.floor(a.length*Math.random())];case"topleft":n=a[0];var u=n.x+n.y;for(i=1;i<a.length;i++)r=a[i],(o=r.x+r.y)<u&&(u=o,n=r);return n;case"bottomright":for(n=a[0],u=n.x+n.y,i=1;i<a.length;i++)r=a[i],(o=r.x+r.y)>u&&(u=o,n=r);return n;default:for(n=a[0],o=r=V.d(e,n),i=a.length-1;1<=i;i--)u=a[i],(r=V.d(e,u))<o&&(o=r,n=u);return n}},this.Ja=function(e,t,n){var r=e.e;if(r[0].C){var o=r.length;for(e=0;e<o;e++)r[e].kd=!1,r[e].ic=0;var i,a,o=[];for(a=i=0,o[i++]=t||r[0],t=t.ic=0;a<i;)if(r=o[a++],!r.kd&&r.C){n(r,t++,r.ic),r.kd=!0;var u=r.C,l=u.length;for(e=0;e<l;e++){var c=u[e];c&&!0!==c.kd&&(0===c.ic&&(c.ic=r.ic+1),o[i++]=c)}}}else for(e=0;e<r.length;e++)n(r[e],e,1)}},Z=function(){function e(e,l,s,p,d,b,v,x){var w=H.extend({},u,e);1>e.lineHeight&&(e.lineHeight=1),e=w.fontFamily;var C=w.fontStyle+" "+w.fontVariant+" "+w.fontWeight,T=w.rb,S=w.Yc,z=C+" "+e;w.Oe=z;var k={la:!1,mc:0,fontSize:0};if(l.save(),l.font=C+" "+m+"px "+e,l.textBaseline="middle",l.textAlign="center",t(l,w),s=s.trim(),g.text=s,o(p,d,b,y),/[\u3000-\u303f\u3040-\u309f\u30a0-\u30ff\uff00-\uff9f\u4e00-\u9faf\u3400-\u4dbf]/.test(s)?(r(g),n(l,g,z),i(w,g,y,S,T,!0,k)):(n(l,g,z),i(w,g,y,S,T,!1,k),!k.la&&(v&&(r(g),n(l,g,z)),x||v)&&(x&&(k.ec=!0),i(w,g,y,S,S,!0,k))),k.la){var M="",L=0,_=Number.MAX_VALUE,j=Number.MIN_VALUE;a(w,g,k.mc,k.fontSize,y,k.ec,function(e,t){0<M.length&&t===c&&(M+=c),M+=e},function(e,t,n,r,o){r===h&&(M+=f),l.save(),l.translate(b.x,t),e=k.fontSize/m,l.scale(e,e),l.fillText(M,0,0),l.restore(),M=n,L<o&&(L=o),_>t&&(_=t),j<t&&(j=t)}),k.da={x:b.x-L/2,y:_-k.fontSize/2,f:L,i:j-_+k.fontSize},l.restore()}else l.clear&&l.clear();return k}function t(e,t){var n=t.Oe,r=l[n];void 0===r&&(r={},l[n]=r),r[c]=e.measureText(c).width,r[s]=e.measureText(s).width}function n(e,t,n){var r,o=t.text.split(/(\n|[ \f\r\t\v\u2028\u2029]+|\u00ad+|\u200b+)/),i=[],a=[],u=o.length>>>1;for(r=0;r<u;r++)i.push(o[2*r]),a.push(o[2*r+1]);for(2*r<o.length&&(i.push(o[2*r]),a.push(void 0)),n=l[n],r=0;r<i.length;r++)o=i[r],void 0===(u=n[o])&&(u=e.measureText(o).width,n[o]=u);t.ld=i,t.lg=a}function r(e){for(var t=e.text.split(/\s+/),n=[],r={".":!0,",":!0,";":!0,"?":!0,"!":!0,":":!0,"。":!0},o=0;o<t.length;o++){var i=t[o];if(3<i.length){for(var a="",a=a+i.charAt(0),a=a+i.charAt(1),u=2;u<i.length-2;u++){var l=i.charAt(u);r[l]||(a+=p),a+=l}a+=p,a+=i.charAt(i.length-2),a+=i.charAt(i.length-1),n.push(a)}else n.push(i)}e.text=n.join(c)}function o(e,t,n,r){for(var o,i,a=0;a<e.length;a++)e[a].y===t.y&&(void 0===o?o=a:i=a);void 0===i&&(i=o),o!==i&&e[i].x<e[o].x&&(a=o,o=i,i=a),r.o=e,r.q=t,r.ud=n,r.tf=o,r.uf=i}function i(e,t,n,r,o,i,u){var l=e.lineHeight,c=Math.max(e.eb,.001),s=e.sb,f=t.ld,h=n.ud,p=n.q,d=void 0,b=void 0;switch(e.verticalAlign){case"top":h=p.y+p.i-h.y;break;case"bottom":h=h.y-p.y;break;default:h=2*Math.min(h.y-p.y,p.y+p.i-h.y)}if(0>=(s=Math.min(h,s*n.q.i)))u.la=!1;else{h=r,o=Math.min(o,s),p=Math.min(1,s/Math.max(20,t.ld.length));do{var v=(h+o)/2,g=Math.min(f.length,Math.floor((s+v*(l-1-2*c))/(v*l))),y=void 0;if(0<g)for(var m=1,x=g;;){var w=Math.floor((m+x)/2);if(a(e,t,w,v,n,i&&v===r&&w===g,null,null)){if(x=d=y=w,m===x)break}else if((m=w+1)>x)break}void 0!==y?h=b=v:o=v}while(o-h>p);void 0===b?(u.la=!1,u.fontSize=0):(u.la=!0,u.fontSize=b,u.mc=d,u.ec=i&&v===h)}}function a(e,t,n,r,o,i,a,u){var f=e.ob,h=r*(e.lineHeight-1),p=e.verticalAlign,g=Math.max(e.eb,.001);e=l[e.Oe];var y=t.ld;t=t.lg;var x,w=o.o,C=o.ud,T=o.tf,S=o.uf;switch(p){case"top":o=C.y+r/2+r*g,x=1;break;case"bottom":o=C.y-(r*n+h*(n-1))+r/2-r*g,x=-1;break;default:o=C.y-(r*(n-1)/2+h*(n-1)/2),x=1}for(p=o,g=0;g<n;g++)d[2*g]=o-r/2,d[2*g+1]=o+r/2,o+=x*r,o+=x*h;for(;b.length<d.length;)b.push(Array(2));g=d,o=2*n,x=b;for(var z=w.length,k=T,T=(T-1+z)%z,M=S,S=(S+1)%z,L=0;L<o;){for(var _=g[L],j=w[T];j.y<_;)k=T,T=(T-1+z)%z,j=w[T];for(var P=w[S];P.y<_;)M=S,S=(S+1)%z,P=w[S];var D=w[k],F=w[M],P=F.x+(P.x-F.x)*(_-F.y)/(P.y-F.y);x[L][0]=D.x+(j.x-D.x)*(_-D.y)/(j.y-D.y),x[L][1]=P,L++}for(g=0;g<n;g++)w=2*g,o=C.x,x=o-b[w][0],z=b[w][1]-o,x=x<z?x:z,z=o-b[w+1][0],w=b[w+1][1]-o,w=z<w?z:w,v[g]=2*(x<w?x:w)-f*r;for(k=e[c]*r/m,x=e[s]*r/m,f=0,T=v[f],C=0,w=void 0,g=0;g<y.length;g++){if(o=y[g],M=t[g],z=e[o]*r/m,C+z<T&&y.length-g>=n-f&&"\n"!=w)C+=z," "===M&&(C+=k),a&&a(o,w);else{if(z>T&&(f!==n-1||!i))return!1;if(f+1>=n)return!!i&&(n=T-C-x,(n>x||z>x)&&0<(n=Math.floor(o.length*n/z))&&a&&a(o.substring(0,n),w),a&&a(s,void 0),u&&u(f,p,o,w,C),!0);if(f++,u&&u(f,p,o,w,C),p+=r,p+=h,T=v[f],C=z," "===M&&(C+=k),z>T&&(f!==n||!i))return!1}w=M}return u&&u(f,p,void 0,void 0,C),!0}var u={rb:72,Yc:0,lineHeight:1.05,ob:1,eb:.5,sb:.9,fontFamily:"sans-serif",fontStyle:"normal",fontWeight:"normal",fontVariant:"normal",verticalAlign:"center"},l={},c=" ",s="…",f="‐",h="­",p="​",d=[],b=[],v=[],g={text:"",ld:void 0,lg:void 0},y={o:void 0,q:void 0,ud:void 0,tf:0,uf:0},m=100;return{Me:e,ye:function(t,n,r,o,i,a,u,l,c,s,f,h){var p,d=0,b=0;return r=r.toString().trim(),!h&&c.result&&r===c.sg&&Math.abs(s-c.ve)/s<=f&&(p=c.result,p.la&&(d=a.x-c.zg,b=a.y-c.Ag,f=c.hd,n.save(),n.translate(d,b),f.Sa(n),n.restore())),p||(f=c.hd,f.clear(),p=e(t,f,r,o,i,a,u,l),p.la&&f.Sa(n),c.ve=s,c.zg=a.x,c.Ag=a.y,c.result=p,c.sg=r),p.la?{la:!0,mc:p.mc,fontSize:p.fontSize,da:{x:p.da.x+d,y:p.da.y+b,f:p.da.f,i:p.da.i},ec:p.ec}:{la:!1}},xi:function(){return{ve:0,zg:0,Ag:0,result:void 0,hd:new O,sg:void 0}},Ea:u}}(),Q=new function(){function e(e,t){return function(r,o,i,a){function u(e,r,o,a,c){if(0!=e.length){var s,f,h,p,d=e.shift(),b=n(d);if(t(a,c)){s=r,h=b/a;do{b=d.shift(),f=b.vc,p=f/h,f=b;var v=o,g=h;f.x=s+p/2,f.y=v+g/2,i&&l(b,s,o,p,h),s+=p}while(0<d.length);return u(e,r,o+h,a,c-h)}s=o,p=b/c;do{b=d.shift(),f=b.vc,h=f/p,f=b,v=s,g=h,f.x=r+p/2,f.y=v+g/2,i&&l(b,r,s,p,h),s+=h}while(0<d.length);return u(e,r+p,o,a-p,c)}}function l(e,t,n,r,o){e.o=[{x:t,y:n},{x:t+r,y:n},{x:t+r,y:n+o},{x:t,y:n+o}]}var c=o.x,s=o.y,f=o.f;if(o=o.i,0!=r.length)if(1==r.length)r[0].x=c+f/2,r[0].y=s+o/2,r[0].Dd=0,i&&l(r[0],c,s,f,o);else{r=r.slice(0);for(var h=0,p=0;p<r.length;p++)h+=r[p].T;for(h=f*o/h,p=0;p<r.length;p++)r[p].vc=r[p].T*h;a=e(r,f,o,[[r.shift()]],a),u(a,c,s,f,o)}}}function t(e,t,r,o){function i(e){return Math.max(Math.pow(l*e/u,r),Math.pow(u/(l*e),o))}var a=n(e),u=a*a,l=t*t;for(t=i(e[0].vc),a=1;a<e.length;a++)t=Math.max(t,i(e[a].vc));return t}function n(e){for(var t=0,n=0;n<e.length;n++)t+=e[n].vc;return t}this.ue=e(function(e,r,o,i,a){a=Math.pow(2,a);for(var u=1/a,l=r<o;0<e.length;){var c=i[i.length-1],s=e.shift(),f=l?r:o,h=l?a:u,p=l?u:a,d=t(c,f,h,p);c.push(s),f=t(c,f,h,p),d<f&&(c.pop(),i.push([s]),l?o-=n(c)/r:r-=n(c)/o,l=r<o)}return i},function(e,t){return e<t}),this.Xb=e(function(e,n,r,o,i){function a(e){if(1<o.length){for(var r=o[o.length-1],i=o[o.length-2].slice(0),a=0;a<r.length;a++)i.push(r[a]);t(i,n,u,l)<e&&o.splice(-2,2,i)}}for(var u=Math.pow(2,i),l=1/u;0<e.length;){if(r=o[o.length-1],i=t(r,n,u,l),0==e.length)return;var c=e.shift();r.push(c);i<t(r,n,u,l)&&(r.pop(),a(i),o.push([c]))}return a(t(o[o.length-1],n,u,l)),o},function(){return!0})},$=new function(){var e=window.console;this.Pa=function(e){throw"FoamTree: "+e},this.info=function(t){e.info("FoamTree: "+t)},this.warn=function(t){e.warn("FoamTree: "+t)}},ee=new function(){this.Gg=function(e){e.beginPath(),e.moveTo(3.2,497),e.bezierCurveTo(.1,495.1,0,494.1,0,449.6),e.bezierCurveTo(0,403.5,-.1,404.8,4.1,402.6),e.bezierCurveTo(5.2,402,7.4,401.4,9,401.2),e.bezierCurveTo(10.6,401,31.2,400.6,54.7,400.2),e.bezierCurveTo(99.5,399.4,101,399.5,104.6,402.3),e.bezierCurveTo(107.9,404.9,107.6,404,129.3,473.2),e.bezierCurveTo(131,478.6,132.9,484.4,133.4,486.1),e.bezierCurveTo(135.2,491.4,135.4,494.9,134,496.4),e.bezierCurveTo(132.8,497.7,131.7,497.7,68.6,497.7),e.bezierCurveTo(24.2,497.7,4,497.5,3.2,497),e.closePath(),e.fillStyle="rgba(220,20,3,1)",e.fill(),e.beginPath(),e.moveTo(162.1,497),e.bezierCurveTo(159.5,496.3,157.7,494.6,156.2,491.6),e.bezierCurveTo(155.5,490.3,148.7,469.4,141.1,445.2),e.bezierCurveTo(126.1,397.5,125.6,395.4,128.1,389.8),e.bezierCurveTo(129.5,386.7,164.1,339,168,334.9),e.bezierCurveTo(170.3,332.5,172.2,332.1,175.1,333.7),e.bezierCurveTo(176.1,334.2,189.3,347,204.3,362.1),e.bezierCurveTo(229.4,387.4,231.8,390,233.5,394),e.bezierCurveTo(235.2,397.8,235.4,399.2,235.4,404.3),e.bezierCurveTo(235.3,415,230.5,489.9,229.8,492.5),e.bezierCurveTo(228.4,497.5,229.2,497.4,194.7,497.5),e.bezierCurveTo(177.8,497.6,163.1,497.4,162.1,497),e.closePath(),e.fillStyle="rgba(220,20,3,1)",e.fill(),e.beginPath(),e.moveTo(258.7,497),e.bezierCurveTo(255.8,496.1,252.6,492.3,252,489.1),e.bezierCurveTo(251.4,484.8,256.8,405.2,258.1,401.1),e.bezierCurveTo(260.4,393.4,262.7,391.1,300.4,359.2),e.bezierCurveTo(319.9,342.6,337.7,327.9,339.9,326.5),e.bezierCurveTo(347.4,321.6,350.4,321,372,320.5),e.bezierCurveTo(393.4,320,400.5,320.4,407.5,322.5),e.bezierCurveTo(413.9,324.4,487.4,359.5,490.6,362.1),e.bezierCurveTo(492,363.3,493.9,365.8,495,367.7),e.lineTo(496.8,371.2),e.lineTo(497,419.3),e.bezierCurveTo(497.1,445.7,497,468,496.8,468.8),e.bezierCurveTo(496.2,471.6,489.6,480.8,485,485.3),e.bezierCurveTo(478.6,491.7,474.9,494.1,468.2,496),e.lineTo(462.3,497.7),e.lineTo(361.6,497.7),e.bezierCurveTo(303.1,497.6,259.9,497.3,258.7,497),e.closePath(),e.fillStyle="rgba(200,200,200,1)",e.fill(),e.beginPath(),e.moveTo(4.4,380.8),e.bezierCurveTo(2.9,380.2,1.7,379.8,1.6,379.8),e.bezierCurveTo(1.5,379.8,1.2,378.8,.7,377.6),e.bezierCurveTo(.2,376.1,0,361.6,0,331.2),e.bezierCurveTo(0,281.2,-.2,283.1,4.9,280.9),e.bezierCurveTo(7.1,279.9,19.3,278.2,54.8,274.1),e.bezierCurveTo(80.6,271.1,102.9,268.6,104.4,268.6),e.bezierCurveTo(105.8,268.6,109.1,269.4,111.7,270.4),e.bezierCurveTo(116,272.1,117.2,273.2,133.4,289.3),e.bezierCurveTo(150.9,306.8,153.4,310,153.4,314.5),e.bezierCurveTo(153.4,317.6,151.1,321.3,136.4,341.2),e.bezierCurveTo(109.4,377.8,111.6,375.3,105.4,378.1),e.lineTo(101.3,380),e.lineTo(75.7,380.5),e.bezierCurveTo(6.8,381.8,7.3,381.8,4.4,380.8),e.closePath(),e.fillStyle="rgba(220,20,3,1)",e.fill(),e.beginPath(),e.moveTo(243.5,372.4),e.bezierCurveTo(240.2,370.8,136.6,266.7,134.2,262.6),e.bezierCurveTo(132.1,259,131.7,254.9,133.2,251.3),e.bezierCurveTo(134.5,248.2,166.3,206,169.3,203.4),e.bezierCurveTo(172.6,200.5,178.5,198.4,183.2,198.4),e.bezierCurveTo(187.1,198.4,275.2,204.1,281.6,204.8),e.bezierCurveTo(289.7,205.7,294.6,208.7,297.6,214.6),e.bezierCurveTo(300.5,220.3,327.4,297.4,327.8,301.1),e.bezierCurveTo(328.3,305.7,326.7,310.4,323.4,314),e.bezierCurveTo(322,315.6,307.8,327.9,291.9,341.3),e.bezierCurveTo(256.2,371.4,256.6,371.2,253.9,372.5),e.bezierCurveTo(251.1,373.9,246.5,373.9,243.5,372.4),e.closePath(),e.fillStyle="rgba(220,20,3,1)",e.fill(),e.beginPath(),e.moveTo(489.3,339.1),e.bezierCurveTo(488.6,338.9,473.7,331.9,456.3,323.6),e.bezierCurveTo(435.9,313.9,423.8,307.8,422.4,306.4),e.bezierCurveTo(419.5,303.7,418,300.2,418,296.1),e.bezierCurveTo(418,292.5,438,185,439.3,181.6),e.bezierCurveTo(441.2,176.6,445.5,173.1,450.8,172.1),e.bezierCurveTo(456,171.2,487.1,169.2,489.6,169.7),e.bezierCurveTo(493.1,170.3,495.5,171.9,497,174.7),e.bezierCurveTo(498.1,176.7,498.2,181.7,498.4,253.2),e.bezierCurveTo(498.5,295.3,498.4,330.9,498.2,332.5),e.bezierCurveTo(497.5,337.4,493.7,340.2,489.3,339.1),e.closePath(),e.fillStyle="rgba(220,20,3,1)",e.fill(),e.beginPath(),e.moveTo(353.2,300.7),e.bezierCurveTo(350.4,299.8,347.9,297.9,346.5,295.6),e.bezierCurveTo(345.8,294.5,338.2,273.7,329.6,249.5),e.bezierCurveTo(314.6,207.1,314.1,205.3,314.1,200.4),e.bezierCurveTo(314.1,196.7,314.4,194.6,315.3,193),e.bezierCurveTo(316,191.7,322.5,181.6,329.8,170.6),e.bezierCurveTo(346.8,144.8,345.4,145.8,365.8,144.4),e.bezierCurveTo(380.9,143.4,385.7,143.7,390.6,146.3),e.bezierCurveTo(397.3,149.8,417.4,164.4,419.2,167),e.bezierCurveTo(422.4,171.8,422.4,171.8,410.6,234.4),e.bezierCurveTo(402.3,278.6,399.3,293.2,398.1,295.3),e.bezierCurveTo(395.4,300.1,393.7,300.5,373,300.9),e.bezierCurveTo(363.1,301.1,354.2,301,353.2,300.7),e.closePath(),e.fillStyle="rgba(220,20,3,1)",e.fill(),e.beginPath(),e.moveTo(6.2,259.9),e.bezierCurveTo(4.9,259.2,3.2,257.8,2.4,256.8),e.bezierCurveTo(1,254.9,1,254.8,.8,148.7),e.bezierCurveTo(.7,74,.9,40.8,1.4,36.7),e.bezierCurveTo(2.3,29.6,4.7,24.4,9.8,18.3),e.bezierCurveTo(14.1,13.1,20.9,7.3,25,5.3),e.bezierCurveTo(26.5,4.6,31,3.3,34.9,2.6),e.bezierCurveTo(41.3,1.3,44.2,1.2,68.5,1.4),e.lineTo(95.1,1.6),e.lineTo(99,3.5),e.bezierCurveTo(101.2,4.6,103.9,6.6,105.2,8.1),e.bezierCurveTo(107.7,11,153.1,88.2,155.8,94),e.bezierCurveTo(159.1,101.4,159.6,104.7,159.5,121.6),e.bezierCurveTo(159.5,147.8,158.4,177.2,157.3,181),e.bezierCurveTo(156.8,182.8,155.6,186.1,154.6,188.1),e.bezierCurveTo(152.6,192.2,119.5,237.2,115.1,241.8),e.bezierCurveTo(112.1,244.9,106.3,248.3,102,249.4),e.bezierCurveTo(99.2,250.1,13,261.1,10.1,261.1),e.bezierCurveTo(9.2,261.1,7.5,260.6,6.2,259.9),e.closePath(),e.fillStyle="rgba(200,200,200,1)",e.fill(),e.beginPath(),e.moveTo(234.1,183.4),e.bezierCurveTo(180.2,179.7,182.3,180,179.5,174.5),e.lineTo(178,171.4),e.lineTo(178.7,142.4),e.bezierCurveTo(179.4,114.8,179.5,113.3,180.9,110.4),e.bezierCurveTo(183.5,105,182.7,105.2,237.9,95.3),e.bezierCurveTo(285.1,86.7,287.9,86.3,291,87.1),e.bezierCurveTo(292.8,87.6,295.3,88.8,296.7,89.9),e.bezierCurveTo(299.1,91.8,321.9,124.4,325,130.3),e.bezierCurveTo(326.9,134,327.2,139.1,325.7,142.6),e.bezierCurveTo(324.5,145.5,302.5,179.1,300.2,181.5),e.bezierCurveTo(297,184.9,293.5,186.3,287.4,186.5),e.bezierCurveTo(284.4,186.6,260.4,185.2,234.1,183.4),e.closePath(),e.fillStyle="rgba(220,20,3,1)",e.fill(),e.beginPath(),e.moveTo(435.8,153.4),e.bezierCurveTo(434.8,153.1,433,152.3,431.7,151.6),e.bezierCurveTo(428.4,150,410.1,137.1,407,134.4),e.bezierCurveTo(404.1,131.8,402.7,128.3,403.2,125.1),e.bezierCurveTo(403.6,122.9,420.3,81.3,423,75.9),e.bezierCurveTo(424.7,72.6,426.6,70.4,429.3,68.9),e.bezierCurveTo(431.1,67.9,435,67.7,462.2,67.6),e.lineTo(493.1,67.3),e.lineTo(495.4,69.6),e.bezierCurveTo(497,71.3,497.8,72.8,498.1,75),e.bezierCurveTo(498.4,76.6,498.5,92.9,498.4,111.1),e.bezierCurveTo(498.2,141.2,498.1,144.3,497,146.3),e.bezierCurveTo(494.8,150.3,493.3,150.6,470.3,152.4),e.bezierCurveTo(448.6,154,438.8,154.3,435.8,153.4),e.closePath(),e.fillStyle="rgba(220,20,3,1)",e.fill(),e.beginPath(),e.moveTo(346.6,125.3),e.bezierCurveTo(345,124.5,342.6,122.6,341.4,121),e.bezierCurveTo(337.1,115.7,313,79.8,311.6,76.7),e.bezierCurveTo(309.4,71.7,309.3,68,311.2,58.2),e.bezierCurveTo(319.2,16.9,321.3,7.1,322.4,5.2),e.bezierCurveTo(323.1,4,324.7,2.4,326,1.6),e.bezierCurveTo(328.3,.3,329.4,.3,353.9,.3),e.bezierCurveTo(379.2,.3,379.5,.3,382.4,1.8),e.bezierCurveTo(384,2.7,386,4.5,386.9,5.9),e.bezierCurveTo(388.6,8.6,405.1,46.3,407.2,52.2),e.bezierCurveTo(408.7,56.3,408.8,60.7,407.7,64.1),e.bezierCurveTo(407.3,65.4,402.2,78.2,396.3,92.7),e.bezierCurveTo(382.6,126.3,384.1,124.6,366.6,126),e.bezierCurveTo(353.4,127.1,350,127,346.6,125.3),e.closePath(),e.fillStyle="rgba(220,20,3,1)",e.fill(),e.beginPath(),e.moveTo(179.2,85.6),e.bezierCurveTo(175.7,84.6,171.9,82,170,79.2),e.bezierCurveTo(167.2,75.2,130.6,12.4,129.3,9.3),e.bezierCurveTo(128.2,6.7,128.1,5.9,128.8,4.2);e.bezierCurveTo(130.5,0,125.2,.3,211.7,0),e.bezierCurveTo(255.3,-.1,292.2,0,293.9,.3),e.bezierCurveTo(297.7,.8,301.1,4,301.8,7.6),e.bezierCurveTo(302.3,10.5,293.9,55.2,291.9,59.6),e.bezierCurveTo(290.4,63,286.1,66.9,282.3,68.3),e.bezierCurveTo(279.6,69.3,193.5,85.1,185.5,86.1),e.bezierCurveTo(183.8,86.3,181,86.1,179.2,85.6),e.closePath(),e.fillStyle="rgba(220,20,3,1)",e.fill(),e.beginPath(),e.moveTo(431.9,47.7),e.bezierCurveTo(428.7,46.9,426.4,45.2,424.6,42.3),e.bezierCurveTo(421.8,37.8,409.2,7.7,409.2,5.5),e.bezierCurveTo(409.2,1.2,408,1.3,451.6,1.3),e.bezierCurveTo(495,1.3,494,1.2,496.1,5.4),e.bezierCurveTo(497,7.2,497.2,10.2,497,25.5),e.lineTo(496.8,43.5),e.lineTo(494.9,45.4),e.lineTo(493,47.3),e.lineTo(474.8,47.7),e.bezierCurveTo(450.1,48.3,434.5,48.3,431.9,47.7),e.closePath(),e.fillStyle="rgba(200,200,200,1)",e.fill(),e.beginPath(),e.moveTo(1.3,511.9),e.lineTo(1.3,514.3),e.lineTo(3.7,514.3),e.bezierCurveTo(7.2,514.4,9.5,515.5,10.6,517.6),e.bezierCurveTo(11.7,519.8,12.1,522.7,12,526.3),e.lineTo(12,591),e.lineTo(22.8,591),e.lineTo(22.8,553.2),e.lineTo(49.9,553.2),e.lineTo(49.9,548.5),e.lineTo(22.8,548.5),e.lineTo(22.8,516.7),e.lineTo(41.9,516.7),e.bezierCurveTo(46.7,516.7,50.4,517.8,52.9,520),e.bezierCurveTo(55.5,522.2,56.8,525.7,56.8,530.5),e.lineTo(59.2,530.5),e.lineTo(59.2,521.5),e.bezierCurveTo(59.3,519,58.7,516.8,57.3,514.9),e.bezierCurveTo(55.9,513,53.1,512,49,511.9),e.closePath(),e.fillStyle="rgba(220,20,3,1)",e.fill(),e.beginPath(),e.moveTo(107.6,562.8),e.bezierCurveTo(107.6,569.9,106.2,575.7,103.5,580.3),e.bezierCurveTo(100.8,584.8,97.2,587.2,92.7,587.4),e.bezierCurveTo(88.1,587.2,84.5,584.8,81.8,580.3),e.bezierCurveTo(79.1,575.7,77.8,569.9,77.7,562.8),e.bezierCurveTo(77.8,555.8,79.1,550,81.8,545.4),e.bezierCurveTo(84.5,540.8,88.1,538.4,92.7,538.3),e.bezierCurveTo(97.2,538.4,100.8,540.8,103.5,545.4),e.bezierCurveTo(106.2,550,107.6,555.8,107.6,562.8),e.moveTo(66.3,562.8),e.bezierCurveTo(66.4,571.1,68.7,578,73.2,583.5),e.bezierCurveTo(77.8,589.1,84.2,591.9,92.7,592.1),e.bezierCurveTo(101.1,591.9,107.6,589.1,112.1,583.5),e.bezierCurveTo(116.7,578,118.9,571.1,119,562.8),e.bezierCurveTo(118.9,554.5,116.7,547.6,112.1,542.1),e.bezierCurveTo(107.6,536.6,101.1,533.7,92.7,533.5),e.bezierCurveTo(84.2,533.7,77.8,536.6,73.2,542.1),e.bezierCurveTo(68.7,547.6,66.4,554.5,66.3,562.8),e.closePath(),e.fillStyle="rgba(220,20,3,1)",e.fill(),e.beginPath(),e.moveTo(161.5,579.6),e.bezierCurveTo(160.3,581.4,158.9,583.1,157.2,584.5),e.bezierCurveTo(155.4,585.9,153.1,586.7,150.1,586.8),e.bezierCurveTo(147,586.8,144.4,585.9,142.2,584),e.bezierCurveTo(140,582.1,138.9,579.3,138.8,575.4),e.bezierCurveTo(138.8,571.7,140.5,568.9,143.8,566.7),e.bezierCurveTo(147.2,564.6,151.9,563.5,157.9,563.4),e.lineTo(161.5,563.4),e.moveTo(172.3,591),e.lineTo(172.3,558.6),e.bezierCurveTo(172.1,548.2,169.9,541.3,165.8,538),e.bezierCurveTo(161.7,534.7,156.9,533.2,151.3,533.5),e.bezierCurveTo(147.6,533.5,144.1,533.8,140.8,534.5),e.bezierCurveTo(137.4,535.1,135,536.2,133.4,537.7),e.bezierCurveTo(131.9,539.2,131.1,540.8,130.7,542.6),e.bezierCurveTo(130.4,544.4,130.3,546.4,130.4,548.5),e.lineTo(135.8,548.5),e.bezierCurveTo(136.7,544.6,138.3,542,140.5,540.5),e.bezierCurveTo(142.8,538.9,145.6,538.2,148.9,538.3),e.bezierCurveTo(152.6,538.1,155.6,539.4,157.9,542.2),e.bezierCurveTo(160.2,545,161.4,550.5,161.5,558.6),e.lineTo(157.9,558.6),e.bezierCurveTo(149.6,558.5,142.5,559.7,136.6,562.1),e.bezierCurveTo(130.7,564.5,127.6,568.9,127.4,575.4),e.bezierCurveTo(127.7,581.8,129.8,586.3,133.6,588.7),e.bezierCurveTo(137.4,591.1,141.1,592.3,144.7,592.1),e.bezierCurveTo(149.2,592.1,152.8,591.3,155.6,590),e.bezierCurveTo(158.3,588.6,160.3,587.1,161.5,585.6),e.lineTo(162.1,585.6),e.lineTo(166.3,591),e.closePath(),e.fillStyle="rgba(220,20,3,1)",e.fill(),e.beginPath(),e.moveTo(200.3,539.5),e.bezierCurveTo(199.8,538,198.7,536.8,197,536),e.bezierCurveTo(195.4,535.1,193.1,534.7,190.2,534.7),e.lineTo(179.4,534.7),e.lineTo(179.4,537.1),e.lineTo(181.8,537.1),e.bezierCurveTo(185.3,537.1,187.6,538.2,188.7,540.4),e.bezierCurveTo(189.8,542.5,190.3,545.4,190.2,549.1),e.lineTo(190.2,591),e.lineTo(200.9,591),e.lineTo(200.9,545.2),e.bezierCurveTo(202.4,543.5,204.2,542,206.2,540.8),e.bezierCurveTo(208.3,539.6,210.5,538.9,212.9,538.9),e.bezierCurveTo(215.9,538.8,218.3,540,219.9,542.5),e.bezierCurveTo(221.6,544.9,222.4,549.1,222.5,555),e.lineTo(222.5,591),e.lineTo(233.2,591),e.lineTo(233.2,555),e.bezierCurveTo(233.3,553.8,233.2,552.3,233.2,550.6),e.bezierCurveTo(233.1,549,232.9,547.6,232.6,546.7),e.bezierCurveTo(233.9,544.8,235.7,543,238,541.4),e.bezierCurveTo(240.4,539.8,242.7,539,245.2,538.9),e.bezierCurveTo(248.2,538.8,250.6,540,252.3,542.5),e.bezierCurveTo(253.9,544.9,254.8,549.1,254.8,555),e.lineTo(254.8,591),e.lineTo(265.6,591),e.lineTo(265.6,555),e.bezierCurveTo(265.4,546.5,263.8,540.8,260.6,537.8),e.bezierCurveTo(257.4,534.7,253.4,533.3,248.8,533.5),e.bezierCurveTo(245.4,533.5,242.2,534.2,238.9,535.7),e.bezierCurveTo(235.7,537.1,233,539.2,230.9,541.9),e.bezierCurveTo(229.3,538.6,227.3,536.4,224.8,535.2),e.bezierCurveTo(222.3,534,219.5,533.4,216.5,533.5),e.bezierCurveTo(212.9,533.6,209.8,534.2,207.1,535.4),e.bezierCurveTo(204.5,536.5,202.4,537.9,200.9,539.5),e.closePath(),e.fillStyle="rgba(220,20,3,1)",e.fill(),e.beginPath(),e.moveTo(284,511.9),e.bezierCurveTo(279.9,512,277.2,513,275.8,514.9),e.bezierCurveTo(274.4,516.8,273.7,519,273.8,521.5),e.lineTo(273.8,530.5),e.lineTo(276.2,530.5),e.bezierCurveTo(276.3,525.7,277.6,522.2,280.1,520),e.bezierCurveTo(282.7,517.8,286.4,516.7,291.2,516.7),e.lineTo(302,516.7),e.lineTo(302,590.9),e.lineTo(312.7,590.9),e.lineTo(312.7,516.7),e.lineTo(339.7,516.7),e.lineTo(339.7,511.9),e.closePath(),e.fillStyle="rgba(220,20,3,1)",e.fill(),e.beginPath(),e.moveTo(349.4,590.9),e.lineTo(360.2,590.9),e.lineTo(360.2,546.7),e.bezierCurveTo(361.4,544.8,363,543.4,364.9,542.3),e.bezierCurveTo(366.9,541.2,369.1,540.7,371.5,540.7),e.bezierCurveTo(373.7,540.7,375.5,541,377.2,541.6),e.bezierCurveTo(378.9,542.2,380.2,543.1,381.1,544.3),e.lineTo(385.9,540.7),e.bezierCurveTo(385.3,539.5,384.7,538.4,384,537.5),e.bezierCurveTo(383.4,536.6,382.6,535.9,381.7,535.3),e.bezierCurveTo(380.8,534.7,379.7,534.2,378.3,533.9),e.bezierCurveTo(377,533.6,375.8,533.5,374.5,533.5),e.bezierCurveTo(370.9,533.6,367.9,534.3,365.5,535.7),e.bezierCurveTo(363.2,537,361.4,538.5,360.2,540.1),e.lineTo(359.6,540.1),e.bezierCurveTo(359,538.3,357.9,536.9,356.3,536),e.bezierCurveTo(354.6,535.1,352.4,534.7,349.4,534.7),e.lineTo(339.8,534.7),e.lineTo(339.8,537.1),e.lineTo(341,537.1),e.bezierCurveTo(344.5,537.1,346.8,538.2,347.9,540.4),e.bezierCurveTo(349,542.5,349.5,545.4,349.4,549.1),e.closePath(),e.fillStyle="rgba(220,20,3,1)",e.fill(),e.beginPath(),e.moveTo(440.3,559.8),e.bezierCurveTo(440.3,551.4,438.3,544.9,434.4,540.4),e.bezierCurveTo(430.4,535.8,424.4,533.5,416.3,533.5),e.bezierCurveTo(408.8,533.7,403,536.6,399,542.1),e.bezierCurveTo(395,547.6,393,554.5,393,562.8),e.bezierCurveTo(393,571.1,395.1,578,399.3,583.5),e.bezierCurveTo(403.5,589.1,409.7,591.9,418.1,592.1),e.bezierCurveTo(422.6,592.2,426.7,591.2,430.2,589.2),e.bezierCurveTo(433.8,587.2,437,584,439.7,579.6),e.lineTo(437.3,577.8),e.bezierCurveTo(435.2,580.8,432.9,583.1,430.2,584.8),e.bezierCurveTo(427.6,586.5,424.4,587.3,420.5,587.4),e.bezierCurveTo(415.4,587.2,411.4,585.1,408.6,580.9);e.bezierCurveTo(405.8,576.8,404.4,571.3,404.4,564.6),e.lineTo(440,564.6),e.moveTo(404.4,559.8),e.bezierCurveTo(404.4,553.7,405.6,548.7,407.9,544.9),e.bezierCurveTo(410.3,541,413.3,539,416.9,538.9),e.bezierCurveTo(421.1,538.9,424.3,540.8,426.4,544.4),e.bezierCurveTo(428.4,548.1,429.5,553.2,429.5,559.8),e.closePath(),e.fillStyle="rgba(220,20,3,1)",e.fill(),e.beginPath(),e.moveTo(497.1,559.8),e.bezierCurveTo(497.1,551.4,495.1,544.9,491.2,540.4),e.bezierCurveTo(487.2,535.8,481.2,533.5,473.1,533.5),e.bezierCurveTo(465.6,533.7,459.9,536.6,455.9,542.1),e.bezierCurveTo(451.9,547.6,449.8,554.5,449.8,562.8),e.bezierCurveTo(449.8,571.1,451.9,578,456.1,583.5),e.bezierCurveTo(460.3,589.1,466.6,591.9,474.9,592.1),e.bezierCurveTo(479.4,592.2,483.5,591.2,487.1,589.2),e.bezierCurveTo(490.6,587.2,493.8,584,496.5,579.6),e.lineTo(494.1,577.8),e.bezierCurveTo(492,580.8,489.7,583.1,487.1,584.8),e.bezierCurveTo(484.4,586.5,481.2,587.3,477.3,587.4),e.bezierCurveTo(472.2,587.2,468.2,585.1,465.4,580.9),e.bezierCurveTo(462.6,576.8,461.2,571.3,461.2,564.6),e.lineTo(496.8,564.6),e.moveTo(461.2,559.8),e.bezierCurveTo(461.2,553.7,462.4,548.7,464.8,544.9),e.bezierCurveTo(467.1,541,470.1,539,473.7,538.9),e.bezierCurveTo(477.9,538.9,481.1,540.8,483.2,544.4),e.bezierCurveTo(485.3,548.1,486.3,553.2,486.3,559.8),e.closePath(),e.fillStyle="rgba(220,20,3,1)",e.fill()}};ee.yc={width:498,height:592};var te,ne,re,oe;D.Cd(function(){window.CarrotSearchFoamTree=function(e){function t(e,t){if(!u||u.exists(e))switch(e){case"selection":return s.cb();case"open":return s.Pa();case"exposure":return s.Ja();case"state":return s.ya.apply(this,t);case"geometry":return s.Ba.apply(this,t);case"hierarchy":return s.gb.apply(this,t);case"containerCoordinates":return s.Aa.apply(this,t);case"imageData":return s.S.apply(this,t);case"viewport":return s.za();case"times":return s.zc();case"onModelChanged":case"onRedraw":case"onRolloutStart":case"onRolloutComplete":case"onRelaxationStep":case"onGroupHover":case"onGroupOpenOrCloseChanging":case"onGroupExposureChanging":case"onGroupSelectionChanging":case"onGroupSelectionChanged":case"onGroupClick":case"onGroupDoubleClick":case"onGroupHold":var n=l[e];return Array.isArray(n)?n:[n];default:return l[e]}}function n(e){function t(e,t){return H.Q(n,e)?(t(n[e]),delete n[e],1):0}var n;if(0===arguments.length)return 0;1===arguments.length?n=H.extend({},arguments[0]):2===arguments.length&&(n={},n[arguments[0]]=arguments[1]),u&&u.validate(n,c.ni);var r=0;s&&(r+=t("selection",s.A),r+=t("open",s.k),r+=t("exposure",s.d));var i={};return H.Ga(n,function(e,t){(l[t]!==e||H.jc(e))&&(i[t]=e,r++),l[t]=e}),0<r&&o(i),r}function r(e,t){var n="on"+e.charAt(0).toUpperCase()+e.slice(1),r=l[n];l[n]=t(Array.isArray(r)?r:[r]),r={},r[n]=l[n],o(r)}function o(e){(function(){function t(t,n){return H.Q(e,t)||void 0===n?W.m(l[t],a):n}c.ni=l.logging,c.bc=l.dataObject,c.n=l.pixelRatio,c.xb=l.wireframePixelRatio,c.ab=l.stacking,c.dc=l.descriptionGroupType,c.Hc=l.descriptionGroupPosition,c.bh=l.descriptionGroupDistanceFromCenter,c.cc=l.descriptionGroupSize,c.De=l.descriptionGroupMinHeight,c.Ce=l.descriptionGroupMaxHeight,c.Ee=l.descriptionGroupPolygonDrawn,c.Vc=l.layout,c.lc=l.layoutByWeightOrder,c.tj=l.showZeroWeightGroups,c.We=l.groupMinDiameter,c.ce=l.rectangleAspectRatioPreference,c.fj=l.initializer||l.relaxationInitializer,c.gj=l.relaxationMaxDuration,c.de=l.relaxationVisible,c.bg=l.relaxationQualityThreshold,c.Rh=l.groupResizingBudget,c.zh=l.groupGrowingDuration,c.yh=l.groupGrowingDrag,c.Ah=l.groupGrowingEasing,c.jh=l.groupBorderRadius,c.lb=l.groupBorderWidth,c.Ra=l.groupBorderWidthScaling,c.zd=l.groupInsetWidth,c.kh=l.groupBorderRadiusCorrection,c.mb=l.groupStrokeWidth,c.Qc=l.groupSelectionOutlineWidth,c.Vh=l.groupSelectionOutlineColor,c.Ad=l.groupSelectionOutlineShadowSize,c.Xe=l.groupSelectionOutlineShadowColor,c.Sh=l.groupSelectionFillHueShift,c.Uh=l.groupSelectionFillSaturationShift,c.Th=l.groupSelectionFillLightnessShift,c.Ze=l.groupSelectionStrokeHueShift,c.af=l.groupSelectionStrokeSaturationShift,c.$e=l.groupSelectionStrokeLightnessShift,c.xh=l.groupFillType,c.th=l.groupFillGradientRadius,c.qh=l.groupFillGradientCenterHueShift,c.sh=l.groupFillGradientCenterSaturationShift,c.rh=l.groupFillGradientCenterLightnessShift,c.uh=l.groupFillGradientRimHueShift,c.wh=l.groupFillGradientRimSaturationShift,c.vh=l.groupFillGradientRimLightnessShift,c.Bd=l.groupStrokeType,c.mb=l.groupStrokeWidth,c.bf=l.groupStrokePlainHueShift,c.df=l.groupStrokePlainSaturationShift,c.cf=l.groupStrokePlainLightnessShift,c.$h=l.groupStrokeGradientRadius,c.Wh=l.groupStrokeGradientAngle,c.ai=l.groupStrokeGradientUpperHueShift,c.ci=l.groupStrokeGradientUpperSaturationShift,c.bi=l.groupStrokeGradientUpperLightnessShift,c.Xh=l.groupStrokeGradientLowerHueShift,c.Zh=l.groupStrokeGradientLowerSaturationShift,c.Yh=l.groupStrokeGradientLowerLightnessShift,c.Bh=l.groupHoverFillHueShift,c.Dh=l.groupHoverFillSaturationShift,c.Ch=l.groupHoverFillLightnessShift,c.Te=l.groupHoverStrokeHueShift,c.Ve=l.groupHoverStrokeSaturationShift,c.Ue=l.groupHoverStrokeLightnessShift,c.Va=l.groupExposureScale,c.ph=l.groupExposureShadowColor,c.Se=l.groupExposureShadowSize,c.Pc=l.groupExposureZoomMargin,c.ei=l.groupUnexposureLightnessShift,c.fi=l.groupUnexposureSaturationShift,c.di=l.groupUnexposureLabelColorThreshold,c.Ua=l.exposeDuration,c.gc=l.exposeEasing,c.bd=l.openCloseDuration,c.lh=W.m(l.groupColorDecorator,a),c.mh=l.groupColorDecorator!==H.ta,c.Gh=W.m(l.groupLabelDecorator,a),c.Hh=l.groupLabelDecorator!==H.ta,c.Mh=W.m(l.groupLabelLayoutDecorator,a),c.Nh=l.groupLabelLayoutDecorator!==H.ta,c.nh=W.m(l.groupContentDecorator,a),c.Oc=l.groupContentDecorator!==H.ta,c.oh=l.groupContentDecoratorTriggering,c.bj=l.rainbowStartColor,c.Vi=l.rainbowEndColor,c.Ti=l.rainbowColorDistribution,c.Ui=l.rainbowColorDistributionAngle,c.Yi=l.rainbowLightnessDistributionAngle,c.Zi=l.rainbowLightnessShift,c.$i=l.rainbowLightnessShiftCenter,c.aj=l.rainbowSaturationCorrection,c.Xi=l.rainbowLightnessCorrection,c.Zf=l.parentFillOpacity,c.vi=l.parentStrokeOpacity,c.$f=l.parentLabelOpacity,c.ag=l.parentOpacityBalancing,c.Qh=l.groupLabelUpdateThreshold,c.Ih=l.groupLabelFontFamily,c.Jh=l.groupLabelFontStyle,c.Kh=l.groupLabelFontVariant,c.Lh=l.groupLabelFontWeight,c.Ph=l.groupLabelMinFontSize,c.Pj=l.groupLabelMaxFontSize,c.Oj=l.groupLabelLineHeight,c.Nj=l.groupLabelHorizontalPadding,c.Rj=l.groupLabelVerticalPadding,c.Qj=l.groupLabelMaxTotalHeight,c.Fh=l.groupLabelDarkColor,c.Oh=l.groupLabelLightColor,c.Eh=l.groupLabelColorThreshold,c.Dj=l.wireframeDrawMaxDuration,c.Ej=l.wireframeLabelDrawing,c.Cj=l.wireframeContentDecorationDrawing,c.yg=l.wireframeToFinalFadeDuration,c.Fj=l.wireframeToFinalFadeDelay,c.gh=l.finalCompleteDrawMaxDuration,c.hh=l.finalIncrementalDrawMaxDuration,c.Ne=l.finalToWireframeFadeDuration,c.qd=l.androidStockBrowserWorkaround,c.ff=l.incrementalDraw,c.pi=l.maxGroups,c.Nd=l.maxGroupLevelsDrawn,c.oi=l.maxGroupLabelLevelsDrawn,c.he=l.rolloutStartPoint,c.ge=l.rolloutMethod,c.kj=l.rolloutEasing,c.fe=l.rolloutDuration,c.gg=l.rolloutScalingStrength,c.ig=l.rolloutTranslationXStrength,c.jg=l.rolloutTranslationYStrength,c.fg=l.rolloutRotationStrength,c.hg=l.rolloutTransformationCenter,c.oj=l.rolloutPolygonDrag,c.pj=l.rolloutPolygonDuration,c.lj=l.rolloutLabelDelay,c.mj=l.rolloutLabelDrag,c.nj=l.rolloutLabelDuration,c.jj=l.rolloutChildGroupsDrag,c.ij=l.rolloutChildGroupsDelay,c.Mi=l.pullbackStartPoint,c.Gi=l.pullbackMethod,c.Ci=l.pullbackEasing,c.Uj=l.pullbackType,c.Yd=l.pullbackDuration,c.Li=l.pullbackScalingStrength,c.Oi=l.pullbackTranslationXStrength,c.Pi=l.pullbackTranslationYStrength,c.Ki=l.pullbackRotationStrength,c.Ni=l.pullbackTransformationCenter,c.Hi=l.pullbackPolygonDelay,c.Ii=l.pullbackPolygonDrag,c.Ji=l.pullbackPolygonDuration,c.Di=l.pullbackLabelDelay,c.Ei=l.pullbackLabelDrag,c.Fi=l.pullbackLabelDuration,c.zi=l.pullbackChildGroupsDelay,c.Ai=l.pullbackChildGroupsDrag,c.Bi=l.pullbackChildGroupsDuration,c.Le=l.fadeDuration,c.fh=l.fadeEasing,c.Gj=l.zoomMouseWheelFactor,c.wc=l.zoomMouseWheelDuration,c.xc=l.zoomMouseWheelEasing,c.qi=l.maxLabelSizeForTitleBar,c.wj=l.titleBarFontFamily,c.tg=l.titleBarBackgroundColor,c.ug=l.titleBarTextColor,c.xj=l.titleBarMinFontSize,c.ne=l.titleBarMaxFontSize,c.yj=l.titleBarTextPaddingLeftRight,c.zj=l.titleBarTextPaddingTopBottom,c.vj=l.titleBarDecorator,c.Kj=l.attributionText,c.Hj=l.attributionLogo,c.Jj=l.attributionLogoScale,c.Lj=l.attributionUrl,c.we=l.attributionPosition,c.Sg=l.attributionDistanceFromCenter,c.Ug=l.attributionWeight,c.Tg=l.attributionTheme,c.hf=l.interactionHandler,c.Uf=t("onModelChanging",c.Uf),c.Tf=t("onModelChanged",c.Tf),c.Vf=t("onRedraw",c.Vf),c.Xf=t("onRolloutStart",c.Xf),c.Wf=t("onRolloutComplete",c.Wf),c.Sd=t("onRelaxationStep",c.Sd),c.Yf=t("onViewReset",c.Yf),c.Mf=t("onGroupOpenOrCloseChanging",c.Mf),c.Lf=t("onGroupOpenOrCloseChanged",c.Lf),c.Ef=t("onGroupExposureChanging",c.Ef),c.Df=t("onGroupExposureChanged",c.Df),c.Of=t("onGroupSelectionChanging",c.Of),c.Nf=t("onGroupSelectionChanged",c.Nf),c.Gf=t("onGroupHover",c.Gf),c.If=t("onGroupMouseMove",c.If);c.yf=t("onGroupClick",c.yf),c.zf=t("onGroupDoubleClick",c.zf),c.Ff=t("onGroupHold",c.Ff),c.Kf=t("onGroupMouseWheel",c.Kf),c.Jf=t("onGroupMouseUp",c.Jf),c.Hf=t("onGroupMouseDown",c.Hf),c.Cf=t("onGroupDragStart",c.Cf),c.Af=t("onGroupDrag",c.Af),c.Bf=t("onGroupDragEnd",c.Bf),c.Rf=t("onGroupTransformStart",c.Rf),c.Pf=t("onGroupTransform",c.Pf),c.Qf=t("onGroupTransformEnd",c.Qf),c.Sf=t("onKeyUp",c.Sf)})(),c.cj=I.Ba(c.bj),c.Wi=I.Ba(c.Vi),c.Ye=I.Ba(c.Xe),c.Ij=null,s&&(s.Wb(e),H.Q(e,"dataObject")&&s.reload())}function i(e){return function(){return e.apply(this,arguments).ih(a)}}var a=this,u=window.CarrotSearchFoamTree.asserts,l=H.extend({},window.CarrotSearchFoamTree.defaults),c={};n(e),(e=l.element||document.getElementById(l.id))||$.Pa("Element to embed FoamTree in not found."),l.element=e;var s=new _(e,c,l);s.H();var f={get:function(e){return 0===arguments.length?H.extend({},l):t(arguments[0],Array.prototype.slice.call(arguments,1))},set:n,on:function(e,t){r(e,function(e){return e.push(t),e})},off:function(e,t){r(e,function(e){return e.filter(function(e){return e!==t})})},resize:s.Y,redraw:s.Xb,update:s.update,select:i(s.A),expose:i(s.d),open:i(s.k),reset:i(s.reset),zoom:i(s.Ac),trigger:function(e,t){var n=s.Ta(e);n&&n(t)},dispose:function(){function e(){throw"FoamTree instance disposed"}s.kb(),H.Ga(f,function(t,n){"dispose"!==n&&(a[n]=e)})}};H.Ga(f,function(e,t){a[t]=e}),s.reload()},window["CarrotSearchFoamTree.asserts"]&&(window.CarrotSearchFoamTree.asserts=window["CarrotSearchFoamTree.asserts"],delete window["CarrotSearchFoamTree.asserts"]),window.CarrotSearchFoamTree.supported=!0,window.CarrotSearchFoamTree.version=j,window.CarrotSearchFoamTree.defaults=Object.freeze({id:void 0,element:void 0,logging:!1,dataObject:void 0,pixelRatio:1,wireframePixelRatio:1,layout:"relaxed",layoutByWeightOrder:!0,showZeroWeightGroups:!0,groupMinDiameter:10,rectangleAspectRatioPreference:-1,relaxationInitializer:"fisheye",relaxationMaxDuration:3e3,relaxationVisible:!1,relaxationQualityThreshold:1,stacking:"hierarchical",descriptionGroupType:"stab",descriptionGroupPosition:225,descriptionGroupDistanceFromCenter:1,descriptionGroupSize:.125,descriptionGroupMinHeight:35,descriptionGroupMaxHeight:.5,descriptionGroupPolygonDrawn:!1,maxGroups:5e4,maxGroupLevelsDrawn:4,maxGroupLabelLevelsDrawn:3,groupGrowingDuration:0,groupGrowingEasing:"bounce",groupGrowingDrag:0,groupResizingBudget:2,groupBorderRadius:.15,groupBorderWidth:4,groupBorderWidthScaling:.6,groupInsetWidth:6,groupBorderRadiusCorrection:1,groupSelectionOutlineWidth:5,groupSelectionOutlineColor:"#222",groupSelectionOutlineShadowSize:0,groupSelectionOutlineShadowColor:"#fff",groupSelectionFillHueShift:0,groupSelectionFillSaturationShift:0,groupSelectionFillLightnessShift:0,groupSelectionStrokeHueShift:0,groupSelectionStrokeSaturationShift:0,groupSelectionStrokeLightnessShift:-10,groupFillType:"gradient",groupFillGradientRadius:1,groupFillGradientCenterHueShift:0,groupFillGradientCenterSaturationShift:0,groupFillGradientCenterLightnessShift:20,groupFillGradientRimHueShift:0,groupFillGradientRimSaturationShift:0,groupFillGradientRimLightnessShift:-5,groupStrokeType:"plain",groupStrokeWidth:1.5,groupStrokePlainHueShift:0,groupStrokePlainSaturationShift:0,groupStrokePlainLightnessShift:-10,groupStrokeGradientRadius:1,groupStrokeGradientAngle:45,groupStrokeGradientUpperHueShift:0,groupStrokeGradientUpperSaturationShift:0,groupStrokeGradientUpperLightnessShift:20,groupStrokeGradientLowerHueShift:0,groupStrokeGradientLowerSaturationShift:0,groupStrokeGradientLowerLightnessShift:-20,groupHoverFillHueShift:0,groupHoverFillSaturationShift:0,groupHoverFillLightnessShift:20,groupHoverStrokeHueShift:0,groupHoverStrokeSaturationShift:0,groupHoverStrokeLightnessShift:-10,groupExposureScale:1.15,groupExposureShadowColor:"rgba(0, 0, 0, 0.5)",groupExposureShadowSize:50,groupExposureZoomMargin:.1,groupUnexposureLightnessShift:65,groupUnexposureSaturationShift:-65,groupUnexposureLabelColorThreshold:.35,exposeDuration:700,exposeEasing:"squareInOut",groupColorDecorator:H.ta,groupLabelDecorator:H.ta,groupLabelLayoutDecorator:H.ta,groupContentDecorator:H.ta,groupContentDecoratorTriggering:"onLayoutDirty",openCloseDuration:500,rainbowColorDistribution:"radial",rainbowColorDistributionAngle:-45,rainbowLightnessDistributionAngle:45,rainbowSaturationCorrection:.1,rainbowLightnessCorrection:.4,rainbowStartColor:"hsla(0, 100%, 55%, 1)",rainbowEndColor:"hsla(359, 100%, 55%, 1)",rainbowLightnessShift:30,rainbowLightnessShiftCenter:.4,parentFillOpacity:.7,parentStrokeOpacity:1,parentLabelOpacity:1,parentOpacityBalancing:!0,wireframeDrawMaxDuration:15,wireframeLabelDrawing:"auto",wireframeContentDecorationDrawing:"auto",wireframeToFinalFadeDuration:500,wireframeToFinalFadeDelay:300,finalCompleteDrawMaxDuration:80,finalIncrementalDrawMaxDuration:100,finalToWireframeFadeDuration:200,androidStockBrowserWorkaround:D.jf(),incrementalDraw:"fast",groupLabelFontFamily:"sans-serif",groupLabelFontStyle:"normal",groupLabelFontWeight:"normal",groupLabelFontVariant:"normal",groupLabelLineHeight:1.05,groupLabelHorizontalPadding:1,groupLabelVerticalPadding:1,groupLabelMinFontSize:6,groupLabelMaxFontSize:160,groupLabelMaxTotalHeight:.9,groupLabelUpdateThreshold:.05,groupLabelDarkColor:"#000",groupLabelLightColor:"#fff",groupLabelColorThreshold:.35,rolloutStartPoint:"center",rolloutEasing:"squareOut",rolloutMethod:"groups",rolloutDuration:2e3,rolloutScalingStrength:-.7,rolloutTranslationXStrength:0,rolloutTranslationYStrength:0,rolloutRotationStrength:-.7,rolloutTransformationCenter:.7,rolloutPolygonDrag:.1,rolloutPolygonDuration:.5,rolloutLabelDelay:.8,rolloutLabelDrag:.1,rolloutLabelDuration:.5,rolloutChildGroupsDrag:.1,rolloutChildGroupsDelay:.2,pullbackStartPoint:"center",pullbackEasing:"squareIn",pullbackMethod:"groups",pullbackDuration:1500,pullbackScalingStrength:-.7,pullbackTranslationXStrength:0,pullbackTranslationYStrength:0,pullbackRotationStrength:-.7,pullbackTransformationCenter:.7,pullbackPolygonDelay:.3,pullbackPolygonDrag:.1,pullbackPolygonDuration:.8,pullbackLabelDelay:0,pullbackLabelDrag:.1,pullbackLabelDuration:.3,pullbackChildGroupsDelay:.1,pullbackChildGroupsDrag:.1,pullbackChildGroupsDuration:.3,fadeDuration:700,fadeEasing:"cubicInOut",zoomMouseWheelFactor:1.5,zoomMouseWheelDuration:500,zoomMouseWheelEasing:"squareOut",maxLabelSizeForTitleBar:8,titleBarFontFamily:null,titleBarFontStyle:"normal",titleBarFontWeight:"normal",titleBarFontVariant:"normal",titleBarBackgroundColor:"rgba(0, 0, 0, 0.5)",titleBarTextColor:"rgba(255, 255, 255, 1)",titleBarMinFontSize:10,titleBarMaxFontSize:40,titleBarTextPaddingLeftRight:20,titleBarTextPaddingTopBottom:15,titleBarDecorator:H.ta,attributionText:null,attributionLogo:null,attributionLogoScale:.5,attributionUrl:"http://carrotsearch.com/foamtree",attributionPosition:"bottom-right",attributionDistanceFromCenter:1,attributionWeight:.025,attributionTheme:"light",interactionHandler:D.ii()?"hammerjs":"builtin",onModelChanging:[],onModelChanged:[],onRedraw:[],onRolloutStart:[],onRolloutComplete:[],onRelaxationStep:[],onViewReset:[],onGroupOpenOrCloseChanging:[],onGroupOpenOrCloseChanged:[],onGroupExposureChanging:[],onGroupExposureChanged:[],onGroupSelectionChanging:[],onGroupSelectionChanged:[],onGroupHover:[],onGroupMouseMove:[],onGroupClick:[],onGroupDoubleClick:[],onGroupHold:[],onGroupMouseWheel:[],onGroupMouseUp:[],onGroupMouseDown:[],onGroupDragStart:[],onGroupDrag:[],onGroupDragEnd:[],onGroupTransformStart:[],onGroupTransform:[],onGroupTransformEnd:[],onKeyUp:[],selection:null,open:null,exposure:null,imageData:null,hierarchy:null,geometry:null,containerCoordinates:null,state:null,viewport:null,times:null}),window.CarrotSearchFoamTree.geometry=Object.freeze(function(){return{rectangleInPolygon:function(e,t,n,r,o,i,a){return o=H.B(o,1),i=H.B(i,.5),a=H.B(a,.5),e=V.te(e,{x:t,y:n},r,i,a)*o,{x:t-e*r*i,y:n-e*a,w:e*r,h:e}},circleInPolygon:function(e,t,n){return V.Eg(e,{x:t,y:n})},stabPolygon:function(e,t,n,r){return V.Wb(e,{x:t,y:n},r)},polygonCentroid:function(e){return e=V.k(e,{}),{x:e.x,y:e.y,area:e.ja}},boundingBox:function(e){for(var t=e[0].x,n=e[0].y,r=e[0].x,o=e[0].y,i=1;i<e.length;i++){var a=e[i];a.x<t&&(t=a.x),a.y<n&&(n=a.y),a.x>r&&(r=a.x),a.y>o&&(o=a.y)}return{x:t,y:n,w:r-t,h:o-n}}}}())},function(){window.CarrotSearchFoamTree=function(){window.console.error("FoamTree is not supported on this browser.")},window.CarrotSearchFoamTree.Wj=!1})})(),e.exports=CarrotSearchFoamTree},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}function o(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function u(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var l=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),c=n(0),s=n(3),f=r(s),h=n(15),p=r(h),d=function(e){function t(e){i(this,t);var n=a(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return n.saveNode=function(e){return n.node=e},n.onMouseMove=function(e){Object.assign(n.mouseCoords,{x:e.pageX,y:e.pageY}),n.props.visible&&n.updatePosition()},n.mouseCoords={x:0,y:0},n.state={left:0,top:0},n}return u(t,e),l(t,[{key:"componentDidMount",value:function(){document.addEventListener("mousemove",this.onMouseMove,!1)}},{key:"shouldComponentUpdate",value:function(e){return this.props.visible||e.visible}},{key:"componentWillUnmount",value:function(){document.removeEventListener("mousemove",this.onMouseMove)}},{key:"render",value:function(){var e,t=this.props,n=t.children,r=t.visible,i=(0,f.default)((e={},o(e,p.default.container,!0),o(e,p.default.hidden,!r),e));return(0,c.h)("div",{ref:this.saveNode,className:i,style:this.getStyle()},n)}},{key:"getStyle",value:function(){return{left:this.state.left,top:this.state.top}}},{key:"updatePosition",value:function(){if(this.props.visible){var e={left:this.mouseCoords.x+t.marginX,top:this.mouseCoords.y+t.marginY},n=this.node.getBoundingClientRect();e.left+n.width>window.innerWidth&&(e.left=window.innerWidth-n.width),e.top+n.height>window.innerHeight&&(e.top=this.mouseCoords.y-t.marginY-n.height),this.setState(e)}}}]),t}(c.Component);d.marginX=10,d.marginY=30,t.default=d},function(e,t,n){var r=n(16);"string"==typeof r&&(r=[[e.i,r,""]]);var o={hmr:!0};o.transform=void 0,o.insertInto=void 0;n(2)(r,o);r.locals&&(e.exports=r.locals)},function(e,t,n){t=e.exports=n(1)(!1),t.push([e.i,".Tooltip__container{font:normal 10px Verdana;position:absolute;padding:5px 10px;border-radius:4px;background:#fff;border:1px solid #aaa;opacity:.7;white-space:nowrap;visibility:visible;transition:opacity .2s ease,visibility .2s ease}.Tooltip__hidden{opacity:0;visibility:hidden}",""]),t.locals={container:"Tooltip__container",hidden:"Tooltip__hidden"}},function(e,t){e.exports=function(e){var t="undefined"!=typeof window&&window.location;if(!t)throw new Error("fixUrls requires window.location");if(!e||"string"!=typeof e)return e;var n=t.protocol+"//"+t.host,r=n+t.pathname.replace(/\/[^\/]*$/,"/");return e.replace(/url\s*\(((?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)\)/gi,function(e,t){var o=t.trim().replace(/^"(.*)"$/,function(e,t){return t}).replace(/^'(.*)'$/,function(e,t){return t});if(/^(#|data:|http:\/\/|https:\/\/|file:\/\/\/)/i.test(o))return e;var i;return i=0===o.indexOf("//")?o:0===o.indexOf("/")?n+o:r+o.replace(/^\.\//,""),"url("+JSON.stringify(i)+")"})}},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function a(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var u=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),l=n(0),c=n(19),s=r(c),f=n(4),h=r(f),p=function(e){function t(){return o(this,t),i(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return a(t,e),u(t,[{key:"render",value:function(){var e=this.props,t=e.label,n=e.items,r=e.activeItem,o=e.onSwitch;return(0,l.h)("div",{className:h.default.container},(0,l.h)("div",{className:h.default.label},t,":"),(0,l.h)("div",null,n.map(function(e){return(0,l.h)(s.default,{key:e.label,item:e,active:e===r,onClick:o})})))}}]),t}(l.Component);t.default=p},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}function o(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function u(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var l=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),c=n(0),s=n(3),f=r(s),h=n(4),p=r(h),d=function(e){function t(){var e,n,r,o;i(this,t);for(var u=arguments.length,l=Array(u),c=0;c<u;c++)l[c]=arguments[c];return n=r=a(this,(e=t.__proto__||Object.getPrototypeOf(t)).call.apply(e,[this].concat(l))),r.handleClick=function(){r.props.onClick&&r.props.onClick(r.props.item)},o=n,a(r,o)}return u(t,e),l(t,[{key:"render",value:function(){var e,t=this.props,n=t.item,r=t.active,i=(0,f.default)((e={},o(e,p.default.item,!0),o(e,p.default.active,r),e));return(0,c.h)("span",{className:i,onClick:this.handleClick},n.label)}}]),t}(c.Component);t.default=d},function(e,t,n){t=e.exports=n(1)(!1),t.push([e.i,".Switcher__container{font:normal 10px Verdana;white-space:nowrap}.Switcher__label{font-weight:700;font-size:11px;margin-bottom:7px}.Switcher__item{display:inline-block;border:1px solid #aaa;border-radius:4px;padding:5px 7px;cursor:pointer;transition:background .3s ease}.Switcher__item:hover{background:rgba(255,165,0,.15)}.Switcher__item.Switcher__active{cursor:default;background:orange}.Switcher__item+.Switcher__item{margin-left:5px}",""]),t.locals={container:"Switcher__container",label:"Switcher__label",item:"Switcher__item",active:"Switcher__active"}},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}function o(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function u(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var l=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),c=n(0),s=n(3),f=r(s),h=n(22),p=r(h),d=function(e){function t(){var e,n,r,o;i(this,t);for(var u=arguments.length,l=Array(u),c=0;c<u;c++)l[c]=arguments[c];return n=r=a(this,(e=t.__proto__||Object.getPrototypeOf(t)).call.apply(e,[this].concat(l))),r.state={visible:!0,renderContent:!0},r.handleMouseEnter=function(){clearTimeout(r.hideTimeoutId),r.toggleVisibility(!0)},r.handleMouseLeave=function(){return r.toggleVisibility(!1)},o=n,a(r,o)}return u(t,e),l(t,[{key:"componentDidMount",value:function(){var e=this;this.hideTimeoutId=setTimeout(function(){return e.toggleVisibility(!1)},1500),this.hideContentTimeout=null}},{key:"componentWillUnmount",value:function(){clearTimeout(this.hideTimeoutId),clearTimeout(this.hideContentTimeout)}},{key:"render",value:function(){var e,t=this.props,n=t.position,r=t.children,i=this.state,a=i.visible,u=i.renderContent,l=(0,f.default)((e={},o(e,p.default.container,!0),o(e,p.default.left,"left"===n),o(e,p.default.hidden,!a),e));return(0,c.h)("div",{className:l,onMouseEnter:this.handleMouseEnter,onMouseLeave:this.handleMouseLeave},u?r:null)}},{key:"toggleVisibility",value:function(e){var t=this;clearTimeout(this.hideContentTimeout),this.setState({visible:e}),e?this.setState({renderContent:!0}):this.hideContentTimeout=setTimeout(function(){return t.setState({renderContent:!1})},500)}}]),t}(c.Component);d.defaultProps={position:"left"},t.default=d},function(e,t,n){var r=n(23);"string"==typeof r&&(r=[[e.i,r,""]]);var o={hmr:!0};o.transform=void 0,o.insertInto=void 0;n(2)(r,o);r.locals&&(e.exports=r.locals)},function(e,t,n){t=e.exports=n(1)(!1),t.push([e.i,".Sidebar__container{background:#fff;border:none;border-right:1px solid #aaa;bottom:0;opacity:.95;overflow-y:auto;padding:20px;position:absolute;top:0;transition:transform .2s ease;z-index:1}.Sidebar__container.Sidebar__left{left:0}.Sidebar__container.Sidebar__left.Sidebar__hidden{transform:translateX(calc(-100% + 7px))}",""]),t.locals={container:"Sidebar__container",left:"Sidebar__left",hidden:"Sidebar__hidden"}},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function a(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var u=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),l=n(0),c=n(5),s=r(c),f=n(6),h=r(f),p=function(e){function t(){var e,n,r,a;o(this,t);for(var u=arguments.length,l=Array(u),c=0;c<u;c++)l[c]=arguments[c];return n=r=i(this,(e=t.__proto__||Object.getPrototypeOf(t)).call.apply(e,[this].concat(l))),r.handleChange=function(){r.props.onChange(r.props.item)},a=n,i(r,a)}return a(t,e),u(t,[{key:"render",value:function(){var e=this.props.checked;return(0,l.h)("label",{className:h.default.item},(0,l.h)("input",{className:h.default.checkbox,type:"checkbox",checked:e,onChange:this.handleChange}),this.renderLabel())}},{key:"renderLabel",value:function(){var e=this.props,t=e.children,n=e.item;return t&&t.length?t[0](n,h.default.itemText):(0,l.h)("span",{className:h.default.itemText},n===s.default.ALL_ITEM?"All":n.label)}}]),t}(l.Component);t.default=p},function(e,t,n){t=e.exports=n(1)(!1),t.push([e.i,".CheckboxList__container{font:normal 10px Verdana;white-space:nowrap}.CheckboxList__label{font-size:11px;font-weight:700;margin-bottom:7px}.CheckboxList__checkbox{cursor:pointer}.CheckboxList__item{cursor:pointer;display:block;margin-bottom:3px}.CheckboxList__itemText{margin-left:3px;position:relative;top:-2px;vertical-align:middle}",""]),t.locals={container:"CheckboxList__container",label:"CheckboxList__label",checkbox:"CheckboxList__checkbox",item:"CheckboxList__item",itemText:"CheckboxList__itemText"}},function(e,t,n){var r=n(27);"string"==typeof r&&(r=[[e.i,r,""]]);var o={hmr:!0};o.transform=void 0,o.insertInto=void 0;n(2)(r,o);r.locals&&(e.exports=r.locals)},function(e,t,n){t=e.exports=n(1)(!1),t.push([e.i,".ModulesTreemap__container,.ModulesTreemap__map{position:relative;width:100%;height:100%}.ModulesTreemap__sidebarGroup{margin-bottom:20px}.ModulesTreemap__activeSize{font-weight:700}",""]),t.locals={container:"ModulesTreemap__container",map:"ModulesTreemap__map",sidebarGroup:"ModulesTreemap__sidebarGroup",activeSize:"ModulesTreemap__activeSize"}},function(e,t,n){var r=n(29);"string"==typeof r&&(r=[[e.i,r,""]]);var o={hmr:!0};o.transform=void 0,o.insertInto=void 0;n(2)(r,o);r.locals&&(e.exports=r.locals)},function(e,t,n){t=e.exports=n(1)(!1),t.push([e.i,"#app,body,html{width:100%;height:100%;padding:0;margin:0;overflow:hidden}",""])}]);
//# sourceMappingURL=viewer.js.map
  </script>


  </head>

  <body>
    <div id="app"></div>
    <script>
      window.chartData = [{"label":"static/js/0.7462f056d000ee6455a6.*************.y9bzfe.js","statSize":2066760,"parsedSize":1657668,"gzipSize":410736,"groups":[{"label":"src","path":"./src","statSize":560650,"groups":[{"label":"assets","path":"./src/assets","statSize":1710,"groups":[{"label":"newtemp","path":"./src/assets/newtemp","statSize":426,"groups":[{"id":"+diz","label":"23.png","path":"./src/assets/newtemp/23.png","statSize":71,"parsedSize":58,"gzipSize":78},{"id":"1mwt","label":"25.png","path":"./src/assets/newtemp/25.png","statSize":71,"parsedSize":58,"gzipSize":78},{"id":"7Mcs","label":"20.png","path":"./src/assets/newtemp/20.png","statSize":71,"parsedSize":58,"gzipSize":78},{"id":"FNEw","label":"24.png","path":"./src/assets/newtemp/24.png","statSize":71,"parsedSize":58,"gzipSize":78},{"id":"fgtD","label":"22.png","path":"./src/assets/newtemp/22.png","statSize":71,"parsedSize":58,"gzipSize":78},{"id":"lZFd","label":"19.png","path":"./src/assets/newtemp/19.png","statSize":71,"parsedSize":58,"gzipSize":78}],"parsedSize":348,"gzipSize":131},{"label":"img","path":"./src/assets/img","statSize":769,"groups":[{"id":"0YUB","label":"back.png","path":"./src/assets/img/back.png","statSize":73,"parsedSize":60,"gzipSize":80},{"id":"2Uc1","label":"liucheng.png","path":"./src/assets/img/liucheng.png","statSize":77,"parsedSize":64,"gzipSize":84},{"id":"5+53","label":"recharge_icon.png","path":"./src/assets/img/recharge_icon.png","statSize":82,"parsedSize":69,"gzipSize":88},{"id":"7vTl","label":"kefu.png","path":"./src/assets/img/kefu.png","statSize":73,"parsedSize":60,"gzipSize":80},{"id":"CawD","label":"youjiantou.png","path":"./src/assets/img/youjiantou.png","statSize":79,"parsedSize":66,"gzipSize":86},{"id":"UrNP","label":"xiane.png","path":"./src/assets/img/xiane.png","statSize":74,"parsedSize":61,"gzipSize":81},{"id":"owqv","label":"zuojiantou.png","path":"./src/assets/img/zuojiantou.png","statSize":79,"parsedSize":66,"gzipSize":86},{"id":"wPqx","label":"LOGO2.png","path":"./src/assets/img/LOGO2.png","statSize":74,"parsedSize":61,"gzipSize":81},{"id":"yGxA","label":"jinggao.png","path":"./src/assets/img/jinggao.png","statSize":76,"parsedSize":63,"gzipSize":82},{"id":"yt1j","label":"withdrew_icon.png","path":"./src/assets/img/withdrew_icon.png","statSize":82,"parsedSize":69,"gzipSize":88}],"parsedSize":639,"gzipSize":232},{"label":"ico","path":"./src/assets/ico","statSize":218,"groups":[{"id":"2/va","label":"Chinese.png","path":"./src/assets/ico/Chinese.png","statSize":76,"parsedSize":63,"gzipSize":83},{"id":"XHQl","label":"tw.png","path":"./src/assets/ico/tw.png","statSize":71,"parsedSize":58,"gzipSize":77},{"id":"yBcX","label":"dz.png","path":"./src/assets/ico/dz.png","statSize":71,"parsedSize":58,"gzipSize":78}],"parsedSize":179,"gzipSize":110},{"label":"images","path":"./src/assets/images","statSize":75,"groups":[{"label":"qiquan26","path":"./src/assets/images/qiquan26","statSize":75,"groups":[{"id":"I/iC","label":"anquan.png","path":"./src/assets/images/qiquan26/anquan.png","statSize":75,"parsedSize":62,"gzipSize":82}],"parsedSize":62,"gzipSize":82}],"parsedSize":62,"gzipSize":82},{"label":"home","path":"./src/assets/home","statSize":151,"groups":[{"id":"QQ3S","label":"logo.png","path":"./src/assets/home/<USER>","statSize":73,"parsedSize":60,"gzipSize":80},{"id":"RqRP","label":"icon_talk.png","path":"./src/assets/home/<USER>","statSize":78,"parsedSize":65,"gzipSize":85}],"parsedSize":125,"gzipSize":114},{"label":"me","path":"./src/assets/me","statSize":71,"groups":[{"id":"mIyj","label":"qm.png","path":"./src/assets/me/qm.png","statSize":71,"parsedSize":58,"gzipSize":78}],"parsedSize":58,"gzipSize":78}],"parsedSize":1411,"gzipSize":350},{"label":"page","path":"./src/page","statSize":537344,"groups":[{"label":"user","path":"./src/page/user","statSize":141573,"groups":[{"id":"+pLN","label":"Warehouse.vue","path":"./src/page/user/Warehouse.vue","statSize":82,"parsedSize":30,"gzipSize":38},{"label":"compontents","path":"./src/page/user/compontents","statSize":205,"groups":[{"id":"0vLY","label":"recharge-list.vue","path":"./src/page/user/compontents/recharge-list.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"DPkl","label":"capital-all.vue","path":"./src/page/user/compontents/capital-all.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"TZV8","label":"list-detail.vue","path":"./src/page/user/compontents/list-detail.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"Yn3f","label":"notify-list.vue","path":"./src/page/user/compontents/notify-list.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"zxAT","label":"cash-list.vue","path":"./src/page/user/compontents/cash-list.vue","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":75,"gzipSize":38},{"id":"13+b","label":"cashlist.vue + 5 modules","path":"./src/page/user/cashlist.vue + 5 modules","statSize":13807,"parsedSize":5651,"gzipSize":1896},{"id":"1sVn","label":"Warehouse.vue + 2 modules","path":"./src/page/user/Warehouse.vue + 2 modules","statSize":31884,"parsedSize":14230,"gzipSize":2930},{"id":"7RKB","label":"my.vue + 5 modules","path":"./src/page/user/my.vue + 5 modules","statSize":16190,"parsedSize":7180,"gzipSize":2563},{"id":"7rTs","label":"notify.vue + 5 modules","path":"./src/page/user/notify.vue + 5 modules","statSize":9950,"parsedSize":3420,"gzipSize":1399},{"id":"7yfP","label":"addCard.vue + 2 modules","path":"./src/page/user/addCard.vue + 2 modules","statSize":7181,"parsedSize":3144,"gzipSize":1434},{"id":"C07P","label":"rechargelist.vue + 5 modules","path":"./src/page/user/rechargelist.vue + 5 modules","statSize":9738,"parsedSize":3071,"gzipSize":1301},{"id":"D/r/","label":"authentication.vue + 2 modules","path":"./src/page/user/authentication.vue + 2 modules","statSize":7630,"parsedSize":4580,"gzipSize":1685},{"id":"FxOn","label":"recharge-sure.vue + 2 modules","path":"./src/page/user/recharge-sure.vue + 2 modules","statSize":18185,"parsedSize":11576,"gzipSize":3079},{"id":"Ibam","label":"cash.vue + 2 modules","path":"./src/page/user/cash.vue + 2 modules","statSize":8741,"parsedSize":3813,"gzipSize":1859},{"id":"LXAe","label":"notify.vue","path":"./src/page/user/notify.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"Msij","label":"detail.vue","path":"./src/page/user/detail.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"S2cN","label":"addCard.vue","path":"./src/page/user/addCard.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"SB7x","label":"card.vue + 2 modules","path":"./src/page/user/card.vue + 2 modules","statSize":5545,"parsedSize":2350,"gzipSize":1145},{"id":"U0l4","label":"rechargelist.vue","path":"./src/page/user/rechargelist.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"W/R9","label":"detail.vue + 8 modules","path":"./src/page/user/detail.vue + 8 modules","statSize":12025,"parsedSize":4086,"gzipSize":1551},{"id":"cmou","label":"recharge-sure.vue","path":"./src/page/user/recharge-sure.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"dwcX","label":"my.vue","path":"./src/page/user/my.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"fYMb","label":"cashlist.vue","path":"./src/page/user/cashlist.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"oPL8","label":"authentication.vue","path":"./src/page/user/authentication.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"tjyG","label":"cash.vue","path":"./src/page/user/cash.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"z+Cw","label":"card.vue","path":"./src/page/user/card.vue","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":63356,"gzipSize":12797},{"label":"login","path":"./src/page/login","statSize":45260,"groups":[{"id":"/jz5","label":"register.vue","path":"./src/page/login/register.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"4R6/","label":"register.vue + 2 modules","path":"./src/page/login/register.vue + 2 modules","statSize":27321,"parsedSize":9241,"gzipSize":2605},{"id":"QrVH","label":"login.vue + 2 modules","path":"./src/page/login/login.vue + 2 modules","statSize":12768,"parsedSize":5879,"gzipSize":2082},{"id":"VNLI","label":"login.vue","path":"./src/page/login/login.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"label":"components","path":"./src/page/login/components","statSize":5089,"groups":[{"id":"X3RA","label":"header.vue","path":"./src/page/login/components/header.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"xJ83","label":"header.vue + 2 modules","path":"./src/page/login/components/header.vue + 2 modules","statSize":5048,"parsedSize":1766,"gzipSize":925}],"parsedSize":1781,"gzipSize":929}],"parsedSize":16931,"gzipSize":4083},{"label":"cashWithdrawalRecord","path":"./src/page/cashWithdrawalRecord","statSize":11987,"groups":[{"label":"compontents","path":"./src/page/cashWithdrawalRecord/compontents","statSize":41,"groups":[{"id":"4Ik1","label":"cash-list.vue","path":"./src/page/cashWithdrawalRecord/compontents/cash-list.vue","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":15,"gzipSize":35},{"id":"PV1P","label":"index.vue + 5 modules","path":"./src/page/cashWithdrawalRecord/index.vue + 5 modules","statSize":11905,"parsedSize":4039,"gzipSize":1519},{"id":"kren","label":"index.vue","path":"./src/page/cashWithdrawalRecord/index.vue","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":4069,"gzipSize":1528},{"label":"wallet","path":"./src/page/wallet","statSize":25692,"groups":[{"id":"6S+d","label":"index.vue","path":"./src/page/wallet/index.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"AUp5","label":"index.vue + 2 modules","path":"./src/page/wallet/index.vue + 2 modules","statSize":25651,"parsedSize":13911,"gzipSize":3066}],"parsedSize":13926,"gzipSize":3070},{"label":"authentication","path":"./src/page/authentication","statSize":13535,"groups":[{"id":"9002","label":"index.vue + 2 modules","path":"./src/page/authentication/index.vue + 2 modules","statSize":13494,"parsedSize":7358,"gzipSize":2113},{"id":"dr0p","label":"index.vue","path":"./src/page/authentication/index.vue","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":7373,"gzipSize":2117},{"label":"newUser","path":"./src/page/newUser","statSize":187231,"groups":[{"id":"90Pp","label":"recharge.vue + 2 modules","path":"./src/page/newUser/recharge.vue + 2 modules","statSize":14941,"parsedSize":6504,"gzipSize":2718},{"id":"AeCG","label":"FundingDetails.vue + 2 modules","path":"./src/page/newUser/FundingDetails.vue + 2 modules","statSize":32482,"parsedSize":13388,"gzipSize":3748},{"id":"CKOL","label":"recharge.vue","path":"./src/page/newUser/recharge.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"Gp/X","label":"resetpass.vue + 2 modules","path":"./src/page/newUser/resetpass.vue + 2 modules","statSize":25746,"parsedSize":14297,"gzipSize":3681},{"id":"JUpT","label":"loginPassword.vue + 2 modules","path":"./src/page/newUser/loginPassword.vue + 2 modules","statSize":7175,"parsedSize":2564,"gzipSize":1060},{"id":"KyBZ","label":"withdraw.vue","path":"./src/page/newUser/withdraw.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"MV27","label":"withdraw.vue + 2 modules","path":"./src/page/newUser/withdraw.vue + 2 modules","statSize":10793,"parsedSize":3857,"gzipSize":1694},{"id":"RWNo","label":"rechargePay.vue + 2 modules","path":"./src/page/newUser/rechargePay.vue + 2 modules","statSize":3183,"parsedSize":928,"gzipSize":582},{"id":"T8YR","label":"setPassword.vue + 2 modules","path":"./src/page/newUser/setPassword.vue + 2 modules","statSize":32769,"parsedSize":17822,"gzipSize":4465},{"id":"UJS9","label":"rechargePay.vue","path":"./src/page/newUser/rechargePay.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"X3zd","label":"setup.vue","path":"./src/page/newUser/setup.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"b10Y","label":"smrz.vue","path":"./src/page/newUser/smrz.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"bZfU","label":"smrz.vue + 2 modules","path":"./src/page/newUser/smrz.vue + 2 modules","statSize":52486,"parsedSize":19367,"gzipSize":5023},{"id":"ijxn","label":"FundingDetails.vue","path":"./src/page/newUser/FundingDetails.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"lOfR","label":"loginPassword.vue","path":"./src/page/newUser/loginPassword.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"sAAH","label":"resetpass.vue","path":"./src/page/newUser/resetpass.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"tB9w","label":"setPassword.vue","path":"./src/page/newUser/setPassword.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"z9Xb","label":"setup.vue + 2 modules","path":"./src/page/newUser/setup.vue + 2 modules","statSize":7287,"parsedSize":2946,"gzipSize":1731}],"parsedSize":81808,"gzipSize":17360},{"label":"accountOpeningContract","path":"./src/page/accountOpeningContract","statSize":30240,"groups":[{"id":"CBfr","label":"index.vue + 2 modules","path":"./src/page/accountOpeningContract/index.vue + 2 modules","statSize":6749,"parsedSize":2548,"gzipSize":990},{"id":"E/iJ","label":"index.vue","path":"./src/page/accountOpeningContract/index.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"ldhZ","label":"detail.vue + 2 modules","path":"./src/page/accountOpeningContract/detail.vue + 2 modules","statSize":23409,"parsedSize":11985,"gzipSize":5589},{"id":"ry8f","label":"detail.vue","path":"./src/page/accountOpeningContract/detail.vue","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":14563,"gzipSize":5980},{"label":"bankCard","path":"./src/page/bankCard","statSize":55160,"groups":[{"id":"COVr","label":"bankUpDate.vue","path":"./src/page/bankCard/bankUpDate.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"guNO","label":"bankUpDate.vue + 2 modules","path":"./src/page/bankCard/bankUpDate.vue + 2 modules","statSize":30733,"parsedSize":13717,"gzipSize":3882},{"id":"lGZG","label":"index.vue","path":"./src/page/bankCard/index.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"zATV","label":"index.vue + 2 modules","path":"./src/page/bankCard/index.vue + 2 modules","statSize":24345,"parsedSize":10147,"gzipSize":3023}],"parsedSize":23894,"gzipSize":4652},{"label":"silverTransfersInDescription","path":"./src/page/silverTransfersInDescription","statSize":5064,"groups":[{"id":"Ilqc","label":"index.vue + 2 modules","path":"./src/page/silverTransfersInDescription/index.vue + 2 modules","statSize":5023,"parsedSize":1892,"gzipSize":1629},{"id":"ZGOb","label":"index.vue","path":"./src/page/silverTransfersInDescription/index.vue","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":1907,"gzipSize":1635},{"id":"btwZ","label":"forget.vue + 2 modules","path":"./src/page/forget.vue + 2 modules","statSize":14715,"parsedSize":6437,"gzipSize":2197},{"id":"fNwQ","label":"forget.vue","path":"./src/page/forget.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"label":"service","path":"./src/page/service","statSize":6846,"groups":[{"id":"nWyV","label":"service.vue + 2 modules","path":"./src/page/service/service.vue + 2 modules","statSize":6805,"parsedSize":1365,"gzipSize":824},{"id":"vS7E","label":"service.vue","path":"./src/page/service/service.vue","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":1380,"gzipSize":830}],"parsedSize":235659,"gzipSize":48891},{"label":"components","path":"./src/components","statSize":21596,"groups":[{"label":"contract","path":"./src/components/contract","statSize":21555,"groups":[{"id":"OIdV","label":"index.vue + 2 modules","path":"./src/components/contract/index.vue + 2 modules","statSize":21514,"parsedSize":9512,"gzipSize":3324},{"id":"bewr","label":"index.vue","path":"./src/components/contract/index.vue","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":9527,"gzipSize":3328},{"id":"Sk8F","label":"settings-icon.vue","path":"./src/components/settings-icon.vue","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":9542,"gzipSize":3329}],"parsedSize":246612,"gzipSize":51950},{"label":"node_modules","path":"./node_modules","statSize":1506038,"groups":[{"label":"querystring-es3","path":"./node_modules/querystring-es3","statSize":5181,"groups":[{"id":"1nuA","label":"index.js","path":"./node_modules/querystring-es3/index.js","statSize":127,"parsedSize":87,"gzipSize":97},{"id":"kMPS","label":"decode.js","path":"./node_modules/querystring-es3/decode.js","statSize":2510,"parsedSize":627,"gzipSize":411},{"id":"xaZU","label":"encode.js","path":"./node_modules/querystring-es3/encode.js","statSize":2544,"parsedSize":812,"gzipSize":431}],"parsedSize":1526,"gzipSize":694},{"label":"spark-md5","path":"./node_modules/spark-md5","statSize":22968,"groups":[{"id":"JSQk","label":"spark-md5.js","path":"./node_modules/spark-md5/spark-md5.js","statSize":22968,"parsedSize":8241,"gzipSize":2453}],"parsedSize":8241,"gzipSize":2453},{"label":"core-js","path":"./node_modules/core-js","statSize":911,"groups":[{"label":"library","path":"./node_modules/core-js/library","statSize":911,"groups":[{"label":"fn","path":"./node_modules/core-js/library/fn","statSize":107,"groups":[{"label":"object","path":"./node_modules/core-js/library/fn/object","statSize":107,"groups":[{"id":"TmV0","label":"values.js","path":"./node_modules/core-js/library/fn/object/values.js","statSize":107,"parsedSize":60,"gzipSize":79}],"parsedSize":60,"gzipSize":79}],"parsedSize":60,"gzipSize":79},{"label":"modules","path":"./node_modules/core-js/library/modules","statSize":804,"groups":[{"id":"fZOM","label":"es7.object.values.js","path":"./node_modules/core-js/library/modules/es7.object.values.js","statSize":242,"parsedSize":98,"gzipSize":106},{"id":"mbce","label":"_object-to-array.js","path":"./node_modules/core-js/library/modules/_object-to-array.js","statSize":562,"parsedSize":221,"gzipSize":191}],"parsedSize":319,"gzipSize":238}],"parsedSize":379,"gzipSize":261}],"parsedSize":379,"gzipSize":261},{"label":"babel-runtime","path":"./node_modules/babel-runtime","statSize":94,"groups":[{"label":"core-js","path":"./node_modules/babel-runtime/core-js","statSize":94,"groups":[{"label":"object","path":"./node_modules/babel-runtime/core-js/object","statSize":94,"groups":[{"id":"gRE1","label":"values.js","path":"./node_modules/babel-runtime/core-js/object/values.js","statSize":94,"parsedSize":60,"gzipSize":80}],"parsedSize":60,"gzipSize":80}],"parsedSize":60,"gzipSize":80}],"parsedSize":60,"gzipSize":80},{"label":"qiniu-js","path":"./node_modules/qiniu-js","statSize":115337,"groups":[{"label":"esm","path":"./node_modules/qiniu-js/esm","statSize":115337,"groups":[{"id":"sRRY","label":"index.js + 20 modules","path":"./node_modules/qiniu-js/esm/index.js + 20 modules","statSize":115337,"parsedSize":47964,"gzipSize":11628}],"parsedSize":47964,"gzipSize":11628}],"parsedSize":47964,"gzipSize":11628},{"label":"heic2any","path":"./node_modules/heic2any","statSize":1361547,"groups":[{"label":"dist","path":"./node_modules/heic2any/dist","statSize":1361547,"groups":[{"id":"yuOG","label":"heic2any.js","path":"./node_modules/heic2any/dist/heic2any.js","statSize":1361547,"parsedSize":1352015,"gzipSize":337775}],"parsedSize":1352015,"gzipSize":337775}],"parsedSize":1352015,"gzipSize":337775}],"parsedSize":1410185,"gzipSize":352192},{"label":"static","path":"./static","statSize":72,"groups":[{"label":"img","path":"./static/img","statSize":72,"groups":[{"id":"98ln","label":"pay.png","path":"./static/img/pay.png","statSize":72,"parsedSize":59,"gzipSize":78}],"parsedSize":59,"gzipSize":78}],"parsedSize":59,"gzipSize":78}]},{"label":"static/js/1.70d945648ef057234ab2.*************.y9bzfe.js","statSize":314533,"parsedSize":123599,"gzipSize":30717,"groups":[{"label":"src","path":"./src","statSize":224872,"groups":[{"label":"page","path":"./src/page","statSize":224872,"groups":[{"label":"home","path":"./src/page/home","statSize":224872,"groups":[{"id":"1x0z","label":"biglist.vue + 2 modules","path":"./src/page/home/<USER>","statSize":20497,"parsedSize":8816,"gzipSize":2899},{"id":"2Yyv","label":"DragonTiger.vue + 2 modules","path":"./src/page/home/<USER>","statSize":7425,"parsedSize":3376,"gzipSize":1316},{"label":"listcomponents","path":"./src/page/home/<USER>","statSize":246,"groups":[{"id":"5fGK","label":"xinzhaips.vue","path":"./src/page/home/<USER>/xinzhaips.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"MZU3","label":"vipqiangchou.vue","path":"./src/page/home/<USER>/vipqiangchou.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"Rsq9","label":"xingusg.vue","path":"./src/page/home/<USER>/xingusg.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"hOlH","label":"xinguqc.vue","path":"./src/page/home/<USER>/xinguqc.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"iCGa","label":"xinzhaisg.vue","path":"./src/page/home/<USER>/xinzhaisg.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"t6Iz","label":"dazongjiaoyi.vue","path":"./src/page/home/<USER>/dazongjiaoyi.vue","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":90,"gzipSize":38},{"id":"6VbB","label":"daylimit.vue","path":"./src/page/home/<USER>","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"7fP0","label":"topTen.vue + 2 modules","path":"./src/page/home/<USER>","statSize":5531,"parsedSize":2229,"gzipSize":1033},{"id":"8jrc","label":"newshares.vue + 2 modules","path":"./src/page/home/<USER>","statSize":13170,"parsedSize":5156,"gzipSize":2348},{"id":"EjZM","label":"stopRecovery.vue","path":"./src/page/home/<USER>","statSize":41,"parsedSize":15,"gzipSize":35},{"label":"newshares","path":"./src/page/home/<USER>","statSize":73808,"groups":[{"id":"GK8B","label":"vipdetail.vue","path":"./src/page/home/<USER>/vipdetail.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"GZ5Z","label":"zhongqianrecord.vue + 2 modules","path":"./src/page/home/<USER>/zhongqianrecord.vue + 2 modules","statSize":6688,"parsedSize":2462,"gzipSize":1103},{"id":"Hlnc","label":"zhongqianrecord.vue","path":"./src/page/home/<USER>/zhongqianrecord.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"Vyp8","label":"sharerecord.vue","path":"./src/page/home/<USER>/sharerecord.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"XCS3","label":"sharerecordDz.vue + 2 modules","path":"./src/page/home/<USER>/sharerecordDz.vue + 2 modules","statSize":13806,"parsedSize":5550,"gzipSize":1858},{"id":"YofA","label":"vipdetail.vue + 2 modules","path":"./src/page/home/<USER>/vipdetail.vue + 2 modules","statSize":14483,"parsedSize":7826,"gzipSize":4021},{"id":"hMQ3","label":"newsharesDetail.vue + 2 modules","path":"./src/page/home/<USER>/newsharesDetail.vue + 2 modules","statSize":23746,"parsedSize":9416,"gzipSize":3143},{"id":"izbW","label":"sharerecord.vue + 2 modules","path":"./src/page/home/<USER>/sharerecord.vue + 2 modules","statSize":7436,"parsedSize":2867,"gzipSize":1185},{"id":"kfSm","label":"newsharesDetail.vue","path":"./src/page/home/<USER>/newsharesDetail.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"koS5","label":"qingchouDetail.vue","path":"./src/page/home/<USER>/qingchouDetail.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"yPaT","label":"qingchouDetail.vue + 2 modules","path":"./src/page/home/<USER>/qingchouDetail.vue + 2 modules","statSize":7403,"parsedSize":2894,"gzipSize":1292},{"id":"yVy6","label":"sharerecordDz.vue","path":"./src/page/home/<USER>/sharerecordDz.vue","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":31105,"gzipSize":8841},{"id":"KQr1","label":"biglist.vue","path":"./src/page/home/<USER>","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"LgQd","label":"stopRecovery.vue + 2 modules","path":"./src/page/home/<USER>","statSize":5448,"parsedSize":2400,"gzipSize":1121},{"id":"Uc9K","label":"topTen.vue","path":"./src/page/home/<USER>","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"deD5","label":"peishouhistory.vue","path":"./src/page/home/<USER>","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"eJmd","label":"Subscription.vue","path":"./src/page/home/<USER>","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"uGzs","label":"daylimit.vue + 2 modules","path":"./src/page/home/<USER>","statSize":5956,"parsedSize":2239,"gzipSize":1018},{"id":"utQM","label":"newshares.vue","path":"./src/page/home/<USER>","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"xZxm","label":"DragonTiger.vue","path":"./src/page/home/<USER>","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"xwG+","label":"Subscription.vue + 20 modules","path":"./src/page/home/<USER>","statSize":73284,"parsedSize":41672,"gzipSize":6885},{"id":"zCgt","label":"peishouhistory.vue + 2 modules","path":"./src/page/home/<USER>","statSize":19179,"parsedSize":7571,"gzipSize":2719}],"parsedSize":104774,"gzipSize":21873}],"parsedSize":104774,"gzipSize":21873}],"parsedSize":104774,"gzipSize":21873},{"label":"node_modules","path":"./node_modules","statSize":89661,"groups":[{"label":"bignumber.js","path":"./node_modules/bignumber.js","statSize":89661,"groups":[{"id":"uotZ","label":"bignumber.js","path":"./node_modules/bignumber.js/bignumber.js","statSize":89661,"parsedSize":18505,"gzipSize":8260}],"parsedSize":18505,"gzipSize":8260}],"parsedSize":18505,"gzipSize":8260}]},{"label":"static/js/2.0860cf3b4e9ad4d428c8.*************.y9bzfe.js","statSize":117240,"parsedSize":59223,"gzipSize":8507,"groups":[{"label":"src","path":"./src","statSize":113568,"groups":[{"label":"page","path":"./src/page","statSize":105033,"groups":[{"label":"user","path":"./src/page/user","statSize":51460,"groups":[{"label":"search-order","path":"./src/page/user/search-order","statSize":51460,"groups":[{"id":"/85Y","label":"sell-stockCode.vue","path":"./src/page/user/search-order/sell-stockCode.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"0NF/","label":"sell-stockCode.vue + 2 modules","path":"./src/page/user/search-order/sell-stockCode.vue + 2 modules","statSize":11586,"parsedSize":6140,"gzipSize":2088},{"id":"9LlS","label":"hold-stockCode.vue","path":"./src/page/user/search-order/hold-stockCode.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"F4//","label":"sell-stockSpell.vue","path":"./src/page/user/search-order/sell-stockSpell.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"PLSC","label":"hold-stockSpell.vue","path":"./src/page/user/search-order/hold-stockSpell.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"Q4kk","label":"sell-stockSpell.vue + 2 modules","path":"./src/page/user/search-order/sell-stockSpell.vue + 2 modules","statSize":11595,"parsedSize":6142,"gzipSize":2091},{"id":"eRWo","label":"hold-stockSpell.vue + 2 modules","path":"./src/page/user/search-order/hold-stockSpell.vue + 2 modules","statSize":14060,"parsedSize":6881,"gzipSize":2481},{"id":"jWqR","label":"hold-stockCode.vue + 2 modules","path":"./src/page/user/search-order/hold-stockCode.vue + 2 modules","statSize":14055,"parsedSize":6880,"gzipSize":2480}],"parsedSize":26103,"gzipSize":3139}],"parsedSize":26103,"gzipSize":3139},{"label":"list","path":"./src/page/list","statSize":53573,"groups":[{"id":"/kKy","label":"list-searchVip.vue + 2 modules","path":"./src/page/list/list-searchVip.vue + 2 modules","statSize":8588,"parsedSize":3447,"gzipSize":1556},{"id":"ERJ9","label":"search.vue","path":"./src/page/list/search.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"T0si","label":"indexlist-search.vue","path":"./src/page/list/indexlist-search.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"cplT","label":"my-list-search.vue","path":"./src/page/list/my-list-search.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"hW+p","label":"indexlist-search.vue + 2 modules","path":"./src/page/list/indexlist-search.vue + 2 modules","statSize":9635,"parsedSize":4494,"gzipSize":1721},{"id":"rx/1","label":"list-search.vue + 2 modules","path":"./src/page/list/list-search.vue + 2 modules","statSize":10935,"parsedSize":4795,"gzipSize":2219},{"id":"sP8C","label":"my-list-search.vue + 2 modules","path":"./src/page/list/my-list-search.vue + 2 modules","statSize":10256,"parsedSize":5053,"gzipSize":1875},{"id":"tE9R","label":"search.vue + 2 modules","path":"./src/page/list/search.vue + 2 modules","statSize":13954,"parsedSize":6868,"gzipSize":2480},{"id":"tb9y","label":"list-searchVip.vue","path":"./src/page/list/list-searchVip.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"zLSO","label":"list-search.vue","path":"./src/page/list/list-search.vue","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":24732,"gzipSize":5717}],"parsedSize":50835,"gzipSize":6389},{"label":"utils","path":"./src/utils","statSize":2047,"groups":[{"id":"MLi6","label":"deTh.js","path":"./src/utils/deTh.js","statSize":2047,"parsedSize":359,"gzipSize":210}],"parsedSize":359,"gzipSize":210},{"label":"components","path":"./src/components","statSize":6488,"groups":[{"label":"foot","path":"./src/components/foot","statSize":6488,"groups":[{"id":"T/Hw","label":"foot.vue","path":"./src/components/foot/foot.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"hmkV","label":"foot.vue + 2 modules","path":"./src/components/foot/foot.vue + 2 modules","statSize":6447,"parsedSize":3458,"gzipSize":902}],"parsedSize":3473,"gzipSize":905}],"parsedSize":3473,"gzipSize":905}],"parsedSize":54667,"gzipSize":7134},{"label":"static","path":"./static","statSize":3672,"groups":[{"label":"img","path":"./static/img","statSize":3672,"groups":[{"label":"foot ^\\.\\","path":"./static/img/foot ^\\.\\","statSize":2106,"groups":[{"label":".*\\","path":"./static/img/foot ^\\.\\/.*\\","statSize":2106,"groups":[{"id":"/RXH","label":"home\\.png$","path":"./static/img/foot ^\\.\\/.*\\/home\\.png$","statSize":200,"parsedSize":271,"gzipSize":215},{"id":"/Ti4","label":"hangqing\\.png$","path":"./static/img/foot ^\\.\\/.*\\/hangqing\\.png$","statSize":208,"parsedSize":279,"gzipSize":220},{"id":"0Jzj","label":"hangqing\\-active\\.png$","path":"./static/img/foot ^\\.\\/.*\\/hangqing\\-active\\.png$","statSize":222,"parsedSize":293,"gzipSize":222},{"id":"Tbjy","label":"zixuan\\-active\\.png$","path":"./static/img/foot ^\\.\\/.*\\/zixuan\\-active\\.png$","statSize":218,"parsedSize":289,"gzipSize":221},{"id":"Tw+E","label":"user\\-active\\.png$","path":"./static/img/foot ^\\.\\/.*\\/user\\-active\\.png$","statSize":214,"parsedSize":285,"gzipSize":219},{"id":"UJhX","label":"home\\-active\\.png$","path":"./static/img/foot ^\\.\\/.*\\/home\\-active\\.png$","statSize":214,"parsedSize":285,"gzipSize":221},{"id":"VqUm","label":"chicang\\.png$","path":"./static/img/foot ^\\.\\/.*\\/chicang\\.png$","statSize":206,"parsedSize":277,"gzipSize":217},{"id":"bHzZ","label":"zixuan\\.png$","path":"./static/img/foot ^\\.\\/.*\\/zixuan\\.png$","statSize":204,"parsedSize":275,"gzipSize":218},{"id":"qr9D","label":"chicang\\-active\\.png$","path":"./static/img/foot ^\\.\\/.*\\/chicang\\-active\\.png$","statSize":220,"parsedSize":291,"gzipSize":224},{"id":"uMEc","label":"user\\.png$","path":"./static/img/foot ^\\.\\/.*\\/user\\.png$","statSize":200,"parsedSize":271,"gzipSize":213}],"parsedSize":2816,"gzipSize":454}],"parsedSize":2816,"gzipSize":454},{"label":"foot","path":"./static/img/foot","statSize":1566,"groups":[{"label":"black","path":"./static/img/foot/black","statSize":783,"groups":[{"id":"7XQ0","label":"hangqing.png","path":"./static/img/foot/black/hangqing.png","statSize":77,"parsedSize":64,"gzipSize":84},{"id":"NDjZ","label":"zixuan.png","path":"./static/img/foot/black/zixuan.png","statSize":75,"parsedSize":62,"gzipSize":82},{"id":"UwgZ","label":"user-active.png","path":"./static/img/foot/black/user-active.png","statSize":80,"parsedSize":67,"gzipSize":86},{"id":"eLxI","label":"user.png","path":"./static/img/foot/black/user.png","statSize":73,"parsedSize":60,"gzipSize":80},{"id":"kxWX","label":"chicang.png","path":"./static/img/foot/black/chicang.png","statSize":76,"parsedSize":63,"gzipSize":83},{"id":"oE32","label":"home.png","path":"./static/img/foot/black/home.png","statSize":73,"parsedSize":60,"gzipSize":80},{"id":"rnXr","label":"hangqing-active.png","path":"./static/img/foot/black/hangqing-active.png","statSize":84,"parsedSize":71,"gzipSize":90},{"id":"v1UV","label":"home-active.png","path":"./static/img/foot/black/home-active.png","statSize":80,"parsedSize":67,"gzipSize":86},{"id":"vKAT","label":"chicang-active.png","path":"./static/img/foot/black/chicang-active.png","statSize":83,"parsedSize":70,"gzipSize":89},{"id":"ve9t","label":"zixuan-active.png","path":"./static/img/foot/black/zixuan-active.png","statSize":82,"parsedSize":69,"gzipSize":88}],"parsedSize":653,"gzipSize":192},{"label":"red","path":"./static/img/foot/red","statSize":783,"groups":[{"id":"8Ex9","label":"chicang-active.png","path":"./static/img/foot/red/chicang-active.png","statSize":83,"parsedSize":70,"gzipSize":89},{"id":"ErCa","label":"hangqing-active.png","path":"./static/img/foot/red/hangqing-active.png","statSize":84,"parsedSize":71,"gzipSize":90},{"id":"GTme","label":"user-active.png","path":"./static/img/foot/red/user-active.png","statSize":80,"parsedSize":67,"gzipSize":86},{"id":"Iln+","label":"hangqing.png","path":"./static/img/foot/red/hangqing.png","statSize":77,"parsedSize":64,"gzipSize":84},{"id":"bBpS","label":"zixuan.png","path":"./static/img/foot/red/zixuan.png","statSize":75,"parsedSize":62,"gzipSize":82},{"id":"le/q","label":"user.png","path":"./static/img/foot/red/user.png","statSize":73,"parsedSize":60,"gzipSize":80},{"id":"rxde","label":"chicang.png","path":"./static/img/foot/red/chicang.png","statSize":76,"parsedSize":63,"gzipSize":83},{"id":"utCc","label":"home.png","path":"./static/img/foot/red/home.png","statSize":73,"parsedSize":60,"gzipSize":80},{"id":"vO58","label":"zixuan-active.png","path":"./static/img/foot/red/zixuan-active.png","statSize":82,"parsedSize":69,"gzipSize":88},{"id":"yn0l","label":"home-active.png","path":"./static/img/foot/red/home-active.png","statSize":80,"parsedSize":67,"gzipSize":86}],"parsedSize":653,"gzipSize":189}],"parsedSize":1306,"gzipSize":261}],"parsedSize":4122,"gzipSize":649}],"parsedSize":4122,"gzipSize":649}]},{"label":"static/js/3.e353c8e6cb4014c6aaef.*************.y9bzfe.js","statSize":258372,"parsedSize":100221,"gzipSize":21721,"groups":[{"label":"node_modules","path":"./node_modules","statSize":89661,"groups":[{"label":"bignumber.js","path":"./node_modules/bignumber.js","statSize":89661,"groups":[{"id":"uotZ","label":"bignumber.js","path":"./node_modules/bignumber.js/bignumber.js","statSize":89661,"parsedSize":18505,"gzipSize":8260}],"parsedSize":18505,"gzipSize":8260}],"parsedSize":18505,"gzipSize":8260},{"label":"static","path":"./static","statSize":3753,"groups":[{"label":"img","path":"./static/img","statSize":3753,"groups":[{"label":"foot ^\\.\\","path":"./static/img/foot ^\\.\\","statSize":2106,"groups":[{"label":".*\\","path":"./static/img/foot ^\\.\\/.*\\","statSize":2106,"groups":[{"id":"/RXH","label":"home\\.png$","path":"./static/img/foot ^\\.\\/.*\\/home\\.png$","statSize":200,"parsedSize":271,"gzipSize":215},{"id":"/Ti4","label":"hangqing\\.png$","path":"./static/img/foot ^\\.\\/.*\\/hangqing\\.png$","statSize":208,"parsedSize":279,"gzipSize":220},{"id":"0Jzj","label":"hangqing\\-active\\.png$","path":"./static/img/foot ^\\.\\/.*\\/hangqing\\-active\\.png$","statSize":222,"parsedSize":293,"gzipSize":222},{"id":"Tbjy","label":"zixuan\\-active\\.png$","path":"./static/img/foot ^\\.\\/.*\\/zixuan\\-active\\.png$","statSize":218,"parsedSize":289,"gzipSize":221},{"id":"Tw+E","label":"user\\-active\\.png$","path":"./static/img/foot ^\\.\\/.*\\/user\\-active\\.png$","statSize":214,"parsedSize":285,"gzipSize":219},{"id":"UJhX","label":"home\\-active\\.png$","path":"./static/img/foot ^\\.\\/.*\\/home\\-active\\.png$","statSize":214,"parsedSize":285,"gzipSize":221},{"id":"VqUm","label":"chicang\\.png$","path":"./static/img/foot ^\\.\\/.*\\/chicang\\.png$","statSize":206,"parsedSize":277,"gzipSize":217},{"id":"bHzZ","label":"zixuan\\.png$","path":"./static/img/foot ^\\.\\/.*\\/zixuan\\.png$","statSize":204,"parsedSize":275,"gzipSize":218},{"id":"qr9D","label":"chicang\\-active\\.png$","path":"./static/img/foot ^\\.\\/.*\\/chicang\\-active\\.png$","statSize":220,"parsedSize":291,"gzipSize":224},{"id":"uMEc","label":"user\\.png$","path":"./static/img/foot ^\\.\\/.*\\/user\\.png$","statSize":200,"parsedSize":271,"gzipSize":213}],"parsedSize":2816,"gzipSize":454}],"parsedSize":2816,"gzipSize":454},{"label":"foot","path":"./static/img/foot","statSize":1566,"groups":[{"label":"black","path":"./static/img/foot/black","statSize":783,"groups":[{"id":"7XQ0","label":"hangqing.png","path":"./static/img/foot/black/hangqing.png","statSize":77,"parsedSize":64,"gzipSize":84},{"id":"NDjZ","label":"zixuan.png","path":"./static/img/foot/black/zixuan.png","statSize":75,"parsedSize":62,"gzipSize":82},{"id":"UwgZ","label":"user-active.png","path":"./static/img/foot/black/user-active.png","statSize":80,"parsedSize":67,"gzipSize":86},{"id":"eLxI","label":"user.png","path":"./static/img/foot/black/user.png","statSize":73,"parsedSize":60,"gzipSize":80},{"id":"kxWX","label":"chicang.png","path":"./static/img/foot/black/chicang.png","statSize":76,"parsedSize":63,"gzipSize":83},{"id":"oE32","label":"home.png","path":"./static/img/foot/black/home.png","statSize":73,"parsedSize":60,"gzipSize":80},{"id":"rnXr","label":"hangqing-active.png","path":"./static/img/foot/black/hangqing-active.png","statSize":84,"parsedSize":71,"gzipSize":90},{"id":"v1UV","label":"home-active.png","path":"./static/img/foot/black/home-active.png","statSize":80,"parsedSize":67,"gzipSize":86},{"id":"vKAT","label":"chicang-active.png","path":"./static/img/foot/black/chicang-active.png","statSize":83,"parsedSize":70,"gzipSize":89},{"id":"ve9t","label":"zixuan-active.png","path":"./static/img/foot/black/zixuan-active.png","statSize":82,"parsedSize":69,"gzipSize":88}],"parsedSize":653,"gzipSize":192},{"label":"red","path":"./static/img/foot/red","statSize":783,"groups":[{"id":"8Ex9","label":"chicang-active.png","path":"./static/img/foot/red/chicang-active.png","statSize":83,"parsedSize":70,"gzipSize":89},{"id":"ErCa","label":"hangqing-active.png","path":"./static/img/foot/red/hangqing-active.png","statSize":84,"parsedSize":71,"gzipSize":90},{"id":"GTme","label":"user-active.png","path":"./static/img/foot/red/user-active.png","statSize":80,"parsedSize":67,"gzipSize":86},{"id":"Iln+","label":"hangqing.png","path":"./static/img/foot/red/hangqing.png","statSize":77,"parsedSize":64,"gzipSize":84},{"id":"bBpS","label":"zixuan.png","path":"./static/img/foot/red/zixuan.png","statSize":75,"parsedSize":62,"gzipSize":82},{"id":"le/q","label":"user.png","path":"./static/img/foot/red/user.png","statSize":73,"parsedSize":60,"gzipSize":80},{"id":"rxde","label":"chicang.png","path":"./static/img/foot/red/chicang.png","statSize":76,"parsedSize":63,"gzipSize":83},{"id":"utCc","label":"home.png","path":"./static/img/foot/red/home.png","statSize":73,"parsedSize":60,"gzipSize":80},{"id":"vO58","label":"zixuan-active.png","path":"./static/img/foot/red/zixuan-active.png","statSize":82,"parsedSize":69,"gzipSize":88},{"id":"yn0l","label":"home-active.png","path":"./static/img/foot/red/home-active.png","statSize":80,"parsedSize":67,"gzipSize":86}],"parsedSize":653,"gzipSize":189}],"parsedSize":1306,"gzipSize":261},{"label":"detail","path":"./static/img/detail","statSize":81,"groups":[{"id":"ab9X","label":"fencang-icon.png","path":"./static/img/detail/fencang-icon.png","statSize":81,"parsedSize":68,"gzipSize":88}],"parsedSize":68,"gzipSize":88}],"parsedSize":4190,"gzipSize":665}],"parsedSize":4190,"gzipSize":665},{"label":"src","path":"./src","statSize":164958,"groups":[{"label":"components","path":"./src/components","statSize":6488,"groups":[{"label":"foot","path":"./src/components/foot","statSize":6488,"groups":[{"id":"T/Hw","label":"foot.vue","path":"./src/components/foot/foot.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"hmkV","label":"foot.vue + 2 modules","path":"./src/components/foot/foot.vue + 2 modules","statSize":6447,"parsedSize":3458,"gzipSize":902}],"parsedSize":3473,"gzipSize":905}],"parsedSize":3473,"gzipSize":905},{"label":"page","path":"./src/page","statSize":158389,"groups":[{"label":"home","path":"./src/page/home","statSize":117826,"groups":[{"id":"+zkl","label":"sub-warehouse-buy.vue","path":"./src/page/home/<USER>","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"2FYh","label":"two-buy.vue","path":"./src/page/home/<USER>","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"2IVl","label":"two-buy.vue + 2 modules","path":"./src/page/home/<USER>","statSize":24084,"parsedSize":11464,"gzipSize":3265},{"id":"WDqc","label":"sub-warehouse-buy.vue + 2 modules","path":"./src/page/home/<USER>","statSize":27278,"parsedSize":13043,"gzipSize":3631},{"id":"XAXI","label":"futures-buy.vue","path":"./src/page/home/<USER>","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"bimB","label":"index-buy.vue + 2 modules","path":"./src/page/home/<USER>","statSize":21028,"parsedSize":10334,"gzipSize":3115},{"id":"f3ib","label":"buy.vue + 2 modules","path":"./src/page/home/<USER>","statSize":22288,"parsedSize":11679,"gzipSize":3354},{"id":"os24","label":"buy.vue","path":"./src/page/home/<USER>","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"rcRW","label":"index-buy.vue","path":"./src/page/home/<USER>","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"vayI","label":"futures-buy.vue + 2 modules","path":"./src/page/home/<USER>","statSize":22943,"parsedSize":11389,"gzipSize":3158}],"parsedSize":57984,"gzipSize":6293},{"label":"kline","path":"./src/page/kline","statSize":40563,"groups":[{"id":"5iFr","label":"buyStock.vue","path":"./src/page/kline/buyStock.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"HnPO","label":"buyStock.vue + 2 modules","path":"./src/page/kline/buyStock.vue + 2 modules","statSize":40522,"parsedSize":15582,"gzipSize":4272}],"parsedSize":15597,"gzipSize":4277}],"parsedSize":73581,"gzipSize":9845},{"label":"assets","path":"./src/assets","statSize":81,"groups":[{"label":"ico","path":"./src/assets/ico","statSize":81,"groups":[{"id":"UsIu","label":"hangqing-btn.png","path":"./src/assets/ico/hangqing-btn.png","statSize":81,"parsedSize":68,"gzipSize":88}],"parsedSize":68,"gzipSize":88}],"parsedSize":68,"gzipSize":88}],"parsedSize":77122,"gzipSize":10543}]},{"label":"static/js/4.9021e3a15ff2e8578bf5.*************.y9bzfe.js","statSize":872517,"parsedSize":389397,"gzipSize":104653,"groups":[{"label":"node_modules","path":"./node_modules","statSize":829943,"groups":[{"label":"querystring-es3","path":"./node_modules/querystring-es3","statSize":5181,"groups":[{"id":"1nuA","label":"index.js","path":"./node_modules/querystring-es3/index.js","statSize":127,"parsedSize":87,"gzipSize":97},{"id":"kMPS","label":"decode.js","path":"./node_modules/querystring-es3/decode.js","statSize":2510,"parsedSize":627,"gzipSize":411},{"id":"xaZU","label":"encode.js","path":"./node_modules/querystring-es3/encode.js","statSize":2544,"parsedSize":812,"gzipSize":431}],"parsedSize":1526,"gzipSize":694},{"label":"base64-js","path":"./node_modules/base64-js","statSize":3932,"groups":[{"id":"EKta","label":"index.js","path":"./node_modules/base64-js/index.js","statSize":3932,"parsedSize":1406,"gzipSize":744}],"parsedSize":1406,"gzipSize":744},{"label":"buffer","path":"./node_modules/buffer","statSize":48590,"groups":[{"id":"EuP9","label":"index.js","path":"./node_modules/buffer/index.js","statSize":48590,"parsedSize":19845,"gzipSize":5858}],"parsedSize":19845,"gzipSize":5858},{"label":"punycode","path":"./node_modules/punycode","statSize":14670,"groups":[{"id":"MsCo","label":"punycode.js","path":"./node_modules/punycode/punycode.js","statSize":14670,"parsedSize":2588,"gzipSize":1415}],"parsedSize":2588,"gzipSize":1415},{"label":"url","path":"./node_modules/url","statSize":23631,"groups":[{"id":"UZ5h","label":"url.js","path":"./node_modules/url/url.js","statSize":23317,"parsedSize":7476,"gzipSize":2774},{"id":"qOJP","label":"util.js","path":"./node_modules/url/util.js","statSize":314,"parsedSize":227,"gzipSize":145}],"parsedSize":7703,"gzipSize":2833},{"label":"pdfjs-dist","path":"./node_modules/pdfjs-dist","statSize":731653,"groups":[{"label":"build","path":"./node_modules/pdfjs-dist/build","statSize":731653,"groups":[{"id":"nKpR","label":"pdf.js","path":"./node_modules/pdfjs-dist/build/pdf.js","statSize":731653,"parsedSize":334184,"gzipSize":87690}],"parsedSize":334184,"gzipSize":87690}],"parsedSize":334184,"gzipSize":87690},{"label":"isarray","path":"./node_modules/isarray","statSize":132,"groups":[{"id":"sOR5","label":"index.js","path":"./node_modules/isarray/index.js","statSize":132,"parsedSize":104,"gzipSize":109}],"parsedSize":104,"gzipSize":109},{"label":"ieee754","path":"./node_modules/ieee754","statSize":2154,"groups":[{"id":"ujcs","label":"index.js","path":"./node_modules/ieee754/index.js","statSize":2154,"parsedSize":972,"gzipSize":572}],"parsedSize":972,"gzipSize":572}],"parsedSize":368328,"gzipSize":98482},{"label":"src","path":"./src","statSize":42057,"groups":[{"label":"assets","path":"./src/assets","statSize":244,"groups":[{"label":"img","path":"./src/assets/img","statSize":244,"groups":[{"id":"owqv","label":"zuojiantou.png","path":"./src/assets/img/zuojiantou.png","statSize":79,"parsedSize":66,"gzipSize":86},{"id":"Sree","label":"ic_number_add.png","path":"./src/assets/img/ic_number_add.png","statSize":82,"parsedSize":69,"gzipSize":89},{"id":"glPx","label":"ic_number_jian.png","path":"./src/assets/img/ic_number_jian.png","statSize":83,"parsedSize":70,"gzipSize":90}],"parsedSize":205,"gzipSize":135}],"parsedSize":205,"gzipSize":135},{"label":"page","path":"./src/page","statSize":41813,"groups":[{"id":"IKGp","label":"tradeAgree.vue","path":"./src/page/tradeAgree.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"label":"trading","path":"./src/page/trading","statSize":26396,"groups":[{"id":"M6XL","label":"buy.vue","path":"./src/page/trading/buy.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"Oixe","label":"buy.vue + 2 modules","path":"./src/page/trading/buy.vue + 2 modules","statSize":26355,"parsedSize":14111,"gzipSize":3279}],"parsedSize":14126,"gzipSize":3289},{"label":"home","path":"./src/page/home","statSize":9840,"groups":[{"label":"components","path":"./src/page/home/<USER>","statSize":5701,"groups":[{"id":"NWVd","label":"alert.vue + 2 modules","path":"./src/page/home/<USER>/alert.vue + 2 modules","statSize":5660,"parsedSize":2228,"gzipSize":1124},{"id":"tVNW","label":"alert.vue","path":"./src/page/home/<USER>/alert.vue","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":2243,"gzipSize":1130},{"id":"i9sE","label":"inquiry.vue","path":"./src/page/home/<USER>","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"n5VT","label":"inquiry.vue + 2 modules","path":"./src/page/home/<USER>","statSize":4098,"parsedSize":1686,"gzipSize":1057}],"parsedSize":3944,"gzipSize":1799},{"id":"tw4v","label":"tradeAgree.vue + 2 modules","path":"./src/page/tradeAgree.vue + 2 modules","statSize":5536,"parsedSize":2168,"gzipSize":1094}],"parsedSize":20253,"gzipSize":5281}],"parsedSize":20458,"gzipSize":5362},{"label":"buildin","path":"./buildin","statSize":517,"groups":[{"id":"3IRH","label":"module.js","path":"./buildin/module.js","statSize":517,"parsedSize":301,"gzipSize":181}],"parsedSize":301,"gzipSize":181}]},{"label":"static/js/5.f57d9dd0623ee6bea90a.*************.y9bzfe.js","statSize":95667,"parsedSize":48964,"gzipSize":8229,"groups":[{"label":"static","path":"./static","statSize":3672,"groups":[{"label":"img","path":"./static/img","statSize":3672,"groups":[{"label":"foot ^\\.\\","path":"./static/img/foot ^\\.\\","statSize":2106,"groups":[{"label":".*\\","path":"./static/img/foot ^\\.\\/.*\\","statSize":2106,"groups":[{"id":"/RXH","label":"home\\.png$","path":"./static/img/foot ^\\.\\/.*\\/home\\.png$","statSize":200,"parsedSize":271,"gzipSize":215},{"id":"/Ti4","label":"hangqing\\.png$","path":"./static/img/foot ^\\.\\/.*\\/hangqing\\.png$","statSize":208,"parsedSize":279,"gzipSize":220},{"id":"0Jzj","label":"hangqing\\-active\\.png$","path":"./static/img/foot ^\\.\\/.*\\/hangqing\\-active\\.png$","statSize":222,"parsedSize":293,"gzipSize":222},{"id":"Tbjy","label":"zixuan\\-active\\.png$","path":"./static/img/foot ^\\.\\/.*\\/zixuan\\-active\\.png$","statSize":218,"parsedSize":289,"gzipSize":221},{"id":"Tw+E","label":"user\\-active\\.png$","path":"./static/img/foot ^\\.\\/.*\\/user\\-active\\.png$","statSize":214,"parsedSize":285,"gzipSize":219},{"id":"UJhX","label":"home\\-active\\.png$","path":"./static/img/foot ^\\.\\/.*\\/home\\-active\\.png$","statSize":214,"parsedSize":285,"gzipSize":221},{"id":"VqUm","label":"chicang\\.png$","path":"./static/img/foot ^\\.\\/.*\\/chicang\\.png$","statSize":206,"parsedSize":277,"gzipSize":217},{"id":"bHzZ","label":"zixuan\\.png$","path":"./static/img/foot ^\\.\\/.*\\/zixuan\\.png$","statSize":204,"parsedSize":275,"gzipSize":218},{"id":"qr9D","label":"chicang\\-active\\.png$","path":"./static/img/foot ^\\.\\/.*\\/chicang\\-active\\.png$","statSize":220,"parsedSize":291,"gzipSize":224},{"id":"uMEc","label":"user\\.png$","path":"./static/img/foot ^\\.\\/.*\\/user\\.png$","statSize":200,"parsedSize":271,"gzipSize":213}],"parsedSize":2816,"gzipSize":454}],"parsedSize":2816,"gzipSize":454},{"label":"foot","path":"./static/img/foot","statSize":1566,"groups":[{"label":"black","path":"./static/img/foot/black","statSize":783,"groups":[{"id":"7XQ0","label":"hangqing.png","path":"./static/img/foot/black/hangqing.png","statSize":77,"parsedSize":64,"gzipSize":84},{"id":"NDjZ","label":"zixuan.png","path":"./static/img/foot/black/zixuan.png","statSize":75,"parsedSize":62,"gzipSize":82},{"id":"UwgZ","label":"user-active.png","path":"./static/img/foot/black/user-active.png","statSize":80,"parsedSize":67,"gzipSize":86},{"id":"eLxI","label":"user.png","path":"./static/img/foot/black/user.png","statSize":73,"parsedSize":60,"gzipSize":80},{"id":"kxWX","label":"chicang.png","path":"./static/img/foot/black/chicang.png","statSize":76,"parsedSize":63,"gzipSize":83},{"id":"oE32","label":"home.png","path":"./static/img/foot/black/home.png","statSize":73,"parsedSize":60,"gzipSize":80},{"id":"rnXr","label":"hangqing-active.png","path":"./static/img/foot/black/hangqing-active.png","statSize":84,"parsedSize":71,"gzipSize":90},{"id":"v1UV","label":"home-active.png","path":"./static/img/foot/black/home-active.png","statSize":80,"parsedSize":67,"gzipSize":86},{"id":"vKAT","label":"chicang-active.png","path":"./static/img/foot/black/chicang-active.png","statSize":83,"parsedSize":70,"gzipSize":89},{"id":"ve9t","label":"zixuan-active.png","path":"./static/img/foot/black/zixuan-active.png","statSize":82,"parsedSize":69,"gzipSize":88}],"parsedSize":653,"gzipSize":192},{"label":"red","path":"./static/img/foot/red","statSize":783,"groups":[{"id":"8Ex9","label":"chicang-active.png","path":"./static/img/foot/red/chicang-active.png","statSize":83,"parsedSize":70,"gzipSize":89},{"id":"ErCa","label":"hangqing-active.png","path":"./static/img/foot/red/hangqing-active.png","statSize":84,"parsedSize":71,"gzipSize":90},{"id":"GTme","label":"user-active.png","path":"./static/img/foot/red/user-active.png","statSize":80,"parsedSize":67,"gzipSize":86},{"id":"Iln+","label":"hangqing.png","path":"./static/img/foot/red/hangqing.png","statSize":77,"parsedSize":64,"gzipSize":84},{"id":"bBpS","label":"zixuan.png","path":"./static/img/foot/red/zixuan.png","statSize":75,"parsedSize":62,"gzipSize":82},{"id":"le/q","label":"user.png","path":"./static/img/foot/red/user.png","statSize":73,"parsedSize":60,"gzipSize":80},{"id":"rxde","label":"chicang.png","path":"./static/img/foot/red/chicang.png","statSize":76,"parsedSize":63,"gzipSize":83},{"id":"utCc","label":"home.png","path":"./static/img/foot/red/home.png","statSize":73,"parsedSize":60,"gzipSize":80},{"id":"vO58","label":"zixuan-active.png","path":"./static/img/foot/red/zixuan-active.png","statSize":82,"parsedSize":69,"gzipSize":88},{"id":"yn0l","label":"home-active.png","path":"./static/img/foot/red/home-active.png","statSize":80,"parsedSize":67,"gzipSize":86}],"parsedSize":653,"gzipSize":189}],"parsedSize":1306,"gzipSize":261}],"parsedSize":4122,"gzipSize":649}],"parsedSize":4122,"gzipSize":649},{"label":"src","path":"./src","statSize":91995,"groups":[{"label":"components","path":"./src/components","statSize":6488,"groups":[{"label":"foot","path":"./src/components/foot","statSize":6488,"groups":[{"id":"T/Hw","label":"foot.vue","path":"./src/components/foot/foot.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"hmkV","label":"foot.vue + 2 modules","path":"./src/components/foot/foot.vue + 2 modules","statSize":6447,"parsedSize":3458,"gzipSize":902}],"parsedSize":3473,"gzipSize":905}],"parsedSize":3473,"gzipSize":905},{"label":"assets","path":"./src/assets","statSize":459,"groups":[{"label":"ico","path":"./src/assets/ico","statSize":459,"groups":[{"id":"/0QQ","label":"hszzj.png","path":"./src/assets/ico/hszzj.png","statSize":74,"parsedSize":61,"gzipSize":79},{"id":"5h9f","label":"hskyzj.png","path":"./src/assets/ico/hskyzj.png","statSize":75,"parsedSize":62,"gzipSize":82},{"id":"9/nL","label":"hsdjbzj.png","path":"./src/assets/ico/hsdjbzj.png","statSize":76,"parsedSize":63,"gzipSize":83},{"id":"CAJB","label":"zongzichan.png","path":"./src/assets/ico/zongzichan.png","statSize":79,"parsedSize":66,"gzipSize":86},{"id":"uGy7","label":"zongzichan-red.png","path":"./src/assets/ico/zongzichan-red.png","statSize":83,"parsedSize":70,"gzipSize":90},{"id":"v6Mw","label":"zyk.png","path":"./src/assets/ico/zyk.png","statSize":72,"parsedSize":59,"gzipSize":79}],"parsedSize":381,"gzipSize":145}],"parsedSize":381,"gzipSize":145},{"label":"page","path":"./src/page","statSize":85048,"groups":[{"label":"funds","path":"./src/page/funds","statSize":85048,"groups":[{"id":"8/qw","label":"days.vue","path":"./src/page/funds/days.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"8VS6","label":"funds-list3.vue","path":"./src/page/funds/funds-list3.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"ABKn","label":"days.vue + 2 modules","path":"./src/page/funds/days.vue + 2 modules","statSize":16831,"parsedSize":9284,"gzipSize":2804},{"id":"DfC5","label":"funds-list1.vue","path":"./src/page/funds/funds-list1.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"label":"compontents","path":"./src/page/funds/compontents","statSize":123,"groups":[{"id":"HzQA","label":"holdposition.vue","path":"./src/page/funds/compontents/holdposition.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"K9FY","label":"fundssellorder.vue","path":"./src/page/funds/compontents/fundssellorder.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"g2o1","label":"fundsholdposition.vue","path":"./src/page/funds/compontents/fundsholdposition.vue","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":45,"gzipSize":38},{"id":"O4pL","label":"funds-list2.vue","path":"./src/page/funds/funds-list2.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"Oz3d","label":"index.vue + 2 modules","path":"./src/page/funds/index.vue + 2 modules","statSize":11596,"parsedSize":6925,"gzipSize":1796},{"id":"a5+D","label":"funds-list.vue","path":"./src/page/funds/funds-list.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"gWEu","label":"funds-list.vue + 20 modules","path":"./src/page/funds/funds-list.vue + 20 modules","statSize":56252,"parsedSize":24218,"gzipSize":3478},{"id":"oo04","label":"index.vue","path":"./src/page/funds/index.vue","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":40562,"gzipSize":6620}],"parsedSize":40562,"gzipSize":6620}],"parsedSize":44416,"gzipSize":7329}]},{"label":"static/js/6.480f73951f844fbe531b.*************.y9bzfe.js","statSize":33224,"parsedSize":16968,"gzipSize":3016,"groups":[{"label":"src","path":"./src","statSize":33224,"groups":[{"label":"assets","path":"./src/assets","statSize":79,"groups":[{"label":"img","path":"./src/assets/img","statSize":79,"groups":[{"id":"owqv","label":"zuojiantou.png","path":"./src/assets/img/zuojiantou.png","statSize":79,"parsedSize":66,"gzipSize":86}],"parsedSize":66,"gzipSize":86}],"parsedSize":66,"gzipSize":86},{"label":"page","path":"./src/page","statSize":33145,"groups":[{"label":"user","path":"./src/page/user","statSize":11271,"groups":[{"id":"2ov6","label":"transfer.vue","path":"./src/page/user/transfer.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"vWNF","label":"transfer.vue + 2 modules","path":"./src/page/user/transfer.vue + 2 modules","statSize":11230,"parsedSize":6102,"gzipSize":1457}],"parsedSize":6117,"gzipSize":1462},{"label":"transferRecord","path":"./src/page/transferRecord","statSize":10579,"groups":[{"id":"AQm7","label":"index.vue","path":"./src/page/transferRecord/index.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"H2sd","label":"index.vue + 5 modules","path":"./src/page/transferRecord/index.vue + 5 modules","statSize":10497,"parsedSize":4425,"gzipSize":1590},{"label":"compontents","path":"./src/page/transferRecord/compontents","statSize":41,"groups":[{"id":"QV1F","label":"recharge-list.vue","path":"./src/page/transferRecord/compontents/recharge-list.vue","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":15,"gzipSize":35}],"parsedSize":4455,"gzipSize":1596},{"label":"transfer","path":"./src/page/transfer","statSize":11295,"groups":[{"id":"jyaF","label":"index.vue","path":"./src/page/transfer/index.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"oF63","label":"index.vue + 2 modules","path":"./src/page/transfer/index.vue + 2 modules","statSize":11254,"parsedSize":6169,"gzipSize":1484}],"parsedSize":6184,"gzipSize":1489}],"parsedSize":16756,"gzipSize":2873}],"parsedSize":16822,"gzipSize":2908}]},{"label":"static/js/7.a007c86d3fa9902232c0.*************.y9bzfe.js","statSize":12088,"parsedSize":5932,"gzipSize":2944,"groups":[{"label":"src","path":"./src","statSize":12088,"groups":[{"label":"page","path":"./src/page","statSize":12088,"groups":[{"label":"newUser","path":"./src/page/newUser","statSize":8434,"groups":[{"id":"2szZ","label":"xieyiMianze.vue","path":"./src/page/newUser/xieyiMianze.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"4umD","label":"xieyiMianze.vue + 1 modules","path":"./src/page/newUser/xieyiMianze.vue + 1 modules","statSize":8393,"parsedSize":4628,"gzipSize":2394}],"parsedSize":4643,"gzipSize":2396},{"label":"user","path":"./src/page/user","statSize":3654,"groups":[{"id":"VXiN","label":"agreement.vue + 2 modules","path":"./src/page/user/agreement.vue + 2 modules","statSize":3613,"parsedSize":1150,"gzipSize":681},{"id":"r62m","label":"agreement.vue","path":"./src/page/user/agreement.vue","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":1165,"gzipSize":686}],"parsedSize":5808,"gzipSize":2862}],"parsedSize":5808,"gzipSize":2862}]},{"label":"static/js/8.5eaf12efae902f6423b7.*************.y9bzfe.js","statSize":3279758,"parsedSize":884541,"gzipSize":299589,"groups":[{"label":"src","path":"./src","statSize":94321,"groups":[{"label":"assets","path":"./src/assets","statSize":224,"groups":[{"label":"img","path":"./src/assets/img","statSize":224,"groups":[{"id":"0YUB","label":"back.png","path":"./src/assets/img/back.png","statSize":73,"parsedSize":60,"gzipSize":80},{"id":"L/A9","label":"xiala.png","path":"./src/assets/img/xiala.png","statSize":74,"parsedSize":61,"gzipSize":81},{"id":"rZkb","label":"calendar.png","path":"./src/assets/img/calendar.png","statSize":77,"parsedSize":64,"gzipSize":84}],"parsedSize":185,"gzipSize":114}],"parsedSize":185,"gzipSize":114},{"label":"page","path":"./src/page","statSize":94097,"groups":[{"label":"list","path":"./src/page/list","statSize":94097,"groups":[{"id":"1osH","label":"listDetail.vue + 2 modules","path":"./src/page/list/listDetail.vue + 2 modules","statSize":4051,"parsedSize":1775,"gzipSize":735},{"label":"compontent","path":"./src/page/list/compontent","statSize":246,"groups":[{"id":"4rGy","label":"chart-3.vue","path":"./src/page/list/compontent/chart-3.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"6UNd","label":"chart-day.vue","path":"./src/page/list/compontent/chart-day.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"NXyu","label":"chart.vue","path":"./src/page/list/compontent/chart.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"f1A8","label":"chart-2.vue","path":"./src/page/list/compontent/chart-2.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"htAu","label":"img.vue","path":"./src/page/list/compontent/img.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"t3fs","label":"chart-1.vue","path":"./src/page/list/compontent/chart-1.vue","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":90,"gzipSize":38},{"id":"PtXC","label":"detail2.vue","path":"./src/page/list/detail2.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"R2g0","label":"detail2.vue + 20 modules","path":"./src/page/list/detail2.vue + 20 modules","statSize":89718,"parsedSize":33566,"gzipSize":5178},{"id":"fz43","label":"listDetail.vue","path":"./src/page/list/listDetail.vue","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":35461,"gzipSize":5660}],"parsedSize":35461,"gzipSize":5660}],"parsedSize":35646,"gzipSize":5716},{"label":"node_modules","path":"./node_modules","statSize":3185437,"groups":[{"label":"echarts","path":"./node_modules/echarts","statSize":2664504,"groups":[{"label":"lib","path":"./node_modules/echarts/lib","statSize":2661478,"groups":[{"label":"data","path":"./node_modules/echarts/lib/data","statSize":167055,"groups":[{"label":"helper","path":"./node_modules/echarts/lib/data/helper","statSize":69462,"groups":[{"id":"+2Ke","label":"sourceType.js","path":"./node_modules/echarts/lib/data/helper/sourceType.js","statSize":2438,"parsedSize":314,"gzipSize":195},{"id":"/n1K","label":"completeDimensions.js","path":"./node_modules/echarts/lib/data/helper/completeDimensions.js","statSize":12301,"parsedSize":2190,"gzipSize":1120},{"id":"5KBG","label":"dataProvider.js","path":"./node_modules/echarts/lib/data/helper/dataProvider.js","statSize":11378,"parsedSize":3353,"gzipSize":1320},{"id":"NGRG","label":"linkList.js","path":"./node_modules/echarts/lib/data/helper/linkList.js","statSize":5528,"parsedSize":965,"gzipSize":498},{"id":"hcq/","label":"createDimensions.js","path":"./node_modules/echarts/lib/data/helper/createDimensions.js","statSize":2794,"parsedSize":320,"gzipSize":180},{"id":"kdOt","label":"sourceHelper.js","path":"./node_modules/echarts/lib/data/helper/sourceHelper.js","statSize":22402,"parsedSize":4753,"gzipSize":2091},{"id":"mvCM","label":"dimensionHelper.js","path":"./node_modules/echarts/lib/data/helper/dimensionHelper.js","statSize":5923,"parsedSize":1054,"gzipSize":613},{"id":"qVJQ","label":"dataStackHelper.js","path":"./node_modules/echarts/lib/data/helper/dataStackHelper.js","statSize":6698,"parsedSize":1002,"gzipSize":487}],"parsedSize":13951,"gzipSize":5276},{"id":"+jMe","label":"Tree.js","path":"./node_modules/echarts/lib/data/Tree.js","statSize":12501,"parsedSize":3079,"gzipSize":1138},{"id":"1DJE","label":"DataDimensionInfo.js","path":"./node_modules/echarts/lib/data/DataDimensionInfo.js","statSize":4298,"parsedSize":107,"gzipSize":112},{"id":"1Hui","label":"DataDiffer.js","path":"./node_modules/echarts/lib/data/DataDiffer.js","statSize":4644,"parsedSize":966,"gzipSize":470},{"id":"1uRk","label":"Graph.js","path":"./node_modules/echarts/lib/data/Graph.js","statSize":12422,"parsedSize":3836,"gzipSize":1285},{"id":"Rfu2","label":"List.js","path":"./node_modules/echarts/lib/data/List.js","statSize":53937,"parsedSize":14841,"gzipSize":5087},{"id":"iGPw","label":"OrdinalMeta.js","path":"./node_modules/echarts/lib/data/OrdinalMeta.js","statSize":4801,"parsedSize":823,"gzipSize":414},{"id":"rrAD","label":"Source.js","path":"./node_modules/echarts/lib/data/Source.js","statSize":4990,"parsedSize":712,"gzipSize":411}],"parsedSize":38315,"gzipSize":12505},{"label":"component","path":"./node_modules/echarts/lib/component","statSize":836444,"groups":[{"id":"+Dgo","label":"dataset.js","path":"./node_modules/echarts/lib/component/dataset.js","statSize":2597,"parsedSize":278,"gzipSize":224},{"label":"toolbox","path":"./node_modules/echarts/lib/component/toolbox","statSize":58603,"groups":[{"id":"+PQg","label":"ToolboxView.js","path":"./node_modules/echarts/lib/component/toolbox/ToolboxView.js","statSize":10373,"parsedSize":3150,"gzipSize":1437},{"label":"feature","path":"./node_modules/echarts/lib/component/toolbox/feature","statSize":43706,"groups":[{"id":"0pMY","label":"Brush.js","path":"./node_modules/echarts/lib/component/toolbox/feature/Brush.js","statSize":5842,"parsedSize":2646,"gzipSize":1290},{"id":"AbHi","label":"SaveAsImage.js","path":"./node_modules/echarts/lib/component/toolbox/feature/SaveAsImage.js","statSize":4132,"parsedSize":1459,"gzipSize":834},{"id":"DknX","label":"DataZoom.js","path":"./node_modules/echarts/lib/component/toolbox/feature/DataZoom.js","statSize":9649,"parsedSize":3293,"gzipSize":1563},{"id":"JMu0","label":"Restore.js","path":"./node_modules/echarts/lib/component/toolbox/feature/Restore.js","statSize":2603,"parsedSize":597,"gzipSize":402},{"id":"RTd5","label":"MagicType.js","path":"./node_modules/echarts/lib/component/toolbox/feature/MagicType.js","statSize":7134,"parsedSize":2700,"gzipSize":1264},{"id":"u9lB","label":"DataView.js","path":"./node_modules/echarts/lib/component/toolbox/feature/DataView.js","statSize":14346,"parsedSize":5557,"gzipSize":2360}],"parsedSize":16252,"gzipSize":6414},{"id":"auIi","label":"ToolboxModel.js","path":"./node_modules/echarts/lib/component/toolbox/ToolboxModel.js","statSize":2764,"parsedSize":622,"gzipSize":403},{"id":"dCQY","label":"featureManager.js","path":"./node_modules/echarts/lib/component/toolbox/featureManager.js","statSize":1760,"parsedSize":87,"gzipSize":85}],"parsedSize":20111,"gzipSize":7857},{"label":"axisPointer","path":"./node_modules/echarts/lib/component/axisPointer","statSize":81553,"groups":[{"id":"+bS+","label":"SingleAxisPointer.js","path":"./node_modules/echarts/lib/component/axisPointer/SingleAxisPointer.js","statSize":5192,"parsedSize":1337,"gzipSize":745},{"id":"56C7","label":"AxisPointerView.js","path":"./node_modules/echarts/lib/component/axisPointer/AxisPointerView.js","statSize":3011,"parsedSize":592,"gzipSize":326},{"id":"D7EH","label":"CartesianAxisPointer.js","path":"./node_modules/echarts/lib/component/axisPointer/CartesianAxisPointer.js","statSize":5357,"parsedSize":1457,"gzipSize":775},{"id":"DpwM","label":"globalListener.js","path":"./node_modules/echarts/lib/component/axisPointer/globalListener.js","statSize":4519,"parsedSize":975,"gzipSize":471},{"id":"Ou7x","label":"BaseAxisPointer.js","path":"./node_modules/echarts/lib/component/axisPointer/BaseAxisPointer.js","statSize":14399,"parsedSize":4646,"gzipSize":1876},{"id":"OxCu","label":"findPointFromSeries.js","path":"./node_modules/echarts/lib/component/axisPointer/findPointFromSeries.js","statSize":3023,"parsedSize":595,"gzipSize":375},{"id":"QCrJ","label":"modelHelper.js","path":"./node_modules/echarts/lib/component/axisPointer/modelHelper.js","statSize":12179,"parsedSize":3051,"gzipSize":1389},{"id":"QbUQ","label":"PolarAxisPointer.js","path":"./node_modules/echarts/lib/component/axisPointer/PolarAxisPointer.js","statSize":5647,"parsedSize":1602,"gzipSize":885},{"id":"TCXJ","label":"AxisPointerModel.js","path":"./node_modules/echarts/lib/component/axisPointer/AxisPointerModel.js","statSize":4191,"parsedSize":874,"gzipSize":523},{"id":"dtW/","label":"axisTrigger.js","path":"./node_modules/echarts/lib/component/axisPointer/axisTrigger.js","statSize":15535,"parsedSize":3970,"gzipSize":1746},{"id":"zAPJ","label":"viewHelper.js","path":"./node_modules/echarts/lib/component/axisPointer/viewHelper.js","statSize":8500,"parsedSize":2552,"gzipSize":1250}],"parsedSize":21651,"gzipSize":7853},{"id":"/99E","label":"calendar.js","path":"./node_modules/echarts/lib/component/calendar.js","statSize":913,"parsedSize":46,"gzipSize":60},{"label":"axis","path":"./node_modules/echarts/lib/component/axis","statSize":69814,"groups":[{"id":"0BNI","label":"AngleAxisView.js","path":"./node_modules/echarts/lib/component/axis/AngleAxisView.js","statSize":11744,"parsedSize":3974,"gzipSize":1626},{"id":"43ae","label":"AxisView.js","path":"./node_modules/echarts/lib/component/axis/AxisView.js","statSize":4202,"parsedSize":871,"gzipSize":413},{"id":"BqCv","label":"ParallelAxisView.js","path":"./node_modules/echarts/lib/component/axis/ParallelAxisView.js","statSize":6593,"parsedSize":2061,"gzipSize":1036},{"id":"LKZ0","label":"axisSplitHelper.js","path":"./node_modules/echarts/lib/component/axis/axisSplitHelper.js","statSize":4178,"parsedSize":939,"gzipSize":570},{"id":"MRvz","label":"parallelAxisAction.js","path":"./node_modules/echarts/lib/component/axis/parallelAxisAction.js","statSize":2342,"parsedSize":356,"gzipSize":196},{"id":"oqQy","label":"SingleAxisView.js","path":"./node_modules/echarts/lib/component/axis/SingleAxisView.js","statSize":5093,"parsedSize":1436,"gzipSize":799},{"id":"rFvp","label":"RadiusAxisView.js","path":"./node_modules/echarts/lib/component/axis/RadiusAxisView.js","statSize":6674,"parsedSize":1949,"gzipSize":869},{"id":"s48c","label":"CartesianAxisView.js","path":"./node_modules/echarts/lib/component/axis/CartesianAxisView.js","statSize":6961,"parsedSize":2029,"gzipSize":949},{"id":"vjPX","label":"AxisBuilder.js","path":"./node_modules/echarts/lib/component/axis/AxisBuilder.js","statSize":22027,"parsedSize":6900,"gzipSize":2847}],"parsedSize":20515,"gzipSize":6732},{"id":"0O1a","label":"visualMapPiecewise.js","path":"./node_modules/echarts/lib/component/visualMapPiecewise.js","statSize":1958,"parsedSize":120,"gzipSize":120},{"id":"1bf2","label":"radiusAxis.js","path":"./node_modules/echarts/lib/component/radiusAxis.js","statSize":867,"parsedSize":36,"gzipSize":54},{"id":"2tOJ","label":"markPoint.js","path":"./node_modules/echarts/lib/component/markPoint.js","statSize":1870,"parsedSize":117,"gzipSize":117},{"id":"3n/B","label":"angleAxis.js","path":"./node_modules/echarts/lib/component/angleAxis.js","statSize":866,"parsedSize":36,"gzipSize":54},{"id":"4SW2","label":"visualMapContinuous.js","path":"./node_modules/echarts/lib/component/visualMapContinuous.js","statSize":1960,"parsedSize":120,"gzipSize":120},{"id":"4V7L","label":"singleAxis.js","path":"./node_modules/echarts/lib/component/singleAxis.js","statSize":1861,"parsedSize":121,"gzipSize":124},{"label":"dataZoom","path":"./node_modules/echarts/lib/component/dataZoom","statSize":109157,"groups":[{"id":"5Hn/","label":"SliderZoomView.js","path":"./node_modules/echarts/lib/component/dataZoom/SliderZoomView.js","statSize":23092,"parsedSize":8763,"gzipSize":3311},{"id":"8Mpj","label":"InsideZoomModel.js","path":"./node_modules/echarts/lib/component/dataZoom/InsideZoomModel.js","statSize":2192,"parsedSize":203,"gzipSize":166},{"id":"9Owa","label":"history.js","path":"./node_modules/echarts/lib/component/dataZoom/history.js","statSize":3905,"parsedSize":603,"gzipSize":371},{"id":"E7aA","label":"roams.js","path":"./node_modules/echarts/lib/component/dataZoom/roams.js","statSize":7665,"parsedSize":1752,"gzipSize":833},{"id":"GWV8","label":"AxisProxy.js","path":"./node_modules/echarts/lib/component/dataZoom/AxisProxy.js","statSize":17824,"parsedSize":3924,"gzipSize":1648},{"id":"KAfT","label":"dataZoomAction.js","path":"./node_modules/echarts/lib/component/dataZoom/dataZoomAction.js","statSize":2434,"parsedSize":429,"gzipSize":271},{"id":"WO3U","label":"SelectZoomView.js","path":"./node_modules/echarts/lib/component/dataZoom/SelectZoomView.js","statSize":1725,"parsedSize":77,"gzipSize":96},{"id":"WbrJ","label":"InsideZoomView.js","path":"./node_modules/echarts/lib/component/dataZoom/InsideZoomView.js","statSize":8679,"parsedSize":2673,"gzipSize":1091},{"id":"b/SY","label":"dataZoomProcessor.js","path":"./node_modules/echarts/lib/component/dataZoom/dataZoomProcessor.js","statSize":4677,"parsedSize":746,"gzipSize":362},{"id":"cuL/","label":"typeDefaulter.js","path":"./node_modules/echarts/lib/component/dataZoom/typeDefaulter.js","statSize":1764,"parsedSize":90,"gzipSize":102},{"id":"envY","label":"SliderZoomModel.js","path":"./node_modules/echarts/lib/component/dataZoom/SliderZoomModel.js","statSize":3796,"parsedSize":694,"gzipSize":445},{"id":"ilLo","label":"DataZoomView.js","path":"./node_modules/echarts/lib/component/dataZoom/DataZoomView.js","statSize":3636,"parsedSize":541,"gzipSize":325},{"id":"r9WW","label":"SelectZoomModel.js","path":"./node_modules/echarts/lib/component/dataZoom/SelectZoomModel.js","statSize":1728,"parsedSize":77,"gzipSize":96},{"id":"s1Aj","label":"helper.js","path":"./node_modules/echarts/lib/component/dataZoom/helper.js","statSize":5316,"parsedSize":974,"gzipSize":505},{"id":"sJ4e","label":"DataZoomModel.js","path":"./node_modules/echarts/lib/component/dataZoom/DataZoomModel.js","statSize":20724,"parsedSize":4838,"gzipSize":1763}],"parsedSize":26384,"gzipSize":8936},{"label":"helper","path":"./node_modules/echarts/lib/component/helper","statSize":89378,"groups":[{"id":"5Mek","label":"RoamController.js","path":"./node_modules/echarts/lib/component/helper/RoamController.js","statSize":9134,"parsedSize":2321,"gzipSize":974},{"id":"LBXi","label":"MapDraw.js","path":"./node_modules/echarts/lib/component/helper/MapDraw.js","statSize":14872,"parsedSize":4748,"gzipSize":2115},{"id":"NKek","label":"cursorHelper.js","path":"./node_modules/echarts/lib/component/helper/cursorHelper.js","statSize":2209,"parsedSize":208,"gzipSize":179},{"id":"XCrL","label":"BrushTargetManager.js","path":"./node_modules/echarts/lib/component/helper/BrushTargetManager.js","statSize":14633,"parsedSize":4185,"gzipSize":1691},{"id":"YpIy","label":"roamHelper.js","path":"./node_modules/echarts/lib/component/helper/roamHelper.js","statSize":2951,"parsedSize":371,"gzipSize":240},{"id":"ZJ+T","label":"brushHelper.js","path":"./node_modules/echarts/lib/component/helper/brushHelper.js","statSize":2875,"parsedSize":461,"gzipSize":293},{"id":"kQD9","label":"selectableMixin.js","path":"./node_modules/echarts/lib/component/helper/selectableMixin.js","statSize":4430,"parsedSize":794,"gzipSize":313},{"id":"mcsk","label":"interactionMutex.js","path":"./node_modules/echarts/lib/component/helper/interactionMutex.js","statSize":2438,"parsedSize":327,"gzipSize":220},{"id":"oDOe","label":"BrushController.js","path":"./node_modules/echarts/lib/component/helper/BrushController.js","statSize":27532,"parsedSize":8550,"gzipSize":3310},{"id":"og9+","label":"sliderMove.js","path":"./node_modules/echarts/lib/component/helper/sliderMove.js","statSize":4866,"parsedSize":619,"gzipSize":359},{"id":"v/cD","label":"listComponent.js","path":"./node_modules/echarts/lib/component/helper/listComponent.js","statSize":3438,"parsedSize":594,"gzipSize":382}],"parsedSize":23178,"gzipSize":8534},{"label":"legend","path":"./node_modules/echarts/lib/component/legend","statSize":55409,"groups":[{"id":"6JAQ","label":"LegendModel.js","path":"./node_modules/echarts/lib/component/legend/LegendModel.js","statSize":9896,"parsedSize":2939,"gzipSize":1256},{"id":"6f6q","label":"legendAction.js","path":"./node_modules/echarts/lib/component/legend/legendAction.js","statSize":4354,"parsedSize":889,"gzipSize":382},{"id":"8RN9","label":"LegendView.js","path":"./node_modules/echarts/lib/component/legend/LegendView.js","statSize":17013,"parsedSize":5476,"gzipSize":2268},{"id":"JIsR","label":"legendFilter.js","path":"./node_modules/echarts/lib/component/legend/legendFilter.js","statSize":2145,"parsedSize":196,"gzipSize":165},{"id":"R0U9","label":"scrollableLegendAction.js","path":"./node_modules/echarts/lib/component/legend/scrollableLegendAction.js","statSize":2100,"parsedSize":219,"gzipSize":164},{"id":"dp0Z","label":"ScrollableLegendModel.js","path":"./node_modules/echarts/lib/component/legend/ScrollableLegendModel.js","statSize":3522,"parsedSize":832,"gzipSize":473},{"id":"wLWv","label":"ScrollableLegendView.js","path":"./node_modules/echarts/lib/component/legend/ScrollableLegendView.js","statSize":16379,"parsedSize":4407,"gzipSize":1930}],"parsedSize":14958,"gzipSize":5381},{"label":"tooltip","path":"./node_modules/echarts/lib/component/tooltip","statSize":49113,"groups":[{"id":"7XrG","label":"TooltipModel.js","path":"./node_modules/echarts/lib/component/tooltip/TooltipModel.js","statSize":4367,"parsedSize":671,"gzipSize":423},{"id":"XAC3","label":"TooltipView.js","path":"./node_modules/echarts/lib/component/tooltip/TooltipView.js","statSize":25764,"parsedSize":8264,"gzipSize":3135},{"id":"aYad","label":"TooltipContent.js","path":"./node_modules/echarts/lib/component/tooltip/TooltipContent.js","statSize":12019,"parsedSize":4061,"gzipSize":1769},{"id":"oJvE","label":"TooltipRichContent.js","path":"./node_modules/echarts/lib/component/tooltip/TooltipRichContent.js","statSize":6963,"parsedSize":2362,"gzipSize":993}],"parsedSize":15358,"gzipSize":5426},{"id":"80cc","label":"legend.js","path":"./node_modules/echarts/lib/component/legend.js","statSize":2111,"parsedSize":211,"gzipSize":187},{"id":"80zD","label":"geo.js","path":"./node_modules/echarts/lib/component/geo.js","statSize":2611,"parsedSize":535,"gzipSize":306},{"id":"997V","label":"radar.js","path":"./node_modules/echarts/lib/component/radar.js","statSize":895,"parsedSize":46,"gzipSize":60},{"id":"AKXb","label":"axisPointer.js","path":"./node_modules/echarts/lib/component/axisPointer.js","statSize":3288,"parsedSize":503,"gzipSize":303},{"label":"marker","path":"./node_modules/echarts/lib/component/marker","statSize":54791,"groups":[{"id":"ARaV","label":"MarkLineView.js","path":"./node_modules/echarts/lib/component/marker/MarkLineView.js","statSize":12952,"parsedSize":3961,"gzipSize":1694},{"id":"Fkmw","label":"MarkAreaView.js","path":"./node_modules/echarts/lib/component/marker/MarkAreaView.js","statSize":11946,"parsedSize":3660,"gzipSize":1614},{"id":"Mlni","label":"MarkerModel.js","path":"./node_modules/echarts/lib/component/marker/MarkerModel.js","statSize":5147,"parsedSize":1325,"gzipSize":730},{"id":"SZjP","label":"MarkerView.js","path":"./node_modules/echarts/lib/component/marker/MarkerView.js","statSize":2516,"parsedSize":422,"gzipSize":252},{"id":"TTCf","label":"MarkLineModel.js","path":"./node_modules/echarts/lib/component/marker/MarkLineModel.js","statSize":2171,"parsedSize":320,"gzipSize":247},{"id":"ZtEr","label":"MarkAreaModel.js","path":"./node_modules/echarts/lib/component/marker/MarkAreaModel.js","statSize":2209,"parsedSize":236,"gzipSize":188},{"id":"orv6","label":"MarkPointModel.js","path":"./node_modules/echarts/lib/component/marker/MarkPointModel.js","statSize":2070,"parsedSize":239,"gzipSize":202},{"id":"vEM8","label":"MarkPointView.js","path":"./node_modules/echarts/lib/component/marker/MarkPointView.js","statSize":6909,"parsedSize":1840,"gzipSize":893},{"id":"vx1D","label":"markerHelper.js","path":"./node_modules/echarts/lib/component/marker/markerHelper.js","statSize":8871,"parsedSize":2018,"gzipSize":912}],"parsedSize":14021,"gzipSize":4757},{"label":"visualMap","path":"./node_modules/echarts/lib/component/visualMap","statSize":93827,"groups":[{"id":"DZTl","label":"preprocessor.js","path":"./node_modules/echarts/lib/component/visualMap/preprocessor.js","statSize":2564,"parsedSize":426,"gzipSize":252},{"id":"E8YU","label":"helper.js","path":"./node_modules/echarts/lib/component/visualMap/helper.js","statSize":3523,"parsedSize":684,"gzipSize":448},{"id":"H4Wn","label":"visualMapAction.js","path":"./node_modules/echarts/lib/component/visualMap/visualMapAction.js","statSize":1974,"parsedSize":211,"gzipSize":162},{"id":"OlnU","label":"ContinuousModel.js","path":"./node_modules/echarts/lib/component/visualMap/ContinuousModel.js","statSize":8482,"parsedSize":2451,"gzipSize":1088},{"id":"Osoq","label":"typeDefaulter.js","path":"./node_modules/echarts/lib/component/visualMap/typeDefaulter.js","statSize":1944,"parsedSize":183,"gzipSize":166},{"id":"XiVP","label":"PiecewiseView.js","path":"./node_modules/echarts/lib/component/visualMap/PiecewiseView.js","statSize":7589,"parsedSize":2494,"gzipSize":1159},{"id":"gZam","label":"ContinuousView.js","path":"./node_modules/echarts/lib/component/visualMap/ContinuousView.js","statSize":24848,"parsedSize":9512,"gzipSize":3208},{"id":"mlpt","label":"PiecewiseModel.js","path":"./node_modules/echarts/lib/component/visualMap/PiecewiseModel.js","statSize":16772,"parsedSize":4841,"gzipSize":1991},{"id":"w2H/","label":"visualEncoding.js","path":"./node_modules/echarts/lib/component/visualMap/visualEncoding.js","statSize":4314,"parsedSize":1031,"gzipSize":554},{"id":"wH4Y","label":"VisualMapModel.js","path":"./node_modules/echarts/lib/component/visualMap/VisualMapModel.js","statSize":16356,"parsedSize":4286,"gzipSize":1844},{"id":"zO13","label":"VisualMapView.js","path":"./node_modules/echarts/lib/component/visualMap/VisualMapView.js","statSize":5461,"parsedSize":1373,"gzipSize":756}],"parsedSize":27492,"gzipSize":8986},{"label":"timeline","path":"./node_modules/echarts/lib/component/timeline","statSize":44412,"groups":[{"id":"F8oC","label":"TimelineModel.js","path":"./node_modules/echarts/lib/component/timeline/TimelineModel.js","statSize":5285,"parsedSize":1507,"gzipSize":763},{"id":"GQGX","label":"preprocessor.js","path":"./node_modules/echarts/lib/component/timeline/preprocessor.js","statSize":3615,"parsedSize":858,"gzipSize":436},{"id":"Gp87","label":"TimelineView.js","path":"./node_modules/echarts/lib/component/timeline/TimelineView.js","statSize":1726,"parsedSize":70,"gzipSize":89},{"id":"Pwgp","label":"TimelineAxis.js","path":"./node_modules/echarts/lib/component/timeline/TimelineAxis.js","statSize":2678,"parsedSize":318,"gzipSize":215},{"id":"ZuqD","label":"SliderTimelineModel.js","path":"./node_modules/echarts/lib/component/timeline/SliderTimelineModel.js","statSize":5474,"parsedSize":2389,"gzipSize":1084},{"id":"iEm+","label":"timelineAction.js","path":"./node_modules/echarts/lib/component/timeline/timelineAction.js","statSize":2593,"parsedSize":571,"gzipSize":281},{"id":"oYIf","label":"SliderTimelineView.js","path":"./node_modules/echarts/lib/component/timeline/SliderTimelineView.js","statSize":21301,"parsedSize":7915,"gzipSize":3107},{"id":"od06","label":"typeDefaulter.js","path":"./node_modules/echarts/lib/component/timeline/typeDefaulter.js","statSize":1740,"parsedSize":90,"gzipSize":102}],"parsedSize":13718,"gzipSize":5257},{"id":"FlXs","label":"parallel.js","path":"./node_modules/echarts/lib/component/parallel.js","statSize":4691,"parsedSize":1581,"gzipSize":668},{"id":"ILx8","label":"grid.js","path":"./node_modules/echarts/lib/component/grid.js","statSize":892,"parsedSize":46,"gzipSize":60},{"id":"JRc4","label":"timeline.js","path":"./node_modules/echarts/lib/component/timeline.js","statSize":1922,"parsedSize":110,"gzipSize":114},{"id":"LbEf","label":"polar.js","path":"./node_modules/echarts/lib/component/polar.js","statSize":2073,"parsedSize":179,"gzipSize":164},{"id":"LjPz","label":"parallelAxis.js","path":"./node_modules/echarts/lib/component/parallelAxis.js","statSize":914,"parsedSize":46,"gzipSize":60},{"id":"Mn6+","label":"markArea.js","path":"./node_modules/echarts/lib/component/markArea.js","statSize":1824,"parsedSize":115,"gzipSize":114},{"label":"brush","path":"./node_modules/echarts/lib/component/brush","statSize":32445,"groups":[{"id":"OCS9","label":"preprocessor.js","path":"./node_modules/echarts/lib/component/brush/preprocessor.js","statSize":3066,"parsedSize":559,"gzipSize":339},{"id":"S+iL","label":"brushAction.js","path":"./node_modules/echarts/lib/component/brush/brushAction.js","statSize":2712,"parsedSize":339,"gzipSize":179},{"id":"hdbT","label":"BrushView.js","path":"./node_modules/echarts/lib/component/brush/BrushView.js","statSize":4369,"parsedSize":941,"gzipSize":478},{"id":"icur","label":"BrushModel.js","path":"./node_modules/echarts/lib/component/brush/BrushModel.js","statSize":5453,"parsedSize":1187,"gzipSize":627},{"id":"n3NR","label":"visualEncoding.js","path":"./node_modules/echarts/lib/component/brush/visualEncoding.js","statSize":12224,"parsedSize":3098,"gzipSize":1427},{"id":"zlsk","label":"selector.js","path":"./node_modules/echarts/lib/component/brush/selector.js","statSize":4621,"parsedSize":973,"gzipSize":463}],"parsedSize":7097,"gzipSize":2803},{"id":"Oq2I","label":"tooltip.js","path":"./node_modules/echarts/lib/component/tooltip.js","statSize":2192,"parsedSize":256,"gzipSize":163},{"id":"P7ry","label":"legendScroll.js","path":"./node_modules/echarts/lib/component/legendScroll.js","statSize":945,"parsedSize":56,"gzipSize":66},{"id":"UeW/","label":"graphic.js","path":"./node_modules/echarts/lib/component/graphic.js","statSize":17287,"parsedSize":4029,"gzipSize":1723},{"id":"UkNE","label":"gridSimple.js","path":"./node_modules/echarts/lib/component/gridSimple.js","statSize":2372,"parsedSize":397,"gzipSize":293},{"id":"XuY+","label":"dataZoomSlider.js","path":"./node_modules/echarts/lib/component/dataZoomSlider.js","statSize":1064,"parsedSize":86,"gzipSize":82},{"id":"YsUA","label":"toolbox.js","path":"./node_modules/echarts/lib/component/toolbox.js","statSize":1065,"parsedSize":86,"gzipSize":82},{"label":"geo","path":"./node_modules/echarts/lib/component/geo","statSize":2439,"groups":[{"id":"jpt2","label":"GeoView.js","path":"./node_modules/echarts/lib/component/geo/GeoView.js","statSize":2439,"parsedSize":428,"gzipSize":271}],"parsedSize":428,"gzipSize":271},{"id":"l2wH","label":"dataZoomSelect.js","path":"./node_modules/echarts/lib/component/dataZoomSelect.js","statSize":1064,"parsedSize":86,"gzipSize":83},{"label":"radar","path":"./node_modules/echarts/lib/component/radar","statSize":7087,"groups":[{"id":"lVde","label":"RadarView.js","path":"./node_modules/echarts/lib/component/radar/RadarView.js","statSize":7087,"parsedSize":1931,"gzipSize":918}],"parsedSize":1931,"gzipSize":918},{"id":"miEh","label":"title.js","path":"./node_modules/echarts/lib/component/title.js","statSize":7562,"parsedSize":2118,"gzipSize":1029},{"id":"p1Ck","label":"markLine.js","path":"./node_modules/echarts/lib/component/markLine.js","statSize":1824,"parsedSize":115,"gzipSize":116},{"id":"swsf","label":"brush.js","path":"./node_modules/echarts/lib/component/brush.js","statSize":1921,"parsedSize":120,"gzipSize":120},{"id":"tQk0","label":"visualMap.js","path":"./node_modules/echarts/lib/component/visualMap.js","statSize":860,"parsedSize":36,"gzipSize":54},{"id":"v/z1","label":"dataZoomInside.js","path":"./node_modules/echarts/lib/component/dataZoomInside.js","statSize":1064,"parsedSize":86,"gzipSize":83},{"label":"calendar","path":"./node_modules/echarts/lib/component/calendar","statSize":13361,"groups":[{"id":"w6Zv","label":"CalendarView.js","path":"./node_modules/echarts/lib/component/calendar/CalendarView.js","statSize":13361,"parsedSize":5219,"gzipSize":2123}],"parsedSize":5219,"gzipSize":2123},{"id":"wQkr","label":"dataZoom.js","path":"./node_modules/echarts/lib/component/dataZoom.js","statSize":851,"parsedSize":36,"gzipSize":54},{"id":"zz1u","label":"axis.js","path":"./node_modules/echarts/lib/component/axis.js","statSize":871,"parsedSize":36,"gzipSize":54}],"parsedSize":223850,"gzipSize":71733},{"label":"chart","path":"./node_modules/echarts/lib/chart","statSize":836007,"groups":[{"label":"helper","path":"./node_modules/echarts/lib/chart/helper","statSize":119371,"groups":[{"id":"+K7g","label":"focusNodeAdjacencyAction.js","path":"./node_modules/echarts/lib/chart/helper/focusNodeAdjacencyAction.js","statSize":2202,"parsedSize":280,"gzipSize":139},{"id":"/gZK","label":"createListSimply.js","path":"./node_modules/echarts/lib/chart/helper/createListSimply.js","statSize":2602,"parsedSize":210,"gzipSize":178},{"id":"1bHA","label":"Symbol.js","path":"./node_modules/echarts/lib/chart/helper/Symbol.js","statSize":11847,"parsedSize":3500,"gzipSize":1541},{"id":"6n1D","label":"LineDraw.js","path":"./node_modules/echarts/lib/chart/helper/LineDraw.js","statSize":5541,"parsedSize":1732,"gzipSize":719},{"id":"CqCN","label":"createRenderPlanner.js","path":"./node_modules/echarts/lib/chart/helper/createRenderPlanner.js","statSize":2518,"parsedSize":251,"gzipSize":173},{"id":"DDYI","label":"createClipPathFromCoordSys.js","path":"./node_modules/echarts/lib/chart/helper/createClipPathFromCoordSys.js","statSize":3851,"parsedSize":806,"gzipSize":432},{"id":"GnMB","label":"EffectSymbol.js","path":"./node_modules/echarts/lib/chart/helper/EffectSymbol.js","statSize":7767,"parsedSize":2756,"gzipSize":1139},{"id":"Jd65","label":"EffectPolyline.js","path":"./node_modules/echarts/lib/chart/helper/EffectPolyline.js","statSize":4179,"parsedSize":971,"gzipSize":515},{"id":"PiQa","label":"Polyline.js","path":"./node_modules/echarts/lib/chart/helper/Polyline.js","statSize":3626,"parsedSize":910,"gzipSize":439},{"id":"QDQV","label":"multipleGraphEdgeHelper.js","path":"./node_modules/echarts/lib/chart/helper/multipleGraphEdgeHelper.js","statSize":7970,"parsedSize":1402,"gzipSize":690},{"id":"RjA7","label":"labelHelper.js","path":"./node_modules/echarts/lib/chart/helper/labelHelper.js","statSize":2377,"parsedSize":250,"gzipSize":196},{"id":"US3d","label":"LargeLineDraw.js","path":"./node_modules/echarts/lib/chart/helper/LargeLineDraw.js","statSize":6828,"parsedSize":2351,"gzipSize":988},{"id":"ao1T","label":"createListFromArray.js","path":"./node_modules/echarts/lib/chart/helper/createListFromArray.js","statSize":5833,"parsedSize":1303,"gzipSize":753},{"id":"bzOU","label":"Line.js","path":"./node_modules/echarts/lib/chart/helper/Line.js","statSize":14826,"parsedSize":5196,"gzipSize":2084},{"id":"d1IL","label":"createGraphFromNodeEdge.js","path":"./node_modules/echarts/lib/chart/helper/createGraphFromNodeEdge.js","statSize":4020,"parsedSize":777,"gzipSize":509},{"id":"dZZy","label":"SymbolDraw.js","path":"./node_modules/echarts/lib/chart/helper/SymbolDraw.js","statSize":6623,"parsedSize":2074,"gzipSize":889},{"id":"gOx9","label":"treeHelper.js","path":"./node_modules/echarts/lib/chart/helper/treeHelper.js","statSize":3190,"parsedSize":613,"gzipSize":354},{"id":"sK5G","label":"whiskerBoxCommon.js","path":"./node_modules/echarts/lib/chart/helper/whiskerBoxCommon.js","statSize":5027,"parsedSize":1185,"gzipSize":619},{"id":"sOJ4","label":"LinePath.js","path":"./node_modules/echarts/lib/chart/helper/LinePath.js","statSize":2803,"parsedSize":686,"gzipSize":371},{"id":"uA0k","label":"EffectLine.js","path":"./node_modules/echarts/lib/chart/helper/EffectLine.js","statSize":7264,"parsedSize":2515,"gzipSize":1094},{"id":"uv9t","label":"LargeSymbolDraw.js","path":"./node_modules/echarts/lib/chart/helper/LargeSymbolDraw.js","statSize":8477,"parsedSize":2842,"gzipSize":1219}],"parsedSize":32610,"gzipSize":10795},{"label":"parallel","path":"./node_modules/echarts/lib/chart/parallel","statSize":15843,"groups":[{"id":"+bDV","label":"ParallelSeries.js","path":"./node_modules/echarts/lib/chart/parallel/ParallelSeries.js","statSize":4152,"parsedSize":900,"gzipSize":522},{"id":"2Ow2","label":"ParallelView.js","path":"./node_modules/echarts/lib/chart/parallel/ParallelView.js","statSize":8657,"parsedSize":2109,"gzipSize":973},{"id":"CWSg","label":"parallelVisual.js","path":"./node_modules/echarts/lib/chart/parallel/parallelVisual.js","statSize":3034,"parsedSize":647,"gzipSize":365}],"parsedSize":3656,"gzipSize":1560},{"label":"treemap","path":"./node_modules/echarts/lib/chart/treemap","statSize":77384,"groups":[{"id":"+pdh","label":"treemapAction.js","path":"./node_modules/echarts/lib/chart/treemap/treemapAction.js","statSize":2624,"parsedSize":537,"gzipSize":316},{"id":"IkDM","label":"Breadcrumb.js","path":"./node_modules/echarts/lib/chart/treemap/Breadcrumb.js","statSize":6251,"parsedSize":1799,"gzipSize":927},{"id":"M9eA","label":"treemapVisual.js","path":"./node_modules/echarts/lib/chart/treemap/treemapVisual.js","statSize":7769,"parsedSize":1939,"gzipSize":948},{"id":"fm2I","label":"treemapLayout.js","path":"./node_modules/echarts/lib/chart/treemap/treemapLayout.js","statSize":18683,"parsedSize":4801,"gzipSize":2272},{"id":"gLqW","label":"TreemapSeries.js","path":"./node_modules/echarts/lib/chart/treemap/TreemapSeries.js","statSize":12822,"parsedSize":3093,"gzipSize":1444},{"id":"ix3c","label":"TreemapView.js","path":"./node_modules/echarts/lib/chart/treemap/TreemapView.js","statSize":29235,"parsedSize":8993,"gzipSize":3460}],"parsedSize":21162,"gzipSize":8062},{"label":"sankey","path":"./node_modules/echarts/lib/chart/sankey","statSize":43851,"groups":[{"id":"+u5N","label":"SankeySeries.js","path":"./node_modules/echarts/lib/chart/sankey/SankeySeries.js","statSize":6880,"parsedSize":2138,"gzipSize":1001},{"id":"Cuh7","label":"SankeyView.js","path":"./node_modules/echarts/lib/chart/sankey/SankeyView.js","statSize":14150,"parsedSize":5747,"gzipSize":1972},{"id":"IeRB","label":"sankeyAction.js","path":"./node_modules/echarts/lib/chart/sankey/sankeyAction.js","statSize":2084,"parsedSize":252,"gzipSize":195},{"id":"Mxrs","label":"sankeyVisual.js","path":"./node_modules/echarts/lib/chart/sankey/sankeyVisual.js","statSize":2760,"parsedSize":481,"gzipSize":303},{"id":"wTOJ","label":"sankeyLayout.js","path":"./node_modules/echarts/lib/chart/sankey/sankeyLayout.js","statSize":17977,"parsedSize":5069,"gzipSize":1709}],"parsedSize":13687,"gzipSize":4549},{"label":"pie","path":"./node_modules/echarts/lib/chart/pie","statSize":37080,"groups":[{"id":"/vN/","label":"PieSeries.js","path":"./node_modules/echarts/lib/chart/pie/PieSeries.js","statSize":7069,"parsedSize":1932,"gzipSize":984},{"id":"1A4n","label":"PieView.js","path":"./node_modules/echarts/lib/chart/pie/PieView.js","statSize":12882,"parsedSize":4686,"gzipSize":1917},{"id":"9Z3y","label":"pieLayout.js","path":"./node_modules/echarts/lib/chart/pie/pieLayout.js","statSize":6161,"parsedSize":1466,"gzipSize":772},{"id":"XhgW","label":"labelLayout.js","path":"./node_modules/echarts/lib/chart/pie/labelLayout.js","statSize":10968,"parsedSize":3179,"gzipSize":1558}],"parsedSize":11263,"gzipSize":4620},{"label":"graph","path":"./node_modules/echarts/lib/chart/graph","statSize":71605,"groups":[{"id":"0nGg","label":"GraphView.js","path":"./node_modules/echarts/lib/chart/graph/GraphView.js","statSize":12726,"parsedSize":5277,"gzipSize":1918},{"id":"4RQY","label":"simpleLayout.js","path":"./node_modules/echarts/lib/chart/graph/simpleLayout.js","statSize":3055,"parsedSize":523,"gzipSize":353},{"id":"Goha","label":"adjustEdge.js","path":"./node_modules/echarts/lib/chart/graph/adjustEdge.js","statSize":6071,"parsedSize":1510,"gzipSize":701},{"id":"ITiI","label":"forceHelper.js","path":"./node_modules/echarts/lib/chart/graph/forceHelper.js","statSize":5612,"parsedSize":1125,"gzipSize":601},{"id":"KGuM","label":"createView.js","path":"./node_modules/echarts/lib/chart/graph/createView.js","statSize":3787,"parsedSize":836,"gzipSize":484},{"id":"LRsb","label":"circularLayoutHelper.js","path":"./node_modules/echarts/lib/chart/graph/circularLayoutHelper.js","statSize":5774,"parsedSize":1195,"gzipSize":667},{"id":"NAKW","label":"circularLayout.js","path":"./node_modules/echarts/lib/chart/graph/circularLayout.js","statSize":1940,"parsedSize":159,"gzipSize":145},{"id":"T6W2","label":"categoryFilter.js","path":"./node_modules/echarts/lib/chart/graph/categoryFilter.js","statSize":2569,"parsedSize":395,"gzipSize":286},{"id":"TXKS","label":"edgeVisual.js","path":"./node_modules/echarts/lib/chart/graph/edgeVisual.js","statSize":3508,"parsedSize":943,"gzipSize":416},{"id":"akwy","label":"categoryVisual.js","path":"./node_modules/echarts/lib/chart/graph/categoryVisual.js","statSize":3366,"parsedSize":741,"gzipSize":391},{"id":"hD/x","label":"graphHelper.js","path":"./node_modules/echarts/lib/chart/graph/graphHelper.js","statSize":2322,"parsedSize":293,"gzipSize":226},{"id":"iZVd","label":"graphAction.js","path":"./node_modules/echarts/lib/chart/graph/graphAction.js","statSize":2475,"parsedSize":314,"gzipSize":224},{"id":"pzOI","label":"forceLayout.js","path":"./node_modules/echarts/lib/chart/graph/forceLayout.js","statSize":6813,"parsedSize":1936,"gzipSize":999},{"id":"rbn0","label":"simpleLayoutHelper.js","path":"./node_modules/echarts/lib/chart/graph/simpleLayoutHelper.js","statSize":2815,"parsedSize":556,"gzipSize":347},{"id":"z81E","label":"GraphSeries.js","path":"./node_modules/echarts/lib/chart/graph/GraphSeries.js","statSize":8772,"parsedSize":3027,"gzipSize":1349}],"parsedSize":18830,"gzipSize":6692},{"id":"1FNb","label":"graph.js","path":"./node_modules/echarts/lib/chart/graph.js","statSize":2586,"parsedSize":415,"gzipSize":268},{"label":"candlestick","path":"./node_modules/echarts/lib/chart/candlestick","statSize":26444,"groups":[{"id":"1VkX","label":"CandlestickView.js","path":"./node_modules/echarts/lib/chart/candlestick/CandlestickView.js","statSize":9755,"parsedSize":3504,"gzipSize":1361},{"id":"a1DW","label":"CandlestickSeries.js","path":"./node_modules/echarts/lib/chart/candlestick/CandlestickSeries.js","statSize":3647,"parsedSize":954,"gzipSize":534},{"id":"fAbo","label":"candlestickVisual.js","path":"./node_modules/echarts/lib/chart/candlestick/candlestickVisual.js","statSize":3249,"parsedSize":646,"gzipSize":372},{"id":"g8A/","label":"preprocessor.js","path":"./node_modules/echarts/lib/chart/candlestick/preprocessor.js","statSize":1963,"parsedSize":161,"gzipSize":140},{"id":"vxwL","label":"candlestickLayout.js","path":"./node_modules/echarts/lib/chart/candlestick/candlestickLayout.js","statSize":7830,"parsedSize":1894,"gzipSize":1012}],"parsedSize":7159,"gzipSize":2831},{"label":"map","path":"./node_modules/echarts/lib/chart/map","statSize":28266,"groups":[{"id":"2W4A","label":"mapVisual.js","path":"./node_modules/echarts/lib/chart/map/mapVisual.js","statSize":2065,"parsedSize":233,"gzipSize":176},{"id":"OvrE","label":"MapSeries.js","path":"./node_modules/echarts/lib/chart/map/MapSeries.js","statSize":8429,"parsedSize":2135,"gzipSize":1097},{"id":"PdL8","label":"MapView.js","path":"./node_modules/echarts/lib/chart/map/MapView.js","statSize":8559,"parsedSize":2240,"gzipSize":1092},{"id":"QZ7o","label":"mapSymbolLayout.js","path":"./node_modules/echarts/lib/chart/map/mapSymbolLayout.js","statSize":3257,"parsedSize":614,"gzipSize":381},{"id":"Z2m1","label":"backwardCompat.js","path":"./node_modules/echarts/lib/chart/map/backwardCompat.js","statSize":2044,"parsedSize":176,"gzipSize":152},{"id":"vIe4","label":"mapDataStatistic.js","path":"./node_modules/echarts/lib/chart/map/mapDataStatistic.js","statSize":3912,"parsedSize":886,"gzipSize":488}],"parsedSize":6284,"gzipSize":2612},{"label":"bar","path":"./node_modules/echarts/lib/chart/bar","statSize":60483,"groups":[{"id":"2m1D","label":"BaseBarSeries.js","path":"./node_modules/echarts/lib/chart/bar/BaseBarSeries.js","statSize":3375,"parsedSize":621,"gzipSize":423},{"id":"DPh+","label":"BarSeries.js","path":"./node_modules/echarts/lib/chart/bar/BarSeries.js","statSize":2846,"parsedSize":567,"gzipSize":342},{"id":"OQAC","label":"PictorialBarSeries.js","path":"./node_modules/echarts/lib/chart/bar/PictorialBarSeries.js","statSize":3096,"parsedSize":474,"gzipSize":307},{"id":"Pobh","label":"barItemStyle.js","path":"./node_modules/echarts/lib/chart/bar/barItemStyle.js","statSize":2234,"parsedSize":389,"gzipSize":231},{"id":"dzlV","label":"helper.js","path":"./node_modules/echarts/lib/chart/bar/helper.js","statSize":2449,"parsedSize":340,"gzipSize":240},{"id":"eHPu","label":"BarView.js","path":"./node_modules/echarts/lib/chart/bar/BarView.js","statSize":22401,"parsedSize":7751,"gzipSize":2975},{"id":"m5oG","label":"PictorialBarView.js","path":"./node_modules/echarts/lib/chart/bar/PictorialBarView.js","statSize":24082,"parsedSize":8202,"gzipSize":3277}],"parsedSize":18344,"gzipSize":6603},{"label":"lines","path":"./node_modules/echarts/lib/chart/lines","statSize":22754,"groups":[{"id":"4A6G","label":"linesLayout.js","path":"./node_modules/echarts/lib/chart/lines/linesLayout.js","statSize":3875,"parsedSize":878,"gzipSize":490},{"id":"P0a5","label":"LinesSeries.js","path":"./node_modules/echarts/lib/chart/lines/LinesSeries.js","statSize":9668,"parsedSize":3619,"gzipSize":1393},{"id":"nV/6","label":"linesVisual.js","path":"./node_modules/echarts/lib/chart/lines/linesVisual.js","statSize":3047,"parsedSize":760,"gzipSize":352},{"id":"z+uQ","label":"LinesView.js","path":"./node_modules/echarts/lib/chart/lines/LinesView.js","statSize":6164,"parsedSize":1981,"gzipSize":864}],"parsedSize":7238,"gzipSize":2634},{"label":"sunburst","path":"./node_modules/echarts/lib/chart/sunburst","statSize":35247,"groups":[{"id":"4SGL","label":"sunburstLayout.js","path":"./node_modules/echarts/lib/chart/sunburst/sunburstLayout.js","statSize":6621,"parsedSize":1495,"gzipSize":773},{"id":"C6b9","label":"sunburstAction.js","path":"./node_modules/echarts/lib/chart/sunburst/sunburstAction.js","statSize":3226,"parsedSize":766,"gzipSize":311},{"id":"DnWC","label":"SunburstView.js","path":"./node_modules/echarts/lib/chart/sunburst/SunburstView.js","statSize":7382,"parsedSize":2360,"gzipSize":1056},{"id":"pSwa","label":"SunburstPiece.js","path":"./node_modules/echarts/lib/chart/sunburst/SunburstPiece.js","statSize":11660,"parsedSize":4191,"gzipSize":1799},{"id":"qBny","label":"SunburstSeries.js","path":"./node_modules/echarts/lib/chart/sunburst/SunburstSeries.js","statSize":6358,"parsedSize":1770,"gzipSize":934}],"parsedSize":10582,"gzipSize":4136},{"id":"4UDB","label":"line.js","path":"./node_modules/echarts/lib/chart/line.js","statSize":2148,"parsedSize":236,"gzipSize":187},{"id":"5/bM","label":"funnel.js","path":"./node_modules/echarts/lib/chart/funnel.js","statSize":1978,"parsedSize":175,"gzipSize":146},{"label":"boxplot","path":"./node_modules/echarts/lib/chart/boxplot","statSize":17395,"groups":[{"id":"5KWC","label":"boxplotVisual.js","path":"./node_modules/echarts/lib/chart/boxplot/boxplotVisual.js","statSize":2494,"parsedSize":351,"gzipSize":249},{"id":"MsVc","label":"BoxplotView.js","path":"./node_modules/echarts/lib/chart/boxplot/BoxplotView.js","statSize":5353,"parsedSize":1546,"gzipSize":751},{"id":"Po+l","label":"BoxplotSeries.js","path":"./node_modules/echarts/lib/chart/boxplot/BoxplotSeries.js","statSize":3323,"parsedSize":707,"gzipSize":422},{"id":"YbE0","label":"boxplotLayout.js","path":"./node_modules/echarts/lib/chart/boxplot/boxplotLayout.js","statSize":6225,"parsedSize":1590,"gzipSize":843}],"parsedSize":4194,"gzipSize":1854},{"label":"radar","path":"./node_modules/echarts/lib/chart/radar","statSize":17535,"groups":[{"id":"6HcI","label":"backwardCompat.js","path":"./node_modules/echarts/lib/chart/radar/backwardCompat.js","statSize":2560,"parsedSize":369,"gzipSize":228},{"id":"Gevp","label":"RadarSeries.js","path":"./node_modules/echarts/lib/chart/radar/RadarSeries.js","statSize":4084,"parsedSize":1141,"gzipSize":647},{"id":"guZJ","label":"RadarView.js","path":"./node_modules/echarts/lib/chart/radar/RadarView.js","statSize":7779,"parsedSize":2648,"gzipSize":1176},{"id":"lwXq","label":"radarLayout.js","path":"./node_modules/echarts/lib/chart/radar/radarLayout.js","statSize":3112,"parsedSize":501,"gzipSize":318}],"parsedSize":4659,"gzipSize":1977},{"label":"themeRiver","path":"./node_modules/echarts/lib/chart/themeRiver","statSize":22511,"groups":[{"id":"6HoR","label":"ThemeRiverView.js","path":"./node_modules/echarts/lib/chart/themeRiver/ThemeRiverView.js","statSize":5944,"parsedSize":1797,"gzipSize":943},{"id":"8DFW","label":"ThemeRiverSeries.js","path":"./node_modules/echarts/lib/chart/themeRiver/ThemeRiverSeries.js","statSize":8901,"parsedSize":2354,"gzipSize":1231},{"id":"h6Uy","label":"themeRiverVisual.js","path":"./node_modules/echarts/lib/chart/themeRiver/themeRiverVisual.js","statSize":2402,"parsedSize":377,"gzipSize":250},{"id":"nQkE","label":"themeRiverLayout.js","path":"./node_modules/echarts/lib/chart/themeRiver/themeRiverLayout.js","statSize":5264,"parsedSize":1265,"gzipSize":657}],"parsedSize":5793,"gzipSize":2597},{"id":"7bL3","label":"themeRiver.js","path":"./node_modules/echarts/lib/chart/themeRiver.js","statSize":2065,"parsedSize":179,"gzipSize":157},{"id":"87tG","label":"gauge.js","path":"./node_modules/echarts/lib/chart/gauge.js","statSize":855,"parsedSize":36,"gzipSize":54},{"id":"8UWf","label":"tree.js","path":"./node_modules/echarts/lib/chart/tree.js","statSize":1901,"parsedSize":147,"gzipSize":141},{"id":"9cSo","label":"candlestick.js","path":"./node_modules/echarts/lib/chart/candlestick.js","statSize":2036,"parsedSize":158,"gzipSize":143},{"id":"A6Kt","label":"sunburst.js","path":"./node_modules/echarts/lib/chart/sunburst.js","statSize":2139,"parsedSize":237,"gzipSize":171},{"id":"AjST","label":"boxplot.js","path":"./node_modules/echarts/lib/chart/boxplot.js","statSize":1885,"parsedSize":120,"gzipSize":119},{"label":"tree","path":"./node_modules/echarts/lib/chart/tree","statSize":49811,"groups":[{"id":"CUwD","label":"TreeView.js","path":"./node_modules/echarts/lib/chart/tree/TreeView.js","statSize":20574,"parsedSize":7617,"gzipSize":2995},{"id":"ERCD","label":"layoutHelper.js","path":"./node_modules/echarts/lib/chart/tree/layoutHelper.js","statSize":12492,"parsedSize":2406,"gzipSize":868},{"id":"LGKB","label":"TreeSeries.js","path":"./node_modules/echarts/lib/chart/tree/TreeSeries.js","statSize":5844,"parsedSize":1682,"gzipSize":912},{"id":"PYc7","label":"treeAction.js","path":"./node_modules/echarts/lib/chart/tree/treeAction.js","statSize":2935,"parsedSize":582,"gzipSize":292},{"id":"XFat","label":"traversalHelper.js","path":"./node_modules/echarts/lib/chart/tree/traversalHelper.js","statSize":2846,"parsedSize":340,"gzipSize":209},{"id":"iDGQ","label":"treeLayout.js","path":"./node_modules/echarts/lib/chart/tree/treeLayout.js","statSize":5120,"parsedSize":1321,"gzipSize":640}],"parsedSize":13948,"gzipSize":5147},{"id":"GbHy","label":"bar.js","path":"./node_modules/echarts/lib/chart/bar.js","statSize":2495,"parsedSize":346,"gzipSize":249},{"label":"gauge","path":"./node_modules/echarts/lib/chart/gauge","statSize":19552,"groups":[{"id":"JNWs","label":"GaugeSeries.js","path":"./node_modules/echarts/lib/chart/gauge/GaugeSeries.js","statSize":4034,"parsedSize":889,"gzipSize":512},{"id":"hG1p","label":"PointerPath.js","path":"./node_modules/echarts/lib/chart/gauge/PointerPath.js","statSize":2472,"parsedSize":404,"gzipSize":254},{"id":"sTIA","label":"GaugeView.js","path":"./node_modules/echarts/lib/chart/gauge/GaugeView.js","statSize":13046,"parsedSize":4461,"gzipSize":1843}],"parsedSize":5754,"gzipSize":2362},{"label":"heatmap","path":"./node_modules/echarts/lib/chart/heatmap","statSize":18442,"groups":[{"id":"LgWN","label":"HeatmapLayer.js","path":"./node_modules/echarts/lib/chart/heatmap/HeatmapLayer.js","statSize":5962,"parsedSize":1405,"gzipSize":746},{"id":"OcRu","label":"HeatmapView.js","path":"./node_modules/echarts/lib/chart/heatmap/HeatmapView.js","statSize":9889,"parsedSize":3643,"gzipSize":1557},{"id":"cN90","label":"HeatmapSeries.js","path":"./node_modules/echarts/lib/chart/heatmap/HeatmapSeries.js","statSize":2591,"parsedSize":459,"gzipSize":315}],"parsedSize":5507,"gzipSize":2341},{"id":"MOpb","label":"custom.js","path":"./node_modules/echarts/lib/chart/custom.js","statSize":24583,"parsedSize":6632,"gzipSize":2876},{"label":"line","path":"./node_modules/echarts/lib/chart/line","statSize":48697,"groups":[{"id":"MXTC","label":"poly.js","path":"./node_modules/echarts/lib/chart/line/poly.js","statSize":10871,"parsedSize":2416,"gzipSize":1131},{"id":"SlE6","label":"lineAnimationDiff.js","path":"./node_modules/echarts/lib/chart/line/lineAnimationDiff.js","statSize":6880,"parsedSize":1291,"gzipSize":631},{"id":"cO/Q","label":"LineView.js","path":"./node_modules/echarts/lib/chart/line/LineView.js","statSize":23228,"parsedSize":6950,"gzipSize":2943},{"id":"jMTz","label":"LineSeries.js","path":"./node_modules/echarts/lib/chart/line/LineSeries.js","statSize":3408,"parsedSize":600,"gzipSize":417},{"id":"tzpD","label":"helper.js","path":"./node_modules/echarts/lib/chart/line/helper.js","statSize":4310,"parsedSize":930,"gzipSize":486}],"parsedSize":12187,"gzipSize":4934},{"id":"N1UU","label":"pictorialBar.js","path":"./node_modules/echarts/lib/chart/pictorialBar.js","statSize":2126,"parsedSize":211,"gzipSize":176},{"id":"O523","label":"treemap.js","path":"./node_modules/echarts/lib/chart/treemap.js","statSize":1922,"parsedSize":130,"gzipSize":125},{"label":"effectScatter","path":"./node_modules/echarts/lib/chart/effectScatter","statSize":5922,"groups":[{"id":"P7Q7","label":"EffectScatterSeries.js","path":"./node_modules/echarts/lib/chart/effectScatter/EffectScatterSeries.js","statSize":2972,"parsedSize":432,"gzipSize":321},{"id":"Y3kp","label":"EffectScatterView.js","path":"./node_modules/echarts/lib/chart/effectScatter/EffectScatterView.js","statSize":2950,"parsedSize":708,"gzipSize":369}],"parsedSize":1140,"gzipSize":589},{"label":"funnel","path":"./node_modules/echarts/lib/chart/funnel","statSize":22648,"groups":[{"id":"UOrf","label":"funnelLayout.js","path":"./node_modules/echarts/lib/chart/funnel/funnelLayout.js","statSize":11511,"parsedSize":3452,"gzipSize":1334},{"id":"eQYg","label":"FunnelSeries.js","path":"./node_modules/echarts/lib/chart/funnel/FunnelSeries.js","statSize":4613,"parsedSize":1180,"gzipSize":672},{"id":"h4VJ","label":"FunnelView.js","path":"./node_modules/echarts/lib/chart/funnel/FunnelView.js","statSize":6524,"parsedSize":2370,"gzipSize":974}],"parsedSize":7002,"gzipSize":2692},{"id":"Vb+l","label":"pie.js","path":"./node_modules/echarts/lib/chart/pie.js","statSize":2357,"parsedSize":409,"gzipSize":238},{"id":"cWq4","label":"sankey.js","path":"./node_modules/echarts/lib/chart/sankey.js","statSize":1908,"parsedSize":130,"gzipSize":125},{"id":"k9Bd","label":"effectScatter.js","path":"./node_modules/echarts/lib/chart/effectScatter.js","statSize":1935,"parsedSize":163,"gzipSize":143},{"id":"nUSl","label":"map.js","path":"./node_modules/echarts/lib/chart/map.js","statSize":2506,"parsedSize":457,"gzipSize":269},{"id":"o0k+","label":"heatmap.js","path":"./node_modules/echarts/lib/chart/heatmap.js","statSize":863,"parsedSize":36,"gzipSize":54},{"label":"scatter","path":"./node_modules/echarts/lib/chart/scatter","statSize":8068,"groups":[{"id":"pmYM","label":"ScatterSeries.js","path":"./node_modules/echarts/lib/chart/scatter/ScatterSeries.js","statSize":3721,"parsedSize":693,"gzipSize":398},{"id":"uNEE","label":"ScatterView.js","path":"./node_modules/echarts/lib/chart/scatter/ScatterView.js","statSize":4347,"parsedSize":1249,"gzipSize":534}],"parsedSize":1942,"gzipSize":835},{"id":"qbKW","label":"parallel.js","path":"./node_modules/echarts/lib/chart/parallel.js","statSize":1832,"parsedSize":98,"gzipSize":105},{"id":"rjsW","label":"radar.js","path":"./node_modules/echarts/lib/chart/radar.js","statSize":2237,"parsedSize":271,"gzipSize":184},{"id":"u9yV","label":"scatter.js","path":"./node_modules/echarts/lib/chart/scatter.js","statSize":2880,"parsedSize":161,"gzipSize":142},{"id":"zjhG","label":"lines.js","path":"./node_modules/echarts/lib/chart/lines.js","statSize":1861,"parsedSize":120,"gzipSize":119}],"parsedSize":223808,"gzipSize":71460},{"label":"scale","path":"./node_modules/echarts/lib/scale","statSize":36086,"groups":[{"id":"/+sa","label":"Scale.js","path":"./node_modules/echarts/lib/scale/Scale.js","statSize":4811,"parsedSize":1071,"gzipSize":447},{"id":"dDRy","label":"Time.js","path":"./node_modules/echarts/lib/scale/Time.js","statSize":8084,"parsedSize":2049,"gzipSize":955},{"id":"tBuv","label":"Interval.js","path":"./node_modules/echarts/lib/scale/Interval.js","statSize":8929,"parsedSize":1945,"gzipSize":878},{"id":"u5Nq","label":"Ordinal.js","path":"./node_modules/echarts/lib/scale/Ordinal.js","statSize":4112,"parsedSize":994,"gzipSize":480},{"id":"wW3A","label":"helper.js","path":"./node_modules/echarts/lib/scale/helper.js","statSize":3774,"parsedSize":569,"gzipSize":329},{"id":"xCbH","label":"Log.js","path":"./node_modules/echarts/lib/scale/Log.js","statSize":6376,"parsedSize":1737,"gzipSize":787}],"parsedSize":8365,"gzipSize":2827},{"label":"coord","path":"./node_modules/echarts/lib/coord","statSize":282469,"groups":[{"label":"parallel","path":"./node_modules/echarts/lib/coord/parallel","statSize":34097,"groups":[{"id":"/BOW","label":"ParallelAxis.js","path":"./node_modules/echarts/lib/coord/parallel/ParallelAxis.js","statSize":2619,"parsedSize":292,"gzipSize":221},{"id":"DHpS","label":"AxisModel.js","path":"./node_modules/echarts/lib/coord/parallel/AxisModel.js","statSize":4738,"parsedSize":1009,"gzipSize":554},{"id":"KjPy","label":"ParallelModel.js","path":"./node_modules/echarts/lib/coord/parallel/ParallelModel.js","statSize":4526,"parsedSize":1234,"gzipSize":617},{"id":"i6Ks","label":"parallelPreprocessor.js","path":"./node_modules/echarts/lib/coord/parallel/parallelPreprocessor.js","statSize":2806,"parsedSize":414,"gzipSize":245},{"id":"qSkD","label":"parallelCreator.js","path":"./node_modules/echarts/lib/coord/parallel/parallelCreator.js","statSize":2630,"parsedSize":454,"gzipSize":264},{"id":"sYrQ","label":"Parallel.js","path":"./node_modules/echarts/lib/coord/parallel/Parallel.js","statSize":16778,"parsedSize":4867,"gzipSize":2074}],"parsedSize":8270,"gzipSize":3239},{"label":"calendar","path":"./node_modules/echarts/lib/coord/calendar","statSize":19490,"groups":[{"id":"0BOU","label":"Calendar.js","path":"./node_modules/echarts/lib/coord/calendar/Calendar.js","statSize":12741,"parsedSize":4649,"gzipSize":1777},{"id":"RAps","label":"prepareCustom.js","path":"./node_modules/echarts/lib/coord/calendar/prepareCustom.js","statSize":2221,"parsedSize":328,"gzipSize":223},{"id":"yEXw","label":"CalendarModel.js","path":"./node_modules/echarts/lib/coord/calendar/CalendarModel.js","statSize":4528,"parsedSize":1081,"gzipSize":604}],"parsedSize":6058,"gzipSize":2331},{"label":"geo","path":"./node_modules/echarts/lib/coord/geo","statSize":57190,"groups":[{"id":"0MNY","label":"mapDataStorage.js","path":"./node_modules/echarts/lib/coord/geo/mapDataStorage.js","statSize":3839,"parsedSize":684,"gzipSize":403},{"id":"AlF5","label":"geoSourceManager.js","path":"./node_modules/echarts/lib/coord/geo/geoSourceManager.js","statSize":4191,"parsedSize":739,"gzipSize":457},{"id":"Axyt","label":"parseGeoJson.js","path":"./node_modules/echarts/lib/coord/geo/parseGeoJson.js","statSize":4624,"parsedSize":1040,"gzipSize":545},{"id":"B33o","label":"Region.js","path":"./node_modules/echarts/lib/coord/geo/Region.js","statSize":5631,"parsedSize":1518,"gzipSize":745},{"id":"HcG6","label":"Geo.js","path":"./node_modules/echarts/lib/coord/geo/Geo.js","statSize":5683,"parsedSize":1489,"gzipSize":687},{"id":"OpfW","label":"prepareCustom.js","path":"./node_modules/echarts/lib/coord/geo/prepareCustom.js","statSize":2657,"parsedSize":433,"gzipSize":282},{"label":"fix","path":"./node_modules/echarts/lib/coord/geo/fix","statSize":9232,"groups":[{"id":"Qv0P","label":"nanhai.js","path":"./node_modules/echarts/lib/coord/geo/fix/nanhai.js","statSize":3070,"parsedSize":972,"gzipSize":502},{"id":"WxG6","label":"geoCoord.js","path":"./node_modules/echarts/lib/coord/geo/fix/geoCoord.js","statSize":1956,"parsedSize":199,"gzipSize":164},{"id":"cU6c","label":"textCoord.js","path":"./node_modules/echarts/lib/coord/geo/fix/textCoord.js","statSize":2046,"parsedSize":198,"gzipSize":208},{"id":"k7nC","label":"diaoyuIsland.js","path":"./node_modules/echarts/lib/coord/geo/fix/diaoyuIsland.js","statSize":2160,"parsedSize":324,"gzipSize":210}],"parsedSize":1693,"gzipSize":790},{"id":"mvkK","label":"geoSVGLoader.js","path":"./node_modules/echarts/lib/coord/geo/geoSVGLoader.js","statSize":5038,"parsedSize":1170,"gzipSize":625},{"id":"s/PG","label":"geoJSONLoader.js","path":"./node_modules/echarts/lib/coord/geo/geoJSONLoader.js","statSize":3563,"parsedSize":587,"gzipSize":405},{"id":"srbS","label":"geoCreator.js","path":"./node_modules/echarts/lib/coord/geo/geoCreator.js","statSize":7647,"parsedSize":2194,"gzipSize":1061},{"id":"whrq","label":"GeoModel.js","path":"./node_modules/echarts/lib/coord/geo/GeoModel.js","statSize":5085,"parsedSize":1298,"gzipSize":725}],"parsedSize":12845,"gzipSize":5139},{"id":"2HcM","label":"Axis.js","path":"./node_modules/echarts/lib/coord/Axis.js","statSize":11054,"parsedSize":2572,"gzipSize":1062},{"id":"2uoh","label":"axisModelCommonMixin.js","path":"./node_modules/echarts/lib/coord/axisModelCommonMixin.js","statSize":3470,"parsedSize":691,"gzipSize":294},{"id":"3yJd","label":"axisHelper.js","path":"./node_modules/echarts/lib/coord/axisHelper.js","statSize":14610,"parsedSize":3431,"gzipSize":1619},{"label":"cartesian","path":"./node_modules/echarts/lib/coord/cartesian","statSize":42190,"groups":[{"id":"4xrk","label":"cartesianAxisHelper.js","path":"./node_modules/echarts/lib/coord/cartesian/cartesianAxisHelper.js","statSize":3904,"parsedSize":855,"gzipSize":498},{"id":"5dr1","label":"Cartesian2D.js","path":"./node_modules/echarts/lib/coord/cartesian/Cartesian2D.js","statSize":5036,"parsedSize":1470,"gzipSize":575},{"id":"5vFd","label":"Grid.js","path":"./node_modules/echarts/lib/coord/cartesian/Grid.js","statSize":17362,"parsedSize":5492,"gzipSize":2197},{"id":"BuI2","label":"GridModel.js","path":"./node_modules/echarts/lib/coord/cartesian/GridModel.js","statSize":2358,"parsedSize":304,"gzipSize":249},{"id":"RKzr","label":"Axis2D.js","path":"./node_modules/echarts/lib/coord/cartesian/Axis2D.js","statSize":4094,"parsedSize":641,"gzipSize":365},{"id":"ct4P","label":"Cartesian.js","path":"./node_modules/echarts/lib/coord/cartesian/Cartesian.js","statSize":3724,"parsedSize":781,"gzipSize":385},{"id":"ecfp","label":"AxisModel.js","path":"./node_modules/echarts/lib/coord/cartesian/AxisModel.js","statSize":3131,"parsedSize":638,"gzipSize":353},{"id":"n/n4","label":"prepareCustom.js","path":"./node_modules/echarts/lib/coord/cartesian/prepareCustom.js","statSize":2581,"parsedSize":424,"gzipSize":293}],"parsedSize":10605,"gzipSize":3826},{"label":"polar","path":"./node_modules/echarts/lib/coord/polar","statSize":29229,"groups":[{"id":"6UfY","label":"AngleAxis.js","path":"./node_modules/echarts/lib/coord/polar/AngleAxis.js","statSize":4531,"parsedSize":873,"gzipSize":522},{"id":"6axr","label":"Polar.js","path":"./node_modules/echarts/lib/coord/polar/Polar.js","statSize":8039,"parsedSize":2231,"gzipSize":874},{"id":"NjeB","label":"AxisModel.js","path":"./node_modules/echarts/lib/coord/polar/AxisModel.js","statSize":2888,"parsedSize":452,"gzipSize":321},{"id":"PBlc","label":"polarCreator.js","path":"./node_modules/echarts/lib/coord/polar/polarCreator.js","statSize":6213,"parsedSize":1821,"gzipSize":882},{"id":"YqdL","label":"RadiusAxis.js","path":"./node_modules/echarts/lib/coord/polar/RadiusAxis.js","statSize":2282,"parsedSize":332,"gzipSize":231},{"id":"ZRmN","label":"PolarModel.js","path":"./node_modules/echarts/lib/coord/polar/PolarModel.js","statSize":2357,"parsedSize":348,"gzipSize":260},{"id":"ilox","label":"prepareCustom.js","path":"./node_modules/echarts/lib/coord/polar/prepareCustom.js","statSize":2919,"parsedSize":592,"gzipSize":387}],"parsedSize":6649,"gzipSize":2537},{"label":"single","path":"./node_modules/echarts/lib/coord/single","statSize":22637,"groups":[{"id":"8YpK","label":"prepareCustom.js","path":"./node_modules/echarts/lib/coord/single/prepareCustom.js","statSize":2456,"parsedSize":406,"gzipSize":277},{"id":"JzYe","label":"Single.js","path":"./node_modules/echarts/lib/coord/single/Single.js","statSize":7669,"parsedSize":2234,"gzipSize":946},{"id":"WK/r","label":"SingleAxis.js","path":"./node_modules/echarts/lib/coord/single/SingleAxis.js","statSize":3381,"parsedSize":413,"gzipSize":266},{"id":"fzS+","label":"singleAxisHelper.js","path":"./node_modules/echarts/lib/coord/single/singleAxisHelper.js","statSize":3234,"parsedSize":721,"gzipSize":404},{"id":"ghha","label":"singleCreator.js","path":"./node_modules/echarts/lib/coord/single/singleCreator.js","statSize":2794,"parsedSize":487,"gzipSize":283},{"id":"rwkR","label":"AxisModel.js","path":"./node_modules/echarts/lib/coord/single/AxisModel.js","statSize":3103,"parsedSize":611,"gzipSize":378}],"parsedSize":4872,"gzipSize":1900},{"id":"H4Od","label":"View.js","path":"./node_modules/echarts/lib/coord/View.js","statSize":8770,"parsedSize":2744,"gzipSize":964},{"label":"radar","path":"./node_modules/echarts/lib/coord/radar","statSize":16293,"groups":[{"id":"JFJR","label":"IndicatorAxis.js","path":"./node_modules/echarts/lib/coord/radar/IndicatorAxis.js","statSize":2148,"parsedSize":172,"gzipSize":147},{"id":"jJrn","label":"Radar.js","path":"./node_modules/echarts/lib/coord/radar/Radar.js","statSize":8958,"parsedSize":2940,"gzipSize":1230},{"id":"xLO3","label":"RadarModel.js","path":"./node_modules/echarts/lib/coord/radar/RadarModel.js","statSize":5187,"parsedSize":1533,"gzipSize":767}],"parsedSize":4645,"gzipSize":1921},{"id":"SiPa","label":"axisTickLabelBuilder.js","path":"./node_modules/echarts/lib/coord/axisTickLabelBuilder.js","statSize":13175,"parsedSize":2935,"gzipSize":1303},{"id":"eH0a","label":"axisModelCreator.js","path":"./node_modules/echarts/lib/coord/axisModelCreator.js","statSize":4088,"parsedSize":780,"gzipSize":441},{"id":"s/lY","label":"axisDefault.js","path":"./node_modules/echarts/lib/coord/axisDefault.js","statSize":6176,"parsedSize":1187,"gzipSize":586}],"parsedSize":68284,"gzipSize":21508},{"label":"theme","path":"./node_modules/echarts/lib/theme","statSize":5866,"groups":[{"id":"/xsj","label":"light.js","path":"./node_modules/echarts/lib/theme/light.js","statSize":2038,"parsedSize":377,"gzipSize":208},{"id":"4oYY","label":"dark.js","path":"./node_modules/echarts/lib/theme/dark.js","statSize":3828,"parsedSize":1726,"gzipSize":480}],"parsedSize":2103,"gzipSize":607},{"label":"util","path":"./node_modules/echarts/lib/util","statSize":148968,"groups":[{"id":"0sHC","label":"graphic.js","path":"./node_modules/echarts/lib/util/graphic.js","statSize":52481,"parsedSize":10742,"gzipSize":4196},{"id":"1Xuh","label":"layout.js","path":"./node_modules/echarts/lib/util/layout.js","statSize":17150,"parsedSize":3180,"gzipSize":1550},{"id":"BNYN","label":"clazz.js","path":"./node_modules/echarts/lib/util/clazz.js","statSize":7927,"parsedSize":1847,"gzipSize":883},{"id":"HHfb","label":"format.js","path":"./node_modules/echarts/lib/util/format.js","statSize":8754,"parsedSize":2729,"gzipSize":1291},{"label":"shape","path":"./node_modules/echarts/lib/util/shape","statSize":3163,"groups":[{"id":"LJck","label":"sausage.js","path":"./node_modules/echarts/lib/util/shape/sausage.js","statSize":3163,"parsedSize":594,"gzipSize":353}],"parsedSize":594,"gzipSize":353},{"id":"QD+P","label":"throttle.js","path":"./node_modules/echarts/lib/util/throttle.js","statSize":5389,"parsedSize":714,"gzipSize":407},{"id":"e+YR","label":"animation.js","path":"./node_modules/echarts/lib/util/animation.js","statSize":3960,"parsedSize":424,"gzipSize":281},{"id":"h0jU","label":"component.js","path":"./node_modules/echarts/lib/util/component.js","statSize":6847,"parsedSize":1279,"gzipSize":641},{"id":"kK7q","label":"symbol.js","path":"./node_modules/echarts/lib/util/symbol.js","statSize":9072,"parsedSize":2940,"gzipSize":1082},{"id":"vXqC","label":"model.js","path":"./node_modules/echarts/lib/util/model.js","statSize":17944,"parsedSize":4116,"gzipSize":1778},{"id":"wWR3","label":"number.js","path":"./node_modules/echarts/lib/util/number.js","statSize":16281,"parsedSize":3272,"gzipSize":1488}],"parsedSize":31837,"gzipSize":12152},{"label":"layout","path":"./node_modules/echarts/lib/layout","statSize":33502,"groups":[{"id":"1Nix","label":"points.js","path":"./node_modules/echarts/lib/layout/points.js","statSize":3892,"parsedSize":804,"gzipSize":485},{"id":"m/6y","label":"barGrid.js","path":"./node_modules/echarts/lib/layout/barGrid.js","statSize":19522,"parsedSize":4793,"gzipSize":2145},{"id":"mLyJ","label":"barPolar.js","path":"./node_modules/echarts/lib/layout/barPolar.js","statSize":10088,"parsedSize":2501,"gzipSize":1199}],"parsedSize":8098,"gzipSize":3096},{"id":"4Nz2","label":"config.js","path":"./node_modules/echarts/lib/config.js","statSize":2226,"parsedSize":164,"gzipSize":147},{"id":"5QRV","label":"helper.js","path":"./node_modules/echarts/lib/helper.js","statSize":4573,"parsedSize":697,"gzipSize":389},{"label":"preprocessor","path":"./node_modules/echarts/lib/preprocessor","statSize":13903,"groups":[{"id":"8V5i","label":"backwardCompat.js","path":"./node_modules/echarts/lib/preprocessor/backwardCompat.js","statSize":4088,"parsedSize":991,"gzipSize":550},{"label":"helper","path":"./node_modules/echarts/lib/preprocessor/helper","statSize":9815,"groups":[{"id":"xb/I","label":"compatStyle.js","path":"./node_modules/echarts/lib/preprocessor/helper/compatStyle.js","statSize":9815,"parsedSize":2902,"gzipSize":1115}],"parsedSize":2902,"gzipSize":1115}],"parsedSize":3893,"gzipSize":1522},{"label":"visual","path":"./node_modules/echarts/lib/visual","statSize":48940,"groups":[{"id":"AjK0","label":"symbol.js","path":"./node_modules/echarts/lib/visual/symbol.js","statSize":5057,"parsedSize":996,"gzipSize":444},{"id":"B123","label":"visualDefault.js","path":"./node_modules/echarts/lib/visual/visualDefault.js","statSize":2569,"parsedSize":535,"gzipSize":290},{"id":"FCaW","label":"LegendVisualProvider.js","path":"./node_modules/echarts/lib/visual/LegendVisualProvider.js","statSize":3068,"parsedSize":294,"gzipSize":170},{"id":"HGSA","label":"VisualMapping.js","path":"./node_modules/echarts/lib/visual/VisualMapping.js","statSize":18206,"parsedSize":4999,"gzipSize":1823},{"id":"NUWb","label":"visualSolution.js","path":"./node_modules/echarts/lib/visual/visualSolution.js","statSize":7480,"parsedSize":1506,"gzipSize":717},{"id":"QDiV","label":"aria.js","path":"./node_modules/echarts/lib/visual/aria.js","statSize":5375,"parsedSize":1551,"gzipSize":817},{"id":"n5nI","label":"seriesColor.js","path":"./node_modules/echarts/lib/visual/seriesColor.js","statSize":3444,"parsedSize":741,"gzipSize":407},{"id":"ri8f","label":"dataColor.js","path":"./node_modules/echarts/lib/visual/dataColor.js","statSize":3741,"parsedSize":696,"gzipSize":368}],"parsedSize":11318,"gzipSize":4060},{"label":"model","path":"./node_modules/echarts/lib/model","statSize":100481,"groups":[{"label":"mixin","path":"./node_modules/echarts/lib/model/mixin","statSize":23395,"groups":[{"id":"BwZ6","label":"lineStyle.js","path":"./node_modules/echarts/lib/model/mixin/lineStyle.js","statSize":2899,"parsedSize":403,"gzipSize":271},{"id":"MyoG","label":"colorPalette.js","path":"./node_modules/echarts/lib/model/mixin/colorPalette.js","statSize":3513,"parsedSize":558,"gzipSize":344},{"id":"NZsM","label":"textStyle.js","path":"./node_modules/echarts/lib/model/mixin/textStyle.js","statSize":2775,"parsedSize":663,"gzipSize":316},{"id":"RYbJ","label":"makeStyleMapper.js","path":"./node_modules/echarts/lib/model/mixin/makeStyleMapper.js","statSize":2352,"parsedSize":285,"gzipSize":210},{"id":"bBvJ","label":"dataFormat.js","path":"./node_modules/echarts/lib/model/mixin/dataFormat.js","statSize":5735,"parsedSize":1422,"gzipSize":712},{"id":"e95b","label":"itemStyle.js","path":"./node_modules/echarts/lib/model/mixin/itemStyle.js","statSize":2315,"parsedSize":447,"gzipSize":281},{"id":"fgF4","label":"areaStyle.js","path":"./node_modules/echarts/lib/model/mixin/areaStyle.js","statSize":1927,"parsedSize":194,"gzipSize":157},{"id":"wEU0","label":"boxLayout.js","path":"./node_modules/echarts/lib/model/mixin/boxLayout.js","statSize":1879,"parsedSize":207,"gzipSize":140}],"parsedSize":4179,"gzipSize":1675},{"id":"EJsE","label":"Series.js","path":"./node_modules/echarts/lib/model/Series.js","statSize":18620,"parsedSize":4924,"gzipSize":2117},{"id":"Pdtn","label":"Model.js","path":"./node_modules/echarts/lib/model/Model.js","statSize":6252,"parsedSize":1419,"gzipSize":665},{"id":"Rtf0","label":"Global.js","path":"./node_modules/echarts/lib/model/Global.js","statSize":22109,"parsedSize":5545,"gzipSize":2057},{"id":"Y5nL","label":"Component.js","path":"./node_modules/echarts/lib/model/Component.js","statSize":6966,"parsedSize":1546,"gzipSize":762},{"id":"em4M","label":"OptionManager.js","path":"./node_modules/echarts/lib/model/OptionManager.js","statSize":13457,"parsedSize":2486,"gzipSize":1055},{"id":"u820","label":"globalDefault.js","path":"./node_modules/echarts/lib/model/globalDefault.js","statSize":3693,"parsedSize":632,"gzipSize":413},{"id":"zZZ/","label":"referHelper.js","path":"./node_modules/echarts/lib/model/referHelper.js","statSize":5989,"parsedSize":1558,"gzipSize":581}],"parsedSize":22289,"gzipSize":7743},{"id":"FIAY","label":"lang.js","path":"./node_modules/echarts/lib/lang.js","statSize":4104,"parsedSize":1219,"gzipSize":928},{"label":"action","path":"./node_modules/echarts/lib/action","statSize":8514,"groups":[{"id":"FvdC","label":"geoRoam.js","path":"./node_modules/echarts/lib/action/geoRoam.js","statSize":2967,"parsedSize":481,"gzipSize":291},{"id":"XRkS","label":"createDataSelectAction.js","path":"./node_modules/echarts/lib/action/createDataSelectAction.js","statSize":2662,"parsedSize":406,"gzipSize":269},{"id":"ii60","label":"roamHelper.js","path":"./node_modules/echarts/lib/action/roamHelper.js","statSize":2885,"parsedSize":501,"gzipSize":302}],"parsedSize":1388,"gzipSize":656},{"id":"Icdr","label":"echarts.js","path":"./node_modules/echarts/lib/echarts.js","statSize":65821,"parsedSize":17704,"gzipSize":6716},{"label":"loading","path":"./node_modules/echarts/lib/loading","statSize":4190,"groups":[{"id":"OkSz","label":"default.js","path":"./node_modules/echarts/lib/loading/default.js","statSize":4190,"parsedSize":1216,"gzipSize":618}],"parsedSize":1216,"gzipSize":618},{"label":"processor","path":"./node_modules/echarts/lib/processor","statSize":11554,"groups":[{"id":"PWa9","label":"dataSample.js","path":"./node_modules/echarts/lib/processor/dataSample.js","statSize":3870,"parsedSize":862,"gzipSize":462},{"id":"fYRm","label":"dataStack.js","path":"./node_modules/echarts/lib/processor/dataStack.js","statSize":5417,"parsedSize":1218,"gzipSize":543},{"id":"l4Op","label":"dataFilter.js","path":"./node_modules/echarts/lib/processor/dataFilter.js","statSize":2267,"parsedSize":268,"gzipSize":205}],"parsedSize":2348,"gzipSize":1001},{"label":"view","path":"./node_modules/echarts/lib/view","statSize":11416,"groups":[{"id":"Pgdp","label":"Component.js","path":"./node_modules/echarts/lib/view/Component.js","statSize":2865,"parsedSize":405,"gzipSize":275},{"id":"Ylhr","label":"Chart.js","path":"./node_modules/echarts/lib/view/Chart.js","statSize":8551,"parsedSize":1740,"gzipSize":854}],"parsedSize":2145,"gzipSize":940},{"label":"stream","path":"./node_modules/echarts/lib/stream","statSize":30773,"groups":[{"id":"gV7x","label":"task.js","path":"./node_modules/echarts/lib/stream/task.js","statSize":10835,"parsedSize":2518,"gzipSize":1022},{"id":"vub9","label":"Scheduler.js","path":"./node_modules/echarts/lib/stream/Scheduler.js","statSize":19938,"parsedSize":5272,"gzipSize":2128}],"parsedSize":7790,"gzipSize":2959},{"id":"iNHu","label":"export.js","path":"./node_modules/echarts/lib/export.js","statSize":3706,"parsedSize":1150,"gzipSize":627},{"id":"rctg","label":"CoordinateSystem.js","path":"./node_modules/echarts/lib/CoordinateSystem.js","statSize":2687,"parsedSize":472,"gzipSize":252},{"id":"uJBW","label":"ExtensionAPI.js","path":"./node_modules/echarts/lib/ExtensionAPI.js","statSize":2193,"parsedSize":328,"gzipSize":229}],"parsedSize":678781,"gzipSize":217511},{"id":"XLwt","label":"index.js","path":"./node_modules/echarts/index.js","statSize":3026,"parsedSize":768,"gzipSize":402}],"parsedSize":679549,"gzipSize":217826},{"label":"zrender","path":"./node_modules/zrender","statSize":520933,"groups":[{"label":"lib","path":"./node_modules/zrender/lib","statSize":520933,"groups":[{"label":"graphic","path":"./node_modules/zrender/lib/graphic","statSize":88013,"groups":[{"label":"shape","path":"./node_modules/zrender/lib/graphic/shape","statSize":10416,"groups":[{"id":"+UTs","label":"Polygon.js","path":"./node_modules/zrender/lib/graphic/shape/Polygon.js","statSize":375,"parsedSize":182,"gzipSize":160},{"id":"46eW","label":"Arc.js","path":"./node_modules/zrender/lib/graphic/shape/Arc.js","statSize":735,"parsedSize":337,"gzipSize":251},{"id":"67nf","label":"BezierCurve.js","path":"./node_modules/zrender/lib/graphic/shape/BezierCurve.js","statSize":2716,"parsedSize":974,"gzipSize":514},{"id":"6Kqb","label":"Ring.js","path":"./node_modules/zrender/lib/graphic/shape/Ring.js","statSize":485,"parsedSize":230,"gzipSize":180},{"id":"BeCT","label":"Polyline.js","path":"./node_modules/zrender/lib/graphic/shape/Polyline.js","statSize":424,"parsedSize":215,"gzipSize":179},{"id":"KsMi","label":"Line.js","path":"./node_modules/zrender/lib/graphic/shape/Line.js","statSize":1490,"parsedSize":485,"gzipSize":329},{"id":"Of86","label":"Circle.js","path":"./node_modules/zrender/lib/graphic/shape/Circle.js","statSize":730,"parsedSize":180,"gzipSize":164},{"id":"PD67","label":"Rect.js","path":"./node_modules/zrender/lib/graphic/shape/Rect.js","statSize":1514,"parsedSize":367,"gzipSize":254},{"id":"sRta","label":"Sector.js","path":"./node_modules/zrender/lib/graphic/shape/Sector.js","statSize":1086,"parsedSize":482,"gzipSize":304},{"id":"udrn","label":"Ellipse.js","path":"./node_modules/zrender/lib/graphic/shape/Ellipse.js","statSize":861,"parsedSize":363,"gzipSize":232}],"parsedSize":3815,"gzipSize":1283},{"label":"helper","path":"./node_modules/zrender/lib/graphic/helper","statSize":35203,"groups":[{"id":"+Y0c","label":"image.js","path":"./node_modules/zrender/lib/graphic/helper/image.js","statSize":2714,"parsedSize":728,"gzipSize":410},{"id":"2XvD","label":"smoothSpline.js","path":"./node_modules/zrender/lib/graphic/helper/smoothSpline.js","statSize":1673,"parsedSize":516,"gzipSize":350},{"id":"9b8q","label":"fixShadow.js","path":"./node_modules/zrender/lib/graphic/helper/fixShadow.js","statSize":414,"parsedSize":254,"gzipSize":150},{"id":"No7X","label":"poly.js","path":"./node_modules/zrender/lib/graphic/helper/poly.js","statSize":1050,"parsedSize":454,"gzipSize":299},{"id":"Sm9T","label":"roundRect.js","path":"./node_modules/zrender/lib/graphic/helper/roundRect.js","statSize":1929,"parsedSize":684,"gzipSize":360},{"id":"b8C2","label":"smoothBezier.js","path":"./node_modules/zrender/lib/graphic/helper/smoothBezier.js","statSize":2544,"parsedSize":611,"gzipSize":396},{"id":"dnLe","label":"fixClipWithShadow.js","path":"./node_modules/zrender/lib/graphic/helper/fixClipWithShadow.js","statSize":1780,"parsedSize":535,"gzipSize":342},{"id":"qjrH","label":"text.js","path":"./node_modules/zrender/lib/graphic/helper/text.js","statSize":19347,"parsedSize":6117,"gzipSize":2548},{"id":"xr8J","label":"subPixelOptimize.js","path":"./node_modules/zrender/lib/graphic/helper/subPixelOptimize.js","statSize":3752,"parsedSize":608,"gzipSize":337}],"parsedSize":10507,"gzipSize":4437},{"id":"/86O","label":"Text.js","path":"./node_modules/zrender/lib/graphic/Text.js","statSize":2345,"parsedSize":929,"gzipSize":516},{"id":"28kU","label":"constant.js","path":"./node_modules/zrender/lib/graphic/constant.js","statSize":217,"parsedSize":88,"gzipSize":108},{"id":"9qnA","label":"Displayable.js","path":"./node_modules/zrender/lib/graphic/Displayable.js","statSize":6790,"parsedSize":1221,"gzipSize":608},{"id":"Gw4f","label":"LinearGradient.js","path":"./node_modules/zrender/lib/graphic/LinearGradient.js","statSize":994,"parsedSize":263,"gzipSize":195},{"id":"GxVO","label":"Path.js","path":"./node_modules/zrender/lib/graphic/Path.js","statSize":10196,"parsedSize":3542,"gzipSize":1435},{"id":"MAom","label":"Image.js","path":"./node_modules/zrender/lib/graphic/Image.js","statSize":2440,"parsedSize":927,"gzipSize":501},{"id":"d8Tt","label":"Style.js","path":"./node_modules/zrender/lib/graphic/Style.js","statSize":10439,"parsedSize":2878,"gzipSize":1232},{"id":"dZ2L","label":"Pattern.js","path":"./node_modules/zrender/lib/graphic/Pattern.js","statSize":462,"parsedSize":201,"gzipSize":145},{"id":"jHiU","label":"RadialGradient.js","path":"./node_modules/zrender/lib/graphic/RadialGradient.js","statSize":935,"parsedSize":243,"gzipSize":184},{"id":"me52","label":"CompoundPath.js","path":"./node_modules/zrender/lib/graphic/CompoundPath.js","statSize":1362,"parsedSize":760,"gzipSize":385},{"label":"mixin","path":"./node_modules/zrender/lib/graphic/mixin","statSize":1654,"groups":[{"id":"taS8","label":"RectText.js","path":"./node_modules/zrender/lib/graphic/mixin/RectText.js","statSize":1654,"parsedSize":448,"gzipSize":320}],"parsedSize":448,"gzipSize":320},{"id":"thE4","label":"IncrementalDisplayable.js","path":"./node_modules/zrender/lib/graphic/IncrementalDisplayable.js","statSize":4208,"parsedSize":2139,"gzipSize":694},{"id":"wRzc","label":"Gradient.js","path":"./node_modules/zrender/lib/graphic/Gradient.js","statSize":352,"parsedSize":172,"gzipSize":141}],"parsedSize":28133,"gzipSize":9551},{"label":"mixin","path":"./node_modules/zrender/lib/mixin","statSize":24766,"groups":[{"id":"/ZBO","label":"Transformable.js","path":"./node_modules/zrender/lib/mixin/Transformable.js","statSize":6762,"parsedSize":2453,"gzipSize":984},{"id":"42YS","label":"Animatable.js","path":"./node_modules/zrender/lib/mixin/Animatable.js","statSize":7326,"parsedSize":1426,"gzipSize":770},{"id":"TIfe","label":"Draggable.js","path":"./node_modules/zrender/lib/mixin/Draggable.js","statSize":2558,"parsedSize":1084,"gzipSize":418},{"id":"qjvV","label":"Eventful.js","path":"./node_modules/zrender/lib/mixin/Eventful.js","statSize":8120,"parsedSize":1896,"gzipSize":767}],"parsedSize":6859,"gzipSize":2624},{"label":"core","path":"./node_modules/zrender/lib/core","statSize":114916,"groups":[{"id":"/gxq","label":"util.js","path":"./node_modules/zrender/lib/core/util.js","statSize":16379,"parsedSize":5048,"gzipSize":1835},{"id":"0fQF","label":"arrayDiff2.js","path":"./node_modules/zrender/lib/core/arrayDiff2.js","statSize":5456,"parsedSize":1574,"gzipSize":722},{"id":"6NQ8","label":"fourPointsTransform.js","path":"./node_modules/zrender/lib/core/fourPointsTransform.js","statSize":3246,"parsedSize":962,"gzipSize":508},{"id":"8b51","label":"BoundingRect.js","path":"./node_modules/zrender/lib/core/BoundingRect.js","statSize":4033,"parsedSize":1502,"gzipSize":653},{"id":"AAi1","label":"curve.js","path":"./node_modules/zrender/lib/core/curve.js","statSize":11636,"parsedSize":2958,"gzipSize":1317},{"id":"C3Vi","label":"dom.js","path":"./node_modules/zrender/lib/core/dom.js","statSize":5312,"parsedSize":1125,"gzipSize":691},{"id":"C7PF","label":"vector.js","path":"./node_modules/zrender/lib/core/vector.js","statSize":5081,"parsedSize":1630,"gzipSize":544},{"id":"HKuw","label":"timsort.js","path":"./node_modules/zrender/lib/core/timsort.js","statSize":13668,"parsedSize":3729,"gzipSize":1442},{"id":"JMnz","label":"GestureMgr.js","path":"./node_modules/zrender/lib/core/GestureMgr.js","statSize":2312,"parsedSize":959,"gzipSize":524},{"id":"RiVu","label":"guid.js","path":"./node_modules/zrender/lib/core/guid.js","statSize":173,"parsedSize":58,"gzipSize":72},{"id":"UAiw","label":"event.js","path":"./node_modules/zrender/lib/core/event.js","statSize":9699,"parsedSize":1557,"gzipSize":810},{"id":"YNzw","label":"env.js","path":"./node_modules/zrender/lib/core/env.js","statSize":6884,"parsedSize":1068,"gzipSize":474},{"id":"dOVI","label":"matrix.js","path":"./node_modules/zrender/lib/core/matrix.js","statSize":3567,"parsedSize":1161,"gzipSize":503},{"id":"eZxa","label":"log.js","path":"./node_modules/zrender/lib/core/log.js","statSize":210,"parsedSize":98,"gzipSize":109},{"id":"moDv","label":"PathProxy.js","path":"./node_modules/zrender/lib/core/PathProxy.js","statSize":18481,"parsedSize":6192,"gzipSize":2312},{"id":"wUOi","label":"bbox.js","path":"./node_modules/zrender/lib/core/bbox.js","statSize":5411,"parsedSize":1427,"gzipSize":698},{"id":"zMj2","label":"LRU.js","path":"./node_modules/zrender/lib/core/LRU.js","statSize":3368,"parsedSize":1126,"gzipSize":463}],"parsedSize":32174,"gzipSize":11842},{"label":"vml","path":"./node_modules/zrender/lib/vml","statSize":35514,"groups":[{"id":"0jKn","label":"Painter.js","path":"./node_modules/zrender/lib/vml/Painter.js","statSize":4541,"parsedSize":2364,"gzipSize":1029},{"id":"6MCj","label":"graphic.js","path":"./node_modules/zrender/lib/vml/graphic.js","statSize":29582,"parsedSize":9809,"gzipSize":4311},{"id":"VmZa","label":"vml.js","path":"./node_modules/zrender/lib/vml/vml.js","statSize":180,"parsedSize":73,"gzipSize":87},{"id":"cI6i","label":"core.js","path":"./node_modules/zrender/lib/vml/core.js","statSize":1211,"parsedSize":599,"gzipSize":356}],"parsedSize":12845,"gzipSize":5426},{"label":"contain","path":"./node_modules/zrender/lib/contain","statSize":36854,"groups":[{"id":"2I/p","label":"arc.js","path":"./node_modules/zrender/lib/contain/arc.js","statSize":1288,"parsedSize":351,"gzipSize":262},{"id":"2M5Q","label":"path.js","path":"./node_modules/zrender/lib/contain/path.js","statSize":9473,"parsedSize":2935,"gzipSize":1228},{"id":"3h1/","label":"text.js","path":"./node_modules/zrender/lib/contain/text.js","statSize":22089,"parsedSize":5682,"gzipSize":2485},{"id":"ABnm","label":"util.js","path":"./node_modules/zrender/lib/contain/util.js","statSize":178,"parsedSize":86,"gzipSize":96},{"id":"LICT","label":"cubic.js","path":"./node_modules/zrender/lib/contain/cubic.js","statSize":952,"parsedSize":279,"gzipSize":195},{"id":"N1qP","label":"polygon.js","path":"./node_modules/zrender/lib/contain/polygon.js","statSize":593,"parsedSize":292,"gzipSize":206},{"id":"QxFU","label":"windingLine.js","path":"./node_modules/zrender/lib/contain/windingLine.js","statSize":558,"parsedSize":195,"gzipSize":162},{"id":"oBGI","label":"quadratic.js","path":"./node_modules/zrender/lib/contain/quadratic.js","statSize":886,"parsedSize":247,"gzipSize":186},{"id":"u+XU","label":"line.js","path":"./node_modules/zrender/lib/contain/line.js","statSize":837,"parsedSize":257,"gzipSize":194}],"parsedSize":10324,"gzipSize":4259},{"label":"svg","path":"./node_modules/zrender/lib/svg","statSize":49220,"groups":[{"id":"4w1v","label":"graphic.js","path":"./node_modules/zrender/lib/svg/graphic.js","statSize":14662,"parsedSize":4836,"gzipSize":2302},{"label":"helper","path":"./node_modules/zrender/lib/svg/helper","statSize":22087,"groups":[{"id":"Pmfi","label":"Definable.js","path":"./node_modules/zrender/lib/svg/helper/Definable.js","statSize":6047,"parsedSize":1770,"gzipSize":722},{"id":"SMB/","label":"ShadowManager.js","path":"./node_modules/zrender/lib/svg/helper/ShadowManager.js","statSize":5403,"parsedSize":1890,"gzipSize":759},{"id":"TDz/","label":"ClippathManager.js","path":"./node_modules/zrender/lib/svg/helper/ClippathManager.js","statSize":4539,"parsedSize":1263,"gzipSize":594},{"id":"hZf2","label":"GradientManager.js","path":"./node_modules/zrender/lib/svg/helper/GradientManager.js","statSize":6098,"parsedSize":2055,"gzipSize":880}],"parsedSize":6978,"gzipSize":2257},{"id":"Q5xN","label":"Painter.js","path":"./node_modules/zrender/lib/svg/Painter.js","statSize":12126,"parsedSize":5224,"gzipSize":2022},{"id":"VewU","label":"core.js","path":"./node_modules/zrender/lib/svg/core.js","statSize":165,"parsedSize":115,"gzipSize":114},{"id":"jLnL","label":"svg.js","path":"./node_modules/zrender/lib/svg/svg.js","statSize":180,"parsedSize":73,"gzipSize":88}],"parsedSize":17226,"gzipSize":6055},{"id":"9N6q","label":"Storage.js","path":"./node_modules/zrender/lib/Storage.js","statSize":5682,"parsedSize":1884,"gzipSize":796},{"label":"container","path":"./node_modules/zrender/lib/container","statSize":6910,"groups":[{"id":"AlhT","label":"Group.js","path":"./node_modules/zrender/lib/container/Group.js","statSize":6910,"parsedSize":2375,"gzipSize":896}],"parsedSize":2375,"gzipSize":896},{"label":"tool","path":"./node_modules/zrender/lib/tool","statSize":48693,"groups":[{"id":"C1+n","label":"transformPath.js","path":"./node_modules/zrender/lib/tool/transformPath.js","statSize":1925,"parsedSize":695,"gzipSize":380},{"id":"DRaW","label":"color.js","path":"./node_modules/zrender/lib/tool/color.js","statSize":16500,"parsedSize":7057,"gzipSize":2916},{"id":"dE09","label":"path.js","path":"./node_modules/zrender/lib/tool/path.js","statSize":10875,"parsedSize":3346,"gzipSize":1256},{"id":"jDhh","label":"parseSVG.js","path":"./node_modules/zrender/lib/tool/parseSVG.js","statSize":19393,"parsedSize":7923,"gzipSize":2813}],"parsedSize":19021,"gzipSize":7010},{"label":"animation","path":"./node_modules/zrender/lib/animation","statSize":30120,"groups":[{"id":"CCtz","label":"Animator.js","path":"./node_modules/zrender/lib/animation/Animator.js","statSize":14873,"parsedSize":4330,"gzipSize":1822},{"id":"K0T9","label":"easing.js","path":"./node_modules/zrender/lib/animation/easing.js","statSize":6613,"parsedSize":2355,"gzipSize":628},{"id":"V4nf","label":"Animation.js","path":"./node_modules/zrender/lib/animation/Animation.js","statSize":5401,"parsedSize":1653,"gzipSize":699},{"id":"a1Sp","label":"requestAnimationFrame.js","path":"./node_modules/zrender/lib/animation/requestAnimationFrame.js","statSize":424,"parsedSize":311,"gzipSize":164},{"id":"yt/B","label":"Clip.js","path":"./node_modules/zrender/lib/animation/Clip.js","statSize":2809,"parsedSize":1050,"gzipSize":482}],"parsedSize":9699,"gzipSize":3353},{"id":"I0Vc","label":"Painter.js","path":"./node_modules/zrender/lib/Painter.js","statSize":27563,"parsedSize":10486,"gzipSize":3914},{"id":"OT4p","label":"Layer.js","path":"./node_modules/zrender/lib/Layer.js","statSize":5504,"parsedSize":2063,"gzipSize":981},{"id":"avYi","label":"Element.js","path":"./node_modules/zrender/lib/Element.js","statSize":5451,"parsedSize":1727,"gzipSize":735},{"label":"dom","path":"./node_modules/zrender/lib/dom","statSize":17709,"groups":[{"id":"e8/X","label":"HandlerProxy.js","path":"./node_modules/zrender/lib/dom/HandlerProxy.js","statSize":17709,"parsedSize":4283,"gzipSize":1532}],"parsedSize":4283,"gzipSize":1532},{"id":"g+yZ","label":"config.js","path":"./node_modules/zrender/lib/config.js","statSize":490,"parsedSize":136,"gzipSize":131},{"id":"hv2j","label":"zrender.js","path":"./node_modules/zrender/lib/zrender.js","statSize":10302,"parsedSize":3138,"gzipSize":1103},{"id":"lj6Z","label":"Handler.js","path":"./node_modules/zrender/lib/Handler.js","statSize":13226,"parsedSize":3493,"gzipSize":1455}],"parsedSize":165866,"gzipSize":56258}],"parsedSize":165866,"gzipSize":56258}],"parsedSize":845415,"gzipSize":273558}]},{"label":"static/js/9.98d0acc720fc77066fb0.*************.y9bzfe.js","statSize":32007,"parsedSize":18123,"gzipSize":4755,"groups":[{"label":"static","path":"./static","statSize":3672,"groups":[{"label":"img","path":"./static/img","statSize":3672,"groups":[{"label":"foot ^\\.\\","path":"./static/img/foot ^\\.\\","statSize":2106,"groups":[{"label":".*\\","path":"./static/img/foot ^\\.\\/.*\\","statSize":2106,"groups":[{"id":"/RXH","label":"home\\.png$","path":"./static/img/foot ^\\.\\/.*\\/home\\.png$","statSize":200,"parsedSize":271,"gzipSize":215},{"id":"/Ti4","label":"hangqing\\.png$","path":"./static/img/foot ^\\.\\/.*\\/hangqing\\.png$","statSize":208,"parsedSize":279,"gzipSize":220},{"id":"0Jzj","label":"hangqing\\-active\\.png$","path":"./static/img/foot ^\\.\\/.*\\/hangqing\\-active\\.png$","statSize":222,"parsedSize":293,"gzipSize":222},{"id":"Tbjy","label":"zixuan\\-active\\.png$","path":"./static/img/foot ^\\.\\/.*\\/zixuan\\-active\\.png$","statSize":218,"parsedSize":289,"gzipSize":221},{"id":"Tw+E","label":"user\\-active\\.png$","path":"./static/img/foot ^\\.\\/.*\\/user\\-active\\.png$","statSize":214,"parsedSize":285,"gzipSize":219},{"id":"UJhX","label":"home\\-active\\.png$","path":"./static/img/foot ^\\.\\/.*\\/home\\-active\\.png$","statSize":214,"parsedSize":285,"gzipSize":221},{"id":"VqUm","label":"chicang\\.png$","path":"./static/img/foot ^\\.\\/.*\\/chicang\\.png$","statSize":206,"parsedSize":277,"gzipSize":217},{"id":"bHzZ","label":"zixuan\\.png$","path":"./static/img/foot ^\\.\\/.*\\/zixuan\\.png$","statSize":204,"parsedSize":275,"gzipSize":218},{"id":"qr9D","label":"chicang\\-active\\.png$","path":"./static/img/foot ^\\.\\/.*\\/chicang\\-active\\.png$","statSize":220,"parsedSize":291,"gzipSize":224},{"id":"uMEc","label":"user\\.png$","path":"./static/img/foot ^\\.\\/.*\\/user\\.png$","statSize":200,"parsedSize":271,"gzipSize":213}],"parsedSize":2816,"gzipSize":454}],"parsedSize":2816,"gzipSize":454},{"label":"foot","path":"./static/img/foot","statSize":1566,"groups":[{"label":"black","path":"./static/img/foot/black","statSize":783,"groups":[{"id":"7XQ0","label":"hangqing.png","path":"./static/img/foot/black/hangqing.png","statSize":77,"parsedSize":64,"gzipSize":84},{"id":"NDjZ","label":"zixuan.png","path":"./static/img/foot/black/zixuan.png","statSize":75,"parsedSize":62,"gzipSize":82},{"id":"UwgZ","label":"user-active.png","path":"./static/img/foot/black/user-active.png","statSize":80,"parsedSize":67,"gzipSize":86},{"id":"eLxI","label":"user.png","path":"./static/img/foot/black/user.png","statSize":73,"parsedSize":60,"gzipSize":80},{"id":"kxWX","label":"chicang.png","path":"./static/img/foot/black/chicang.png","statSize":76,"parsedSize":63,"gzipSize":83},{"id":"oE32","label":"home.png","path":"./static/img/foot/black/home.png","statSize":73,"parsedSize":60,"gzipSize":80},{"id":"rnXr","label":"hangqing-active.png","path":"./static/img/foot/black/hangqing-active.png","statSize":84,"parsedSize":71,"gzipSize":90},{"id":"v1UV","label":"home-active.png","path":"./static/img/foot/black/home-active.png","statSize":80,"parsedSize":67,"gzipSize":86},{"id":"vKAT","label":"chicang-active.png","path":"./static/img/foot/black/chicang-active.png","statSize":83,"parsedSize":70,"gzipSize":89},{"id":"ve9t","label":"zixuan-active.png","path":"./static/img/foot/black/zixuan-active.png","statSize":82,"parsedSize":69,"gzipSize":88}],"parsedSize":653,"gzipSize":192},{"label":"red","path":"./static/img/foot/red","statSize":783,"groups":[{"id":"8Ex9","label":"chicang-active.png","path":"./static/img/foot/red/chicang-active.png","statSize":83,"parsedSize":70,"gzipSize":89},{"id":"ErCa","label":"hangqing-active.png","path":"./static/img/foot/red/hangqing-active.png","statSize":84,"parsedSize":71,"gzipSize":90},{"id":"GTme","label":"user-active.png","path":"./static/img/foot/red/user-active.png","statSize":80,"parsedSize":67,"gzipSize":86},{"id":"Iln+","label":"hangqing.png","path":"./static/img/foot/red/hangqing.png","statSize":77,"parsedSize":64,"gzipSize":84},{"id":"bBpS","label":"zixuan.png","path":"./static/img/foot/red/zixuan.png","statSize":75,"parsedSize":62,"gzipSize":82},{"id":"le/q","label":"user.png","path":"./static/img/foot/red/user.png","statSize":73,"parsedSize":60,"gzipSize":80},{"id":"rxde","label":"chicang.png","path":"./static/img/foot/red/chicang.png","statSize":76,"parsedSize":63,"gzipSize":83},{"id":"utCc","label":"home.png","path":"./static/img/foot/red/home.png","statSize":73,"parsedSize":60,"gzipSize":80},{"id":"vO58","label":"zixuan-active.png","path":"./static/img/foot/red/zixuan-active.png","statSize":82,"parsedSize":69,"gzipSize":88},{"id":"yn0l","label":"home-active.png","path":"./static/img/foot/red/home-active.png","statSize":80,"parsedSize":67,"gzipSize":86}],"parsedSize":653,"gzipSize":189}],"parsedSize":1306,"gzipSize":261}],"parsedSize":4122,"gzipSize":649}],"parsedSize":4122,"gzipSize":649},{"label":"src","path":"./src","statSize":28335,"groups":[{"label":"components","path":"./src/components","statSize":6488,"groups":[{"label":"foot","path":"./src/components/foot","statSize":6488,"groups":[{"id":"T/Hw","label":"foot.vue","path":"./src/components/foot/foot.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"hmkV","label":"foot.vue + 2 modules","path":"./src/components/foot/foot.vue + 2 modules","statSize":6447,"parsedSize":3458,"gzipSize":902}],"parsedSize":3473,"gzipSize":905}],"parsedSize":3473,"gzipSize":905},{"label":"page","path":"./src/page","statSize":21847,"groups":[{"label":"list","path":"./src/page/list","statSize":21847,"groups":[{"id":"ER26","label":"my-list.vue","path":"./src/page/list/my-list.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"KYM2","label":"index-list.vue","path":"./src/page/list/index-list.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"tNaX","label":"my-list.vue + 2 modules","path":"./src/page/list/my-list.vue + 2 modules","statSize":10740,"parsedSize":5165,"gzipSize":2193},{"id":"vNhn","label":"index-list.vue + 2 modules","path":"./src/page/list/index-list.vue + 2 modules","statSize":11025,"parsedSize":5003,"gzipSize":1780}],"parsedSize":10198,"gzipSize":3332}],"parsedSize":10198,"gzipSize":3332}],"parsedSize":13671,"gzipSize":3983}]},{"label":"static/js/10.4709e9d8228c73c45d1a.*************.y9bzfe.js","statSize":41239,"parsedSize":18503,"gzipSize":4538,"groups":[{"label":"src","path":"./src","statSize":41239,"groups":[{"label":"page","path":"./src/page","statSize":41239,"groups":[{"label":"home","path":"./src/page/home","statSize":41239,"groups":[{"id":"TruT","label":"chicang.vue + 2 modules","path":"./src/page/home/<USER>","statSize":29011,"parsedSize":12797,"gzipSize":3344},{"id":"UXuT","label":"chicangDetail.vue + 2 modules","path":"./src/page/home/<USER>","statSize":12146,"parsedSize":5553,"gzipSize":2033},{"id":"WKBe","label":"chicang.vue","path":"./src/page/home/<USER>","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"X0/c","label":"chicangDetail.vue","path":"./src/page/home/<USER>","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":18380,"gzipSize":4460}],"parsedSize":18380,"gzipSize":4460}],"parsedSize":18380,"gzipSize":4460}]},{"label":"static/js/11.2778ad79c84193de8650.*************.y9bzfe.js","statSize":9162,"parsedSize":3516,"gzipSize":1109,"groups":[{"label":"src","path":"./src","statSize":9162,"groups":[{"label":"page","path":"./src/page","statSize":9162,"groups":[{"label":"home","path":"./src/page/home","statSize":9162,"groups":[{"id":"0xX1","label":"newPage.vue","path":"./src/page/home/<USER>","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"3jbf","label":"newPage.vue + 2 modules","path":"./src/page/home/<USER>","statSize":4696,"parsedSize":1799,"gzipSize":891},{"id":"Pzii","label":"newGg.vue + 2 modules","path":"./src/page/home/<USER>","statSize":4384,"parsedSize":1562,"gzipSize":782},{"id":"rIV3","label":"newGg.vue","path":"./src/page/home/<USER>","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":3391,"gzipSize":1032}],"parsedSize":3391,"gzipSize":1032}],"parsedSize":3391,"gzipSize":1032}]},{"label":"static/js/12.05f992ced93fd1f8d39a.*************.y9bzfe.js","statSize":3925531,"parsedSize":2079490,"gzipSize":466850,"groups":[{"label":"buildin","path":"./buildin","statSize":517,"groups":[{"id":"3IRH","label":"module.js","path":"./buildin/module.js","statSize":517,"parsedSize":301,"gzipSize":181}],"parsedSize":301,"gzipSize":181},{"label":"node_modules","path":"./node_modules","statSize":3799485,"groups":[{"label":"element-resize-detector","path":"./node_modules/element-resize-detector","statSize":62181,"groups":[{"label":"src","path":"./node_modules/element-resize-detector/src","statSize":62181,"groups":[{"id":"+1cx","label":"state-handler.js","path":"./node_modules/element-resize-detector/src/state-handler.js","statSize":339,"parsedSize":170,"gzipSize":132},{"id":"601f","label":"id-handler.js","path":"./node_modules/element-resize-detector/src/id-handler.js","statSize":1202,"parsedSize":317,"gzipSize":220},{"id":"CGCf","label":"id-generator.js","path":"./node_modules/element-resize-detector/src/id-generator.js","statSize":309,"parsedSize":99,"gzipSize":98},{"id":"Og1S","label":"reporter.js","path":"./node_modules/element-resize-detector/src/reporter.js","statSize":1174,"parsedSize":312,"gzipSize":210},{"label":"detection-strategy","path":"./node_modules/element-resize-detector/src/detection-strategy","statSize":40512,"groups":[{"id":"S1QW","label":"scroll.js","path":"./node_modules/element-resize-detector/src/detection-strategy/scroll.js","statSize":29665,"parsedSize":9260,"gzipSize":3095},{"id":"SKY5","label":"object.js","path":"./node_modules/element-resize-detector/src/detection-strategy/object.js","statSize":10847,"parsedSize":2366,"gzipSize":1154}],"parsedSize":11626,"gzipSize":3705},{"id":"Saiw","label":"browser-detector.js","path":"./node_modules/element-resize-detector/src/browser-detector.js","statSize":919,"parsedSize":437,"gzipSize":321},{"id":"dUh9","label":"listener-handler.js","path":"./node_modules/element-resize-detector/src/listener-handler.js","statSize":1609,"parsedSize":358,"gzipSize":243},{"id":"dbB1","label":"collection-utils.js","path":"./node_modules/element-resize-detector/src/collection-utils.js","statSize":870,"parsedSize":124,"gzipSize":124},{"id":"p1oW","label":"element-utils.js","path":"./node_modules/element-resize-detector/src/element-utils.js","statSize":1505,"parsedSize":281,"gzipSize":172},{"id":"uk2G","label":"element-resize-detector.js","path":"./node_modules/element-resize-detector/src/element-resize-detector.js","statSize":13742,"parsedSize":2999,"gzipSize":1302}],"parsedSize":16723,"gzipSize":5385}],"parsedSize":16723,"gzipSize":5385},{"label":"hqchart","path":"./node_modules/hqchart","statSize":2895095,"groups":[{"label":"lib","path":"./node_modules/hqchart/lib","statSize":2895013,"groups":[{"id":"0pxj","label":"umychart.vue.js","path":"./node_modules/hqchart/lib/umychart.vue.js","statSize":2727110,"parsedSize":1773115,"gzipSize":374549},{"id":"5tow","label":"umychart.regressiontest.vue.js","path":"./node_modules/hqchart/lib/umychart.regressiontest.vue.js","statSize":15492,"parsedSize":5557,"gzipSize":1961},{"id":"XLO+","label":"umychart.stock.vue.js","path":"./node_modules/hqchart/lib/umychart.stock.vue.js","statSize":151790,"parsedSize":62767,"gzipSize":13837},{"id":"xQcD","label":"main.js","path":"./node_modules/hqchart/lib/main.js","statSize":621,"parsedSize":140,"gzipSize":119}],"parsedSize":1841579,"gzipSize":389840},{"label":"src","path":"./node_modules/hqchart/src","statSize":82,"groups":[{"label":"jscommon","path":"./node_modules/hqchart/src/jscommon","statSize":82,"groups":[{"label":"umychart.resource","path":"./node_modules/hqchart/src/jscommon/umychart.resource","statSize":82,"groups":[{"label":"css","path":"./node_modules/hqchart/src/jscommon/umychart.resource/css","statSize":41,"groups":[{"id":"84sD","label":"tools.css","path":"./node_modules/hqchart/src/jscommon/umychart.resource/css/tools.css","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":15,"gzipSize":35},{"label":"font","path":"./node_modules/hqchart/src/jscommon/umychart.resource/font","statSize":41,"groups":[{"id":"AHEq","label":"iconfont.css","path":"./node_modules/hqchart/src/jscommon/umychart.resource/font/iconfont.css","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":15,"gzipSize":35}],"parsedSize":30,"gzipSize":38}],"parsedSize":30,"gzipSize":38}],"parsedSize":30,"gzipSize":38}],"parsedSize":1841609,"gzipSize":389845},{"label":"jquery","path":"./node_modules/jquery","statSize":289812,"groups":[{"label":"dist","path":"./node_modules/jquery/dist","statSize":289812,"groups":[{"id":"7t+N","label":"jquery.js","path":"./node_modules/jquery/dist/jquery.js","statSize":289812,"parsedSize":90159,"gzipSize":31111}],"parsedSize":90159,"gzipSize":31111}],"parsedSize":90159,"gzipSize":31111},{"label":"core-js","path":"./node_modules/core-js","statSize":3084,"groups":[{"label":"library","path":"./node_modules/core-js/library","statSize":3084,"groups":[{"label":"fn","path":"./node_modules/core-js/library/fn","statSize":1149,"groups":[{"label":"object","path":"./node_modules/core-js/library/fn/object","statSize":590,"groups":[{"id":"9bBU","label":"define-property.js","path":"./node_modules/core-js/library/fn/object/define-property.js","statSize":215,"parsedSize":107,"gzipSize":107},{"id":"CJli","label":"define-properties.js","path":"./node_modules/core-js/library/fn/object/define-properties.js","statSize":203,"parsedSize":105,"gzipSize":111},{"id":"oM7Q","label":"create.js","path":"./node_modules/core-js/library/fn/object/create.js","statSize":172,"parsedSize":95,"gzipSize":101}],"parsedSize":307,"gzipSize":141},{"id":"fxRn","label":"get-iterator.js","path":"./node_modules/core-js/library/fn/get-iterator.js","statSize":141,"parsedSize":56,"gzipSize":72},{"label":"math","path":"./node_modules/core-js/library/fn/math","statSize":101,"groups":[{"id":"iggS","label":"log10.js","path":"./node_modules/core-js/library/fn/math/log10.js","statSize":101,"parsedSize":57,"gzipSize":76}],"parsedSize":57,"gzipSize":76},{"id":"pPW7","label":"set.js","path":"./node_modules/core-js/library/fn/set.js","statSize":317,"parsedSize":110,"gzipSize":105}],"parsedSize":530,"gzipSize":216},{"label":"modules","path":"./node_modules/core-js/library/modules","statSize":1935,"groups":[{"id":"BDhv","label":"es7.set.to-json.js","path":"./node_modules/core-js/library/modules/es7.set.to-json.js","statSize":188,"parsedSize":75,"gzipSize":91},{"id":"eaw8","label":"es6.math.log10.js","path":"./node_modules/core-js/library/modules/es6.math.log10.js","statSize":168,"parsedSize":98,"gzipSize":103},{"id":"g8Ux","label":"core.get-iterator.js","path":"./node_modules/core-js/library/modules/core.get-iterator.js","statSize":296,"parsedSize":183,"gzipSize":162},{"id":"ioQ5","label":"es7.set.from.js","path":"./node_modules/core-js/library/modules/es7.set.from.js","statSize":105,"parsedSize":33,"gzipSize":53},{"id":"mClu","label":"es6.object.define-property.js","path":"./node_modules/core-js/library/modules/es6.object.define-property.js","statSize":217,"parsedSize":92,"gzipSize":110},{"id":"oNmr","label":"es7.set.of.js","path":"./node_modules/core-js/library/modules/es7.set.of.js","statSize":101,"parsedSize":33,"gzipSize":53},{"id":"pRCB","label":"es6.object.define-properties.js","path":"./node_modules/core-js/library/modules/es6.object.define-properties.js","statSize":217,"parsedSize":92,"gzipSize":110},{"id":"sF+V","label":"es6.object.create.js","path":"./node_modules/core-js/library/modules/es6.object.create.js","statSize":162,"parsedSize":67,"gzipSize":86},{"id":"ttyz","label":"es6.set.js","path":"./node_modules/core-js/library/modules/es6.set.js","statSize":481,"parsedSize":233,"gzipSize":181}],"parsedSize":906,"gzipSize":426}],"parsedSize":1436,"gzipSize":542}],"parsedSize":1436,"gzipSize":542},{"label":"babel-runtime","path":"./node_modules/babel-runtime","statSize":1110,"groups":[{"label":"core-js","path":"./node_modules/babel-runtime/core-js","statSize":570,"groups":[{"id":"BO1k","label":"get-iterator.js","path":"./node_modules/babel-runtime/core-js/get-iterator.js","statSize":93,"parsedSize":60,"gzipSize":80},{"label":"object","path":"./node_modules/babel-runtime/core-js/object","statSize":302,"groups":[{"id":"C4MV","label":"define-property.js","path":"./node_modules/babel-runtime/core-js/object/define-property.js","statSize":103,"parsedSize":60,"gzipSize":80},{"id":"HSQo","label":"define-properties.js","path":"./node_modules/babel-runtime/core-js/object/define-properties.js","statSize":105,"parsedSize":60,"gzipSize":80},{"id":"OvRC","label":"create.js","path":"./node_modules/babel-runtime/core-js/object/create.js","statSize":94,"parsedSize":60,"gzipSize":80}],"parsedSize":180,"gzipSize":96},{"label":"math","path":"./node_modules/babel-runtime/core-js/math","statSize":91,"groups":[{"id":"j9JH","label":"log10.js","path":"./node_modules/babel-runtime/core-js/math/log10.js","statSize":91,"parsedSize":60,"gzipSize":80}],"parsedSize":60,"gzipSize":80},{"id":"lHA8","label":"set.js","path":"./node_modules/babel-runtime/core-js/set.js","statSize":84,"parsedSize":60,"gzipSize":80}],"parsedSize":360,"gzipSize":115},{"label":"helpers","path":"./node_modules/babel-runtime/helpers","statSize":540,"groups":[{"id":"bOdI","label":"defineProperty.js","path":"./node_modules/babel-runtime/helpers/defineProperty.js","statSize":540,"parsedSize":220,"gzipSize":174}],"parsedSize":220,"gzipSize":174}],"parsedSize":580,"gzipSize":235},{"label":"batch-processor","path":"./node_modules/batch-processor","statSize":4105,"groups":[{"label":"src","path":"./node_modules/batch-processor/src","statSize":4105,"groups":[{"id":"GpqH","label":"batch-processor.js","path":"./node_modules/batch-processor/src/batch-processor.js","statSize":3808,"parsedSize":768,"gzipSize":465},{"id":"LBxF","label":"utils.js","path":"./node_modules/batch-processor/src/utils.js","statSize":297,"parsedSize":138,"gzipSize":128}],"parsedSize":906,"gzipSize":509}],"parsedSize":906,"gzipSize":509},{"label":"lodash","path":"./node_modules/lodash","statSize":544098,"groups":[{"id":"M4fF","label":"lodash.js","path":"./node_modules/lodash/lodash.js","statSize":544098,"parsedSize":71029,"gzipSize":24917}],"parsedSize":71029,"gzipSize":24917}],"parsedSize":2022442,"gzipSize":451891},{"label":"src","path":"./src","statSize":125529,"groups":[{"label":"page","path":"./src/page","statSize":125453,"groups":[{"label":"kline","path":"./src/page/kline","statSize":125453,"groups":[{"id":"7Qxd","label":"index.vue","path":"./src/page/kline/index.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"LCit","label":"index.vue + 12 modules","path":"./src/page/kline/index.vue + 12 modules","statSize":125371,"parsedSize":56247,"gzipSize":14052},{"label":"components","path":"./src/page/kline/components","statSize":41,"groups":[{"id":"gLXz","label":"kLine.vue","path":"./src/page/kline/components/kLine.vue","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":15,"gzipSize":35}],"parsedSize":56277,"gzipSize":14060}],"parsedSize":56277,"gzipSize":14060},{"label":"assets","path":"./src/assets","statSize":76,"groups":[{"label":"img","path":"./src/assets/img","statSize":76,"groups":[{"id":"tHi6","label":"options.png","path":"./src/assets/img/options.png","statSize":76,"parsedSize":63,"gzipSize":81}],"parsedSize":63,"gzipSize":81}],"parsedSize":63,"gzipSize":81}],"parsedSize":56340,"gzipSize":14090}]},{"label":"static/js/13.c8c6cf826bd1976a0751.*************.y9bzfe.js","statSize":7448,"parsedSize":3230,"gzipSize":1821,"groups":[{"label":"src","path":"./src","statSize":7448,"groups":[{"label":"assets","path":"./src/assets","statSize":151,"groups":[{"label":"home","path":"./src/assets/home","statSize":73,"groups":[{"id":"QQ3S","label":"logo.png","path":"./src/assets/home/<USER>","statSize":73,"parsedSize":60,"gzipSize":80}],"parsedSize":60,"gzipSize":80},{"label":"img","path":"./src/assets/img","statSize":78,"groups":[{"id":"flri","label":"about-bg3.png","path":"./src/assets/img/about-bg3.png","statSize":78,"parsedSize":65,"gzipSize":85}],"parsedSize":65,"gzipSize":85}],"parsedSize":125,"gzipSize":102},{"label":"page","path":"./src/page","statSize":7297,"groups":[{"label":"newUser","path":"./src/page/newUser","statSize":7297,"groups":[{"id":"QY6I","label":"about.vue + 2 modules","path":"./src/page/newUser/about.vue + 2 modules","statSize":7256,"parsedSize":2969,"gzipSize":1688},{"id":"ndtc","label":"about.vue","path":"./src/page/newUser/about.vue","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":2984,"gzipSize":1694}],"parsedSize":2984,"gzipSize":1694}],"parsedSize":3109,"gzipSize":1737}]},{"label":"static/js/14.2c61745b0bc57645996f.*************.y9bzfe.js","statSize":19942,"parsedSize":17518,"gzipSize":12305,"groups":[{"label":"src","path":"./src","statSize":19942,"groups":[{"label":"page","path":"./src/page","statSize":19942,"groups":[{"id":"VxjW","label":"openaccount.vue + 2 modules","path":"./src/page/openaccount.vue + 2 modules","statSize":19901,"parsedSize":17394,"gzipSize":12228},{"id":"kRfG","label":"openaccount.vue","path":"./src/page/openaccount.vue","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":17409,"gzipSize":12237}],"parsedSize":17409,"gzipSize":12237}]},{"label":"static/js/15.addfc0db69090820921a.*************.y9bzfe.js","statSize":6808,"parsedSize":3068,"gzipSize":1267,"groups":[{"label":"src","path":"./src","statSize":6808,"groups":[{"label":"page","path":"./src/page","statSize":6808,"groups":[{"label":"home","path":"./src/page/home","statSize":6808,"groups":[{"id":"M4Fd","label":"weituoDetail.vue + 2 modules","path":"./src/page/home/<USER>","statSize":6767,"parsedSize":2944,"gzipSize":1196},{"id":"S9H8","label":"weituoDetail.vue","path":"./src/page/home/<USER>","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":2959,"gzipSize":1202}],"parsedSize":2959,"gzipSize":1202}],"parsedSize":2959,"gzipSize":1202}]},{"label":"static/js/16.d0e7f52b492b5419cdfc.*************.y9bzfe.js","statSize":19814,"parsedSize":9503,"gzipSize":2908,"groups":[{"label":"src","path":"./src","statSize":19814,"groups":[{"label":"page","path":"./src/page","statSize":19814,"groups":[{"label":"home","path":"./src/page/home","statSize":19814,"groups":[{"id":"GS4Z","label":"dazong.vue","path":"./src/page/home/<USER>","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"IkwI","label":"dazong.vue + 2 modules","path":"./src/page/home/<USER>","statSize":19773,"parsedSize":9379,"gzipSize":2833}],"parsedSize":9394,"gzipSize":2837}],"parsedSize":9394,"gzipSize":2837}],"parsedSize":9394,"gzipSize":2837}]},{"label":"static/js/17.ef2fd1d8186428e1999c.*************.y9bzfe.js","statSize":20808,"parsedSize":8162,"gzipSize":3143,"groups":[{"label":"src","path":"./src","statSize":20808,"groups":[{"label":"page","path":"./src/page","statSize":20808,"groups":[{"label":"speedtest","path":"./src/page/speedtest","statSize":20808,"groups":[{"id":"BCj1","label":"index.vue","path":"./src/page/speedtest/index.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"CxV0","label":"index.vue + 2 modules","path":"./src/page/speedtest/index.vue + 2 modules","statSize":20767,"parsedSize":8038,"gzipSize":3069}],"parsedSize":8053,"gzipSize":3073}],"parsedSize":8053,"gzipSize":3073}],"parsedSize":8053,"gzipSize":3073}]},{"label":"static/js/18.78c7aa2977650f762097.*************.y9bzfe.js","statSize":2369,"parsedSize":954,"gzipSize":588,"groups":[{"label":"src","path":"./src","statSize":2369,"groups":[{"label":"page","path":"./src/page","statSize":2369,"groups":[{"label":"home","path":"./src/page/home","statSize":2369,"groups":[{"id":"Rulh","label":"jijin.vue + 1 modules","path":"./src/page/home/<USER>","statSize":2328,"parsedSize":830,"gzipSize":518},{"id":"blOm","label":"jijin.vue","path":"./src/page/home/<USER>","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":845,"gzipSize":524}],"parsedSize":845,"gzipSize":524}],"parsedSize":845,"gzipSize":524}]},{"label":"static/js/19.958c13dbd221f9040683.*************.y9bzfe.js","statSize":6084,"parsedSize":4689,"gzipSize":3507,"groups":[{"label":"src","path":"./src","statSize":6084,"groups":[{"label":"page","path":"./src/page","statSize":6084,"groups":[{"label":"home","path":"./src/page/home","statSize":6084,"groups":[{"id":"kffX","label":"college.vue + 1 modules","path":"./src/page/home/<USER>","statSize":6043,"parsedSize":4564,"gzipSize":3435},{"id":"xI5QS","label":"college.vue","path":"./src/page/home/<USER>","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":4579,"gzipSize":3443}],"parsedSize":4579,"gzipSize":3443}],"parsedSize":4579,"gzipSize":3443}]},{"label":"static/js/app.673108bed16259bb3026.*************.y9bzfe.js","statSize":4256046,"parsedSize":1941770,"gzipSize":548650,"groups":[{"label":"node_modules","path":"./node_modules","statSize":3648312,"groups":[{"label":"normalize-wheel","path":"./node_modules/normalize-wheel","statSize":17234,"groups":[{"label":"src","path":"./node_modules/normalize-wheel/src","statSize":17181,"groups":[{"id":"++K3","label":"UserAgent_DEPRECATED.js","path":"./node_modules/normalize-wheel/src/UserAgent_DEPRECATED.js","statSize":7369,"parsedSize":1550,"gzipSize":689},{"id":"Y5mS","label":"isEventSupported.js","path":"./node_modules/normalize-wheel/src/isEventSupported.js","statSize":1984,"parsedSize":962,"gzipSize":553},{"id":"YAhB","label":"normalizeWheel.js","path":"./node_modules/normalize-wheel/src/normalizeWheel.js","statSize":6698,"parsedSize":625,"gzipSize":376},{"id":"lFkc","label":"ExecutionEnvironment.js","path":"./node_modules/normalize-wheel/src/ExecutionEnvironment.js","statSize":1130,"parsedSize":305,"gzipSize":203}],"parsedSize":3442,"gzipSize":1554},{"id":"3fo+","label":"index.js","path":"./node_modules/normalize-wheel/index.js","statSize":53,"parsedSize":36,"gzipSize":56}],"parsedSize":3478,"gzipSize":1564},{"label":"has-symbols","path":"./node_modules/has-symbols","statSize":2181,"groups":[{"id":"76UX","label":"index.js","path":"./node_modules/has-symbols/index.js","statSize":420,"parsedSize":228,"gzipSize":156},{"id":"9MlZ","label":"shams.js","path":"./node_modules/has-symbols/shams.js","statSize":1761,"parsedSize":867,"gzipSize":349}],"parsedSize":1095,"gzipSize":412},{"label":"get-intrinsic","path":"./node_modules/get-intrinsic","statSize":12719,"groups":[{"id":"7ar3","label":"index.js","path":"./node_modules/get-intrinsic/index.js","statSize":12719,"parsedSize":8015,"gzipSize":2411}],"parsedSize":8015,"gzipSize":2411},{"label":"object-inspect","path":"./node_modules/object-inspect","statSize":18486,"groups":[{"id":"GybJ","label":"index.js","path":"./node_modules/object-inspect/index.js","statSize":18486,"parsedSize":8871,"gzipSize":3089}],"parsedSize":8871,"gzipSize":3089},{"label":"call-bind","path":"./node_modules/call-bind","statSize":1719,"groups":[{"id":"gcUb","label":"callBound.js","path":"./node_modules/call-bind/callBound.js","statSize":413,"parsedSize":189,"gzipSize":162},{"id":"WER7","label":"index.js","path":"./node_modules/call-bind/index.js","statSize":1306,"parsedSize":547,"gzipSize":331}],"parsedSize":736,"gzipSize":404},{"label":"core-js","path":"./node_modules/core-js","statSize":73666,"groups":[{"label":"library","path":"./node_modules/core-js/library","statSize":73666,"groups":[{"label":"modules","path":"./node_modules/core-js/library/modules","statSize":72053,"groups":[{"id":"+E39","label":"_descriptors.js","path":"./node_modules/core-js/library/modules/_descriptors.js","statSize":184,"parsedSize":119,"gzipSize":114},{"id":"+ZMJ","label":"_ctx.js","path":"./node_modules/core-js/library/modules/_ctx.js","statSize":520,"parsedSize":294,"gzipSize":164},{"id":"+tPU","label":"web.dom.iterable.js","path":"./node_modules/core-js/library/modules/web.dom.iterable.js","statSize":969,"parsedSize":638,"gzipSize":401},{"id":"/bQp","label":"_iterators.js","path":"./node_modules/core-js/library/modules/_iterators.js","statSize":21,"parsedSize":27,"gzipSize":47},{"id":"06OY","label":"_meta.js","path":"./node_modules/core-js/library/modules/_meta.js","statSize":1558,"parsedSize":572,"gzipSize":349},{"id":"1kS7","label":"_object-gops.js","path":"./node_modules/core-js/library/modules/_object-gops.js","statSize":42,"parsedSize":47,"gzipSize":67},{"id":"2KxR","label":"_an-instance.js","path":"./node_modules/core-js/library/modules/_an-instance.js","statSize":237,"parsedSize":138,"gzipSize":132},{"id":"3Eo+","label":"_uid.js","path":"./node_modules/core-js/library/modules/_uid.js","statSize":162,"parsedSize":127,"gzipSize":132},{"id":"3fs2","label":"core.get-iterator-method.js","path":"./node_modules/core-js/library/modules/core.get-iterator-method.js","statSize":297,"parsedSize":170,"gzipSize":158},{"id":"4WTo","label":"_array-from-iterable.js","path":"./node_modules/core-js/library/modules/_array-from-iterable.js","statSize":172,"parsedSize":94,"gzipSize":99},{"id":"4mcu","label":"_add-to-unscopables.js","path":"./node_modules/core-js/library/modules/_add-to-unscopables.js","statSize":46,"parsedSize":37,"gzipSize":51},{"id":"52gC","label":"_defined.js","path":"./node_modules/core-js/library/modules/_defined.js","statSize":162,"parsedSize":103,"gzipSize":113},{"id":"77Pl","label":"_an-object.js","path":"./node_modules/core-js/library/modules/_an-object.js","statSize":154,"parsedSize":113,"gzipSize":120},{"id":"7Doy","label":"_array-species-constructor.js","path":"./node_modules/core-js/library/modules/_array-species-constructor.js","statSize":475,"parsedSize":240,"gzipSize":192},{"id":"7KvD","label":"_global.js","path":"./node_modules/core-js/library/modules/_global.js","statSize":369,"parsedSize":186,"gzipSize":147},{"id":"7UMu","label":"_is-array.js","path":"./node_modules/core-js/library/modules/_is-array.js","statSize":147,"parsedSize":90,"gzipSize":95},{"id":"82Mu","label":"_microtask.js","path":"./node_modules/core-js/library/modules/_microtask.js","statSize":1982,"parsedSize":674,"gzipSize":414},{"id":"880/","label":"_redefine.js","path":"./node_modules/core-js/library/modules/_redefine.js","statSize":37,"parsedSize":36,"gzipSize":56},{"id":"94VQ","label":"_iter-create.js","path":"./node_modules/core-js/library/modules/_iter-create.js","statSize":526,"parsedSize":210,"gzipSize":174},{"id":"9Bbf","label":"_set-collection-of.js","path":"./node_modules/core-js/library/modules/_set-collection-of.js","statSize":350,"parsedSize":178,"gzipSize":157},{"id":"9C8M","label":"_collection-strong.js","path":"./node_modules/core-js/library/modules/_collection-strong.js","statSize":5019,"parsedSize":1482,"gzipSize":785},{"id":"ALrJ","label":"_array-methods.js","path":"./node_modules/core-js/library/modules/_array-methods.js","statSize":1494,"parsedSize":448,"gzipSize":321},{"id":"CXw9","label":"es6.promise.js","path":"./node_modules/core-js/library/modules/es6.promise.js","statSize":9812,"parsedSize":3276,"gzipSize":1541},{"id":"Cdx3","label":"es6.object.keys.js","path":"./node_modules/core-js/library/modules/es6.object.keys.js","statSize":225,"parsedSize":109,"gzipSize":104},{"id":"D2L2","label":"_has.js","path":"./node_modules/core-js/library/modules/_has.js","statSize":120,"parsedSize":82,"gzipSize":88},{"id":"EGZi","label":"_iter-step.js","path":"./node_modules/core-js/library/modules/_iter-step.js","statSize":86,"parsedSize":64,"gzipSize":73},{"id":"EqBC","label":"es7.promise.finally.js","path":"./node_modules/core-js/library/modules/es7.promise.finally.js","statSize":763,"parsedSize":332,"gzipSize":213},{"id":"EqjI","label":"_is-object.js","path":"./node_modules/core-js/library/modules/_is-object.js","statSize":110,"parsedSize":92,"gzipSize":88},{"id":"FeBl","label":"_core.js","path":"./node_modules/core-js/library/modules/_core.js","statSize":123,"parsedSize":79,"gzipSize":96},{"id":"HpRW","label":"_set-collection-from.js","path":"./node_modules/core-js/library/modules/_set-collection-from.js","statSize":802,"parsedSize":319,"gzipSize":239},{"id":"Ibhu","label":"_object-keys-internal.js","path":"./node_modules/core-js/library/modules/_object-keys-internal.js","statSize":537,"parsedSize":228,"gzipSize":198},{"id":"Kh4W","label":"_wks-ext.js","path":"./node_modules/core-js/library/modules/_wks-ext.js","statSize":31,"parsedSize":30,"gzipSize":50},{"id":"L42u","label":"_task.js","path":"./node_modules/core-js/library/modules/_task.js","statSize":2484,"parsedSize":976,"gzipSize":554},{"id":"LIJb","label":"_validate-collection.js","path":"./node_modules/core-js/library/modules/_validate-collection.js","statSize":200,"parsedSize":143,"gzipSize":141},{"id":"LKZe","label":"_object-gopd.js","path":"./node_modules/core-js/library/modules/_object-gopd.js","statSize":577,"parsedSize":247,"gzipSize":208},{"id":"M6a0","label":"es6.object.to-string.js","path":"./node_modules/core-js/library/modules/es6.object.to-string.js","statSize":0,"parsedSize":15,"gzipSize":35},{"id":"MU5D","label":"_iobject.js","path":"./node_modules/core-js/library/modules/_iobject.js","statSize":289,"parsedSize":141,"gzipSize":140},{"id":"Mhyx","label":"_is-array-iter.js","path":"./node_modules/core-js/library/modules/_is-array-iter.js","statSize":279,"parsedSize":140,"gzipSize":138},{"id":"MmMw","label":"_to-primitive.js","path":"./node_modules/core-js/library/modules/_to-primitive.js","statSize":655,"parsedSize":334,"gzipSize":190},{"id":"NWt+","label":"_for-of.js","path":"./node_modules/core-js/library/modules/_for-of.js","statSize":1176,"parsedSize":458,"gzipSize":332},{"id":"NpIQ","label":"_object-pie.js","path":"./node_modules/core-js/library/modules/_object-pie.js","statSize":37,"parsedSize":42,"gzipSize":62},{"id":"O4g8","label":"_library.js","path":"./node_modules/core-js/library/modules/_library.js","statSize":23,"parsedSize":27,"gzipSize":47},{"id":"ON07","label":"_dom-create.js","path":"./node_modules/core-js/library/modules/_dom-create.js","statSize":289,"parsedSize":134,"gzipSize":127},{"id":"OYls","label":"es7.symbol.async-iterator.js","path":"./node_modules/core-js/library/modules/es7.symbol.async-iterator.js","statSize":43,"parsedSize":43,"gzipSize":63},{"id":"PzxK","label":"_object-gpo.js","path":"./node_modules/core-js/library/modules/_object-gpo.js","statSize":493,"parsedSize":271,"gzipSize":194},{"id":"QRG4","label":"_to-length.js","path":"./node_modules/core-js/library/modules/_to-length.js","statSize":215,"parsedSize":104,"gzipSize":116},{"id":"QWe/","label":"es7.symbol.observable.js","path":"./node_modules/core-js/library/modules/es7.symbol.observable.js","statSize":40,"parsedSize":40,"gzipSize":60},{"id":"R4wc","label":"es6.object.assign.js","path":"./node_modules/core-js/library/modules/es6.object.assign.js","statSize":162,"parsedSize":71,"gzipSize":90},{"id":"R9M2","label":"_cof.js","path":"./node_modules/core-js/library/modules/_cof.js","statSize":106,"parsedSize":84,"gzipSize":96},{"id":"RPLV","label":"_html.js","path":"./node_modules/core-js/library/modules/_html.js","statSize":101,"parsedSize":72,"gzipSize":82},{"id":"RY/4","label":"_classof.js","path":"./node_modules/core-js/library/modules/_classof.js","statSize":718,"parsedSize":335,"gzipSize":239},{"id":"Rrel","label":"_object-gopn-ext.js","path":"./node_modules/core-js/library/modules/_object-gopn-ext.js","statSize":604,"parsedSize":291,"gzipSize":211},{"id":"S82l","label":"_fails.js","path":"./node_modules/core-js/library/modules/_fails.js","statSize":104,"parsedSize":72,"gzipSize":76},{"id":"SfB7","label":"_ie8-dom-define.js","path":"./node_modules/core-js/library/modules/_ie8-dom-define.js","statSize":199,"parsedSize":145,"gzipSize":138},{"id":"TcQ7","label":"_to-iobject.js","path":"./node_modules/core-js/library/modules/_to-iobject.js","statSize":217,"parsedSize":82,"gzipSize":91},{"id":"To3L","label":"_object-assign.js","path":"./node_modules/core-js/library/modules/_object-assign.js","statSize":1281,"parsedSize":507,"gzipSize":369},{"id":"UuGF","label":"_to-integer.js","path":"./node_modules/core-js/library/modules/_to-integer.js","statSize":161,"parsedSize":100,"gzipSize":107},{"id":"UvrK","label":"es7.map.to-json.js","path":"./node_modules/core-js/library/modules/es7.map.to-json.js","statSize":188,"parsedSize":75,"gzipSize":91},{"id":"X8DO","label":"_property-desc.js","path":"./node_modules/core-js/library/modules/_property-desc.js","statSize":173,"parsedSize":109,"gzipSize":102},{"id":"Xc4G","label":"_enum-keys.js","path":"./node_modules/core-js/library/modules/_enum-keys.js","statSize":469,"parsedSize":179,"gzipSize":165},{"id":"Xjd4","label":"es7.map.of.js","path":"./node_modules/core-js/library/modules/es7.map.of.js","statSize":101,"parsedSize":33,"gzipSize":53},{"id":"Yobk","label":"_object-create.js","path":"./node_modules/core-js/library/modules/_object-create.js","statSize":1505,"parsedSize":499,"gzipSize":348},{"id":"ax3d","label":"_shared-key.js","path":"./node_modules/core-js/library/modules/_shared-key.js","statSize":159,"parsedSize":100,"gzipSize":107},{"id":"bRrM","label":"_set-species.js","path":"./node_modules/core-js/library/modules/_set-species.js","statSize":435,"parsedSize":235,"gzipSize":196},{"id":"bqnK","label":"es7.map.from.js","path":"./node_modules/core-js/library/modules/es7.map.from.js","statSize":105,"parsedSize":33,"gzipSize":53},{"id":"crlp","label":"_wks-define.js","path":"./node_modules/core-js/library/modules/_wks-define.js","statSize":417,"parsedSize":198,"gzipSize":170},{"id":"dNDb","label":"_perform.js","path":"./node_modules/core-js/library/modules/_perform.js","statSize":132,"parsedSize":87,"gzipSize":85},{"id":"dSzd","label":"_wks.js","path":"./node_modules/core-js/library/modules/_wks.js","statSize":358,"parsedSize":176,"gzipSize":161},{"id":"dY0y","label":"_iter-detect.js","path":"./node_modules/core-js/library/modules/_iter-detect.js","statSize":645,"parsedSize":301,"gzipSize":197},{"id":"e6n0","label":"_set-to-string-tag.js","path":"./node_modules/core-js/library/modules/_set-to-string-tag.js","statSize":262,"parsedSize":161,"gzipSize":152},{"id":"e8AB","label":"_shared.js","path":"./node_modules/core-js/library/modules/_shared.js","statSize":428,"parsedSize":284,"gzipSize":233},{"id":"evD5","label":"_object-dp.js","path":"./node_modules/core-js/library/modules/_object-dp.js","statSize":600,"parsedSize":292,"gzipSize":224},{"id":"fBQ2","label":"_create-property.js","path":"./node_modules/core-js/library/modules/_create-property.js","statSize":271,"parsedSize":114,"gzipSize":114},{"id":"fJUb","label":"_promise-resolve.js","path":"./node_modules/core-js/library/modules/_promise-resolve.js","statSize":397,"parsedSize":168,"gzipSize":153},{"id":"fWfb","label":"es6.symbol.js","path":"./node_modules/core-js/library/modules/es6.symbol.js","statSize":9284,"parsedSize":3450,"gzipSize":1695},{"id":"fkB2","label":"_to-absolute-index.js","path":"./node_modules/core-js/library/modules/_to-absolute-index.js","statSize":223,"parsedSize":112,"gzipSize":113},{"id":"h65t","label":"_string-at.js","path":"./node_modules/core-js/library/modules/_string-at.js","statSize":620,"parsedSize":302,"gzipSize":234},{"id":"hJx8","label":"_hide.js","path":"./node_modules/core-js/library/modules/_hide.js","statSize":286,"parsedSize":137,"gzipSize":114},{"id":"iUbK","label":"_user-agent.js","path":"./node_modules/core-js/library/modules/_user-agent.js","statSize":127,"parsedSize":71,"gzipSize":91},{"id":"jKW+","label":"es7.promise.try.js","path":"./node_modules/core-js/library/modules/es7.promise.try.js","statSize":477,"parsedSize":174,"gzipSize":158},{"id":"kM2E","label":"_export.js","path":"./node_modules/core-js/library/modules/_export.js","statSize":2349,"parsedSize":769,"gzipSize":466},{"id":"knuC","label":"_invoke.js","path":"./node_modules/core-js/library/modules/_invoke.js","statSize":701,"parsedSize":341,"gzipSize":169},{"id":"lOnJ","label":"_a-function.js","path":"./node_modules/core-js/library/modules/_a-function.js","statSize":125,"parsedSize":111,"gzipSize":107},{"id":"lktj","label":"_object-keys.js","path":"./node_modules/core-js/library/modules/_object-keys.js","statSize":222,"parsedSize":94,"gzipSize":104},{"id":"m9gC","label":"_collection-to-json.js","path":"./node_modules/core-js/library/modules/_collection-to-json.js","statSize":317,"parsedSize":158,"gzipSize":147},{"id":"msXi","label":"_iter-call.js","path":"./node_modules/core-js/library/modules/_iter-call.js","statSize":410,"parsedSize":153,"gzipSize":144},{"id":"n0T6","label":"_object-gopn.js","path":"./node_modules/core-js/library/modules/_object-gopn.js","statSize":288,"parsedSize":132,"gzipSize":132},{"id":"oeOm","label":"_array-species-create.js","path":"./node_modules/core-js/library/modules/_array-species-create.js","statSize":223,"parsedSize":77,"gzipSize":88},{"id":"qARP","label":"_new-promise-capability.js","path":"./node_modules/core-js/library/modules/_new-promise-capability.js","statSize":504,"parsedSize":252,"gzipSize":191},{"id":"qCoq","label":"es6.map.js","path":"./node_modules/core-js/library/modules/es6.map.js","statSize":642,"parsedSize":301,"gzipSize":213},{"id":"qio6","label":"_object-dps.js","path":"./node_modules/core-js/library/modules/_object-dps.js","statSize":404,"parsedSize":187,"gzipSize":170},{"id":"qo66","label":"_collection.js","path":"./node_modules/core-js/library/modules/_collection.js","statSize":2009,"parsedSize":889,"gzipSize":569},{"id":"qyJz","label":"es6.array.from.js","path":"./node_modules/core-js/library/modules/es6.array.from.js","statSize":1635,"parsedSize":569,"gzipSize":385},{"id":"sB3e","label":"_to-object.js","path":"./node_modules/core-js/library/modules/_to-object.js","statSize":132,"parsedSize":75,"gzipSize":87},{"id":"t8x9","label":"_species-constructor.js","path":"./node_modules/core-js/library/modules/_species-constructor.js","statSize":348,"parsedSize":163,"gzipSize":148},{"id":"uqUo","label":"_object-sap.js","path":"./node_modules/core-js/library/modules/_object-sap.js","statSize":370,"parsedSize":173,"gzipSize":151},{"id":"vFc/","label":"_array-includes.js","path":"./node_modules/core-js/library/modules/_array-includes.js","statSize":924,"parsedSize":258,"gzipSize":203},{"id":"vIB/","label":"_iter-define.js","path":"./node_modules/core-js/library/modules/_iter-define.js","statSize":2779,"parsedSize":911,"gzipSize":566},{"id":"xGkn","label":"es6.array.iterator.js","path":"./node_modules/core-js/library/modules/es6.array.iterator.js","statSize":1116,"parsedSize":363,"gzipSize":253},{"id":"xH/j","label":"_redefine-all.js","path":"./node_modules/core-js/library/modules/_redefine-all.js","statSize":217,"parsedSize":113,"gzipSize":110},{"id":"xnc9","label":"_enum-bug-keys.js","path":"./node_modules/core-js/library/modules/_enum-bug-keys.js","statSize":160,"parsedSize":131,"gzipSize":128},{"id":"zQR9","label":"es6.string.iterator.js","path":"./node_modules/core-js/library/modules/es6.string.iterator.js","statSize":531,"parsedSize":244,"gzipSize":180}],"parsedSize":29262,"gzipSize":10570},{"label":"fn","path":"./node_modules/core-js/library/fn","statSize":1613,"groups":[{"label":"symbol","path":"./node_modules/core-js/library/fn/symbol","statSize":395,"groups":[{"id":"/n6Q","label":"iterator.js","path":"./node_modules/core-js/library/fn/symbol/iterator.js","statSize":155,"parsedSize":70,"gzipSize":86},{"id":"BwfY","label":"index.js","path":"./node_modules/core-js/library/fn/symbol/index.js","statSize":240,"parsedSize":83,"gzipSize":91}],"parsedSize":153,"gzipSize":126},{"id":"3C/1","label":"map.js","path":"./node_modules/core-js/library/fn/map.js","statSize":317,"parsedSize":110,"gzipSize":104},{"label":"array","path":"./node_modules/core-js/library/fn/array","statSize":147,"groups":[{"id":"5zde","label":"from.js","path":"./node_modules/core-js/library/fn/array/from.js","statSize":147,"parsedSize":67,"gzipSize":83}],"parsedSize":67,"gzipSize":83},{"id":"U5ju","label":"promise.js","path":"./node_modules/core-js/library/fn/promise.js","statSize":298,"parsedSize":104,"gzipSize":102},{"label":"object","path":"./node_modules/core-js/library/fn/object","statSize":210,"groups":[{"id":"V3tA","label":"assign.js","path":"./node_modules/core-js/library/fn/object/assign.js","statSize":107,"parsedSize":60,"gzipSize":79},{"id":"jFbC","label":"keys.js","path":"./node_modules/core-js/library/fn/object/keys.js","statSize":103,"parsedSize":58,"gzipSize":77}],"parsedSize":118,"gzipSize":93},{"label":"json","path":"./node_modules/core-js/library/fn/json","statSize":246,"groups":[{"id":"qkKv","label":"stringify.js","path":"./node_modules/core-js/library/fn/json/stringify.js","statSize":246,"parsedSize":139,"gzipSize":130}],"parsedSize":139,"gzipSize":130}],"parsedSize":691,"gzipSize":298}],"parsedSize":29953,"gzipSize":10747}],"parsedSize":29953,"gzipSize":10747},{"label":"vant","path":"./node_modules/vant","statSize":593125,"groups":[{"label":"lib","path":"./node_modules/vant/lib","statSize":4058,"groups":[{"label":"popup","path":"./node_modules/vant/lib/popup","statSize":200,"groups":[{"id":"+ed2","label":"index.css","path":"./node_modules/vant/lib/popup/index.css","statSize":41,"parsedSize":15,"gzipSize":35},{"label":"style","path":"./node_modules/vant/lib/popup/style","statSize":159,"groups":[{"id":"tLo2","label":"index.js","path":"./node_modules/vant/lib/popup/style/index.js","statSize":159,"parsedSize":66,"gzipSize":71}],"parsedSize":66,"gzipSize":71}],"parsedSize":81,"gzipSize":77},{"label":"tab","path":"./node_modules/vant/lib/tab","statSize":98,"groups":[{"id":"/SKB","label":"index.css","path":"./node_modules/vant/lib/tab/index.css","statSize":41,"parsedSize":15,"gzipSize":35},{"label":"style","path":"./node_modules/vant/lib/tab/style","statSize":57,"groups":[{"id":"3evy","label":"index.js","path":"./node_modules/vant/lib/tab/style/index.js","statSize":57,"parsedSize":36,"gzipSize":54}],"parsedSize":36,"gzipSize":54}],"parsedSize":51,"gzipSize":60},{"label":"swipe","path":"./node_modules/vant/lib/swipe","statSize":98,"groups":[{"label":"style","path":"./node_modules/vant/lib/swipe/style","statSize":57,"groups":[{"id":"08XL","label":"index.js","path":"./node_modules/vant/lib/swipe/style/index.js","statSize":57,"parsedSize":36,"gzipSize":54}],"parsedSize":36,"gzipSize":54},{"id":"Uf+m","label":"index.css","path":"./node_modules/vant/lib/swipe/index.css","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":51,"gzipSize":61},{"label":"tabs","path":"./node_modules/vant/lib/tabs","statSize":166,"groups":[{"id":"0DIp","label":"index.css","path":"./node_modules/vant/lib/tabs/index.css","statSize":41,"parsedSize":15,"gzipSize":35},{"label":"style","path":"./node_modules/vant/lib/tabs/style","statSize":125,"groups":[{"id":"CCOf","label":"index.js","path":"./node_modules/vant/lib/tabs/style/index.js","statSize":125,"parsedSize":56,"gzipSize":66}],"parsedSize":56,"gzipSize":66}],"parsedSize":71,"gzipSize":72},{"label":"button","path":"./node_modules/vant/lib/button","statSize":200,"groups":[{"id":"0Udj","label":"index.css","path":"./node_modules/vant/lib/button/index.css","statSize":41,"parsedSize":15,"gzipSize":35},{"label":"style","path":"./node_modules/vant/lib/button/style","statSize":159,"groups":[{"id":"FDxC","label":"index.js","path":"./node_modules/vant/lib/button/style/index.js","statSize":159,"parsedSize":66,"gzipSize":71}],"parsedSize":66,"gzipSize":71}],"parsedSize":81,"gzipSize":77},{"label":"picker","path":"./node_modules/vant/lib/picker","statSize":41,"groups":[{"id":"0Y+T","label":"index.css","path":"./node_modules/vant/lib/picker/index.css","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":15,"gzipSize":35},{"label":"tag","path":"./node_modules/vant/lib/tag","statSize":164,"groups":[{"label":"style","path":"./node_modules/vant/lib/tag/style","statSize":123,"groups":[{"id":"2PSJ","label":"index.js","path":"./node_modules/vant/lib/tag/style/index.js","statSize":123,"parsedSize":56,"gzipSize":66}],"parsedSize":56,"gzipSize":66},{"id":"yggM","label":"index.css","path":"./node_modules/vant/lib/tag/index.css","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":71,"gzipSize":72},{"label":"notify","path":"./node_modules/vant/lib/notify","statSize":234,"groups":[{"label":"style","path":"./node_modules/vant/lib/notify/style","statSize":193,"groups":[{"id":"4T1P","label":"index.js","path":"./node_modules/vant/lib/notify/style/index.js","statSize":193,"parsedSize":76,"gzipSize":76}],"parsedSize":76,"gzipSize":76},{"id":"5Zmm","label":"index.css","path":"./node_modules/vant/lib/notify/index.css","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":91,"gzipSize":83},{"label":"overlay","path":"./node_modules/vant/lib/overlay","statSize":98,"groups":[{"label":"style","path":"./node_modules/vant/lib/overlay/style","statSize":57,"groups":[{"id":"6NuU","label":"index.js","path":"./node_modules/vant/lib/overlay/style/index.js","statSize":57,"parsedSize":36,"gzipSize":53}],"parsedSize":36,"gzipSize":53},{"id":"k86u","label":"index.css","path":"./node_modules/vant/lib/overlay/index.css","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":51,"gzipSize":59},{"label":"checkbox","path":"./node_modules/vant/lib/checkbox","statSize":164,"groups":[{"id":"6d7H","label":"index.css","path":"./node_modules/vant/lib/checkbox/index.css","statSize":41,"parsedSize":15,"gzipSize":35},{"label":"style","path":"./node_modules/vant/lib/checkbox/style","statSize":123,"groups":[{"id":"d9cu","label":"index.js","path":"./node_modules/vant/lib/checkbox/style/index.js","statSize":123,"parsedSize":56,"gzipSize":66}],"parsedSize":56,"gzipSize":66}],"parsedSize":71,"gzipSize":72},{"label":"loading","path":"./node_modules/vant/lib/loading","statSize":98,"groups":[{"id":"9NA7","label":"index.css","path":"./node_modules/vant/lib/loading/index.css","statSize":41,"parsedSize":15,"gzipSize":35},{"label":"style","path":"./node_modules/vant/lib/loading/style","statSize":57,"groups":[{"id":"uTM9","label":"index.js","path":"./node_modules/vant/lib/loading/style/index.js","statSize":57,"parsedSize":36,"gzipSize":54}],"parsedSize":36,"gzipSize":54}],"parsedSize":51,"gzipSize":60},{"label":"toast","path":"./node_modules/vant/lib/toast","statSize":270,"groups":[{"id":"9S6h","label":"index.css","path":"./node_modules/vant/lib/toast/index.css","statSize":41,"parsedSize":15,"gzipSize":35},{"label":"style","path":"./node_modules/vant/lib/toast/style","statSize":229,"groups":[{"id":"GKy3","label":"index.js","path":"./node_modules/vant/lib/toast/style/index.js","statSize":229,"parsedSize":86,"gzipSize":80}],"parsedSize":86,"gzipSize":80}],"parsedSize":101,"gzipSize":86},{"label":"icon","path":"./node_modules/vant/lib/icon","statSize":131,"groups":[{"id":"9fCr","label":"index.css","path":"./node_modules/vant/lib/icon/index.css","statSize":41,"parsedSize":15,"gzipSize":35},{"label":"style","path":"./node_modules/vant/lib/icon/style","statSize":90,"groups":[{"id":"jydU","label":"index.js","path":"./node_modules/vant/lib/icon/style/index.js","statSize":90,"parsedSize":46,"gzipSize":60}],"parsedSize":46,"gzipSize":60}],"parsedSize":61,"gzipSize":66},{"label":"goods-action","path":"./node_modules/vant/lib/goods-action","statSize":41,"groups":[{"id":"D+QW","label":"index.css","path":"./node_modules/vant/lib/goods-action/index.css","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":15,"gzipSize":35},{"label":"datetime-picker","path":"./node_modules/vant/lib/datetime-picker","statSize":103,"groups":[{"label":"style","path":"./node_modules/vant/lib/datetime-picker/style","statSize":103,"groups":[{"id":"D0HL","label":"index.js","path":"./node_modules/vant/lib/datetime-picker/style/index.js","statSize":103,"parsedSize":46,"gzipSize":60}],"parsedSize":46,"gzipSize":60}],"parsedSize":46,"gzipSize":60},{"label":"swipe-item","path":"./node_modules/vant/lib/swipe-item","statSize":98,"groups":[{"label":"style","path":"./node_modules/vant/lib/swipe-item/style","statSize":57,"groups":[{"id":"G/J0","label":"index.js","path":"./node_modules/vant/lib/swipe-item/style/index.js","statSize":57,"parsedSize":36,"gzipSize":54}],"parsedSize":36,"gzipSize":54},{"id":"aG1J","label":"index.css","path":"./node_modules/vant/lib/swipe-item/index.css","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":51,"gzipSize":61},{"label":"popover","path":"./node_modules/vant/lib/popover","statSize":234,"groups":[{"id":"KZyY","label":"index.css","path":"./node_modules/vant/lib/popover/index.css","statSize":41,"parsedSize":15,"gzipSize":35},{"label":"style","path":"./node_modules/vant/lib/popover/style","statSize":193,"groups":[{"id":"Nf3R","label":"index.js","path":"./node_modules/vant/lib/popover/style/index.js","statSize":193,"parsedSize":76,"gzipSize":76}],"parsedSize":76,"gzipSize":76}],"parsedSize":91,"gzipSize":82},{"label":"tabbar","path":"./node_modules/vant/lib/tabbar","statSize":98,"groups":[{"id":"M8Zc","label":"index.css","path":"./node_modules/vant/lib/tabbar/index.css","statSize":41,"parsedSize":15,"gzipSize":35},{"label":"style","path":"./node_modules/vant/lib/tabbar/style","statSize":57,"groups":[{"id":"SsyO","label":"index.js","path":"./node_modules/vant/lib/tabbar/style/index.js","statSize":57,"parsedSize":36,"gzipSize":54}],"parsedSize":36,"gzipSize":54}],"parsedSize":51,"gzipSize":60},{"label":"dialog","path":"./node_modules/vant/lib/dialog","statSize":394,"groups":[{"label":"style","path":"./node_modules/vant/lib/dialog/style","statSize":353,"groups":[{"id":"MHRi","label":"index.js","path":"./node_modules/vant/lib/dialog/style/index.js","statSize":353,"parsedSize":116,"gzipSize":99}],"parsedSize":116,"gzipSize":99},{"id":"YAYC","label":"index.css","path":"./node_modules/vant/lib/dialog/index.css","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":131,"gzipSize":105},{"label":"list","path":"./node_modules/vant/lib/list","statSize":134,"groups":[{"label":"style","path":"./node_modules/vant/lib/list/style","statSize":93,"groups":[{"id":"Mcfu","label":"index.js","path":"./node_modules/vant/lib/list/style/index.js","statSize":93,"parsedSize":46,"gzipSize":60}],"parsedSize":46,"gzipSize":60},{"id":"jLuM","label":"index.css","path":"./node_modules/vant/lib/list/index.css","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":61,"gzipSize":66},{"label":"sticky","path":"./node_modules/vant/lib/sticky","statSize":41,"groups":[{"id":"OEKK","label":"index.css","path":"./node_modules/vant/lib/sticky/index.css","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":15,"gzipSize":35},{"label":"skeleton","path":"./node_modules/vant/lib/skeleton","statSize":98,"groups":[{"label":"style","path":"./node_modules/vant/lib/skeleton/style","statSize":57,"groups":[{"id":"TpGR","label":"index.js","path":"./node_modules/vant/lib/skeleton/style/index.js","statSize":57,"parsedSize":36,"gzipSize":54}],"parsedSize":36,"gzipSize":54},{"id":"xI5Q","label":"index.css","path":"./node_modules/vant/lib/skeleton/index.css","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":51,"gzipSize":61},{"label":"style","path":"./node_modules/vant/lib/style","statSize":41,"groups":[{"id":"XqYu","label":"base.css","path":"./node_modules/vant/lib/style/base.css","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":15,"gzipSize":35},{"label":"nav-bar","path":"./node_modules/vant/lib/nav-bar","statSize":164,"groups":[{"id":"Z+4s","label":"index.css","path":"./node_modules/vant/lib/nav-bar/index.css","statSize":41,"parsedSize":15,"gzipSize":35},{"label":"style","path":"./node_modules/vant/lib/nav-bar/style","statSize":123,"groups":[{"id":"tcuZ","label":"index.js","path":"./node_modules/vant/lib/nav-bar/style/index.js","statSize":123,"parsedSize":56,"gzipSize":66}],"parsedSize":56,"gzipSize":66}],"parsedSize":71,"gzipSize":72},{"label":"action-sheet","path":"./node_modules/vant/lib/action-sheet","statSize":270,"groups":[{"label":"style","path":"./node_modules/vant/lib/action-sheet/style","statSize":229,"groups":[{"id":"ZYBX","label":"index.js","path":"./node_modules/vant/lib/action-sheet/style/index.js","statSize":229,"parsedSize":86,"gzipSize":81}],"parsedSize":86,"gzipSize":81},{"id":"r9Lt","label":"index.css","path":"./node_modules/vant/lib/action-sheet/index.css","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":101,"gzipSize":88},{"label":"switch","path":"./node_modules/vant/lib/switch","statSize":134,"groups":[{"label":"style","path":"./node_modules/vant/lib/switch/style","statSize":93,"groups":[{"id":"eNeO","label":"index.js","path":"./node_modules/vant/lib/switch/style/index.js","statSize":93,"parsedSize":46,"gzipSize":60}],"parsedSize":46,"gzipSize":60},{"id":"gMie","label":"index.css","path":"./node_modules/vant/lib/switch/index.css","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":61,"gzipSize":66},{"label":"tabbar-item","path":"./node_modules/vant/lib/tabbar-item","statSize":164,"groups":[{"label":"style","path":"./node_modules/vant/lib/tabbar-item/style","statSize":123,"groups":[{"id":"eoO4","label":"index.js","path":"./node_modules/vant/lib/tabbar-item/style/index.js","statSize":123,"parsedSize":56,"gzipSize":65}],"parsedSize":56,"gzipSize":65},{"id":"h+VL","label":"index.css","path":"./node_modules/vant/lib/tabbar-item/index.css","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":71,"gzipSize":71},{"label":"info","path":"./node_modules/vant/lib/info","statSize":41,"groups":[{"id":"j7dL","label":"index.css","path":"./node_modules/vant/lib/info/index.css","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":15,"gzipSize":35},{"label":"goods-action-button","path":"./node_modules/vant/lib/goods-action-button","statSize":41,"groups":[{"id":"nqem","label":"index.css","path":"./node_modules/vant/lib/goods-action-button/index.css","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":15,"gzipSize":35}],"parsedSize":1708,"gzipSize":272},{"label":"es","path":"./node_modules/vant/es","statSize":571398,"groups":[{"label":"utils","path":"./node_modules/vant/es/utils","statSize":7895,"groups":[{"label":"dom","path":"./node_modules/vant/es/utils/dom","statSize":739,"groups":[{"id":"3X7g","label":"raf.js","path":"./node_modules/vant/es/utils/dom/raf.js","statSize":739,"parsedSize":370,"gzipSize":240}],"parsedSize":370,"gzipSize":240},{"label":"format","path":"./node_modules/vant/es/utils/format","statSize":1574,"groups":[{"id":"4PMK","label":"unit.js","path":"./node_modules/vant/es/utils/format/unit.js","statSize":1221,"parsedSize":636,"gzipSize":329},{"id":"YNA3","label":"string.js","path":"./node_modules/vant/es/utils/format/string.js","statSize":353,"parsedSize":201,"gzipSize":160}],"parsedSize":837,"gzipSize":410},{"label":"validate","path":"./node_modules/vant/es/utils/validate","statSize":225,"groups":[{"id":"mRXp","label":"number.js","path":"./node_modules/vant/es/utils/validate/number.js","statSize":225,"parsedSize":144,"gzipSize":123}],"parsedSize":144,"gzipSize":123},{"id":"o69Z","label":"index.js + 5 modules","path":"./node_modules/vant/es/utils/index.js + 5 modules","statSize":5357,"parsedSize":1856,"gzipSize":855}],"parsedSize":3207,"gzipSize":1357},{"id":"Fd2+","label":"index.js + 164 modules","path":"./node_modules/vant/es/index.js + 164 modules","statSize":559875,"parsedSize":256623,"gzipSize":73722},{"label":"locale","path":"./node_modules/vant/es/locale","statSize":3628,"groups":[{"id":"S06l","label":"index.js + 2 modules","path":"./node_modules/vant/es/locale/index.js + 2 modules","statSize":3628,"parsedSize":1785,"gzipSize":1245}],"parsedSize":1785,"gzipSize":1245}],"parsedSize":261615,"gzipSize":75809},{"label":"node_modules","path":"./node_modules/vant/node_modules","statSize":17669,"groups":[{"label":"vue-lazyload","path":"./node_modules/vant/node_modules/vue-lazyload","statSize":17669,"groups":[{"id":"9gq1","label":"vue-lazyload.js","path":"./node_modules/vant/node_modules/vue-lazyload/vue-lazyload.js","statSize":17669,"parsedSize":17372,"gzipSize":5655}],"parsedSize":17372,"gzipSize":5655}],"parsedSize":17372,"gzipSize":5655}],"parsedSize":280695,"gzipSize":81427},{"label":"babel-runtime","path":"./node_modules/babel-runtime","statSize":3761,"groups":[{"label":"core-js","path":"./node_modules/babel-runtime/core-js","statSize":727,"groups":[{"id":"//Fk","label":"promise.js","path":"./node_modules/babel-runtime/core-js/promise.js","statSize":88,"parsedSize":60,"gzipSize":80},{"id":"5QVw","label":"symbol.js","path":"./node_modules/babel-runtime/core-js/symbol.js","statSize":87,"parsedSize":60,"gzipSize":80},{"label":"symbol","path":"./node_modules/babel-runtime/core-js/symbol","statSize":96,"groups":[{"id":"Zzip","label":"iterator.js","path":"./node_modules/babel-runtime/core-js/symbol/iterator.js","statSize":96,"parsedSize":60,"gzipSize":80}],"parsedSize":60,"gzipSize":80},{"label":"array","path":"./node_modules/babel-runtime/core-js/array","statSize":91,"groups":[{"id":"c/Tr","label":"from.js","path":"./node_modules/babel-runtime/core-js/array/from.js","statSize":91,"parsedSize":60,"gzipSize":80}],"parsedSize":60,"gzipSize":80},{"label":"object","path":"./node_modules/babel-runtime/core-js/object","statSize":186,"groups":[{"id":"fZjL","label":"keys.js","path":"./node_modules/babel-runtime/core-js/object/keys.js","statSize":92,"parsedSize":60,"gzipSize":80},{"id":"woOf","label":"assign.js","path":"./node_modules/babel-runtime/core-js/object/assign.js","statSize":94,"parsedSize":60,"gzipSize":80}],"parsedSize":120,"gzipSize":89},{"id":"ifoU","label":"map.js","path":"./node_modules/babel-runtime/core-js/map.js","statSize":84,"parsedSize":60,"gzipSize":80},{"label":"json","path":"./node_modules/babel-runtime/core-js/json","statSize":95,"groups":[{"id":"mvHQ","label":"stringify.js","path":"./node_modules/babel-runtime/core-js/json/stringify.js","statSize":95,"parsedSize":60,"gzipSize":80}],"parsedSize":60,"gzipSize":80}],"parsedSize":480,"gzipSize":127},{"label":"helpers","path":"./node_modules/babel-runtime/helpers","statSize":2985,"groups":[{"id":"Dd8w","label":"extends.js","path":"./node_modules/babel-runtime/helpers/extends.js","statSize":544,"parsedSize":267,"gzipSize":213},{"id":"Gu7T","label":"toConsumableArray.js","path":"./node_modules/babel-runtime/helpers/toConsumableArray.js","statSize":466,"parsedSize":230,"gzipSize":181},{"id":"exGp","label":"asyncToGenerator.js","path":"./node_modules/babel-runtime/helpers/asyncToGenerator.js","statSize":906,"parsedSize":395,"gzipSize":250},{"id":"pFYg","label":"typeof.js","path":"./node_modules/babel-runtime/helpers/typeof.js","statSize":1069,"parsedSize":593,"gzipSize":237}],"parsedSize":1485,"gzipSize":533},{"label":"regenerator","path":"./node_modules/babel-runtime/regenerator","statSize":49,"groups":[{"id":"Xxa5","label":"index.js","path":"./node_modules/babel-runtime/regenerator/index.js","statSize":49,"parsedSize":36,"gzipSize":56}],"parsedSize":36,"gzipSize":56}],"parsedSize":2001,"gzipSize":613},{"label":"vue","path":"./node_modules/vue","statSize":305174,"groups":[{"label":"dist","path":"./node_modules/vue/dist","statSize":305174,"groups":[{"id":"/5sW","label":"vue.runtime.esm.js","path":"./node_modules/vue/dist/vue.runtime.esm.js","statSize":305174,"parsedSize":77994,"gzipSize":27514}],"parsedSize":77994,"gzipSize":27514}],"parsedSize":77994,"gzipSize":27514},{"label":"element-ui","path":"./node_modules/element-ui","statSize":1651599,"groups":[{"label":"lib","path":"./node_modules/element-ui/lib","statSize":1651599,"groups":[{"label":"theme-chalk","path":"./node_modules/element-ui/lib/theme-chalk","statSize":410,"groups":[{"id":"/bpg","label":"menu-item.css","path":"./node_modules/element-ui/lib/theme-chalk/menu-item.css","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"Df1Z","label":"alert.css","path":"./node_modules/element-ui/lib/theme-chalk/alert.css","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"EYpU","label":"step.css","path":"./node_modules/element-ui/lib/theme-chalk/step.css","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"Hq10","label":"infinite-scroll.css","path":"./node_modules/element-ui/lib/theme-chalk/infinite-scroll.css","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"Mf0D","label":"submenu.css","path":"./node_modules/element-ui/lib/theme-chalk/submenu.css","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"X+ky","label":"input.css","path":"./node_modules/element-ui/lib/theme-chalk/input.css","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"Z1wW","label":"steps.css","path":"./node_modules/element-ui/lib/theme-chalk/steps.css","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"bwiK","label":"menu.css","path":"./node_modules/element-ui/lib/theme-chalk/menu.css","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"jAzQ","label":"upload.css","path":"./node_modules/element-ui/lib/theme-chalk/upload.css","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"wJKS","label":"tag.css","path":"./node_modules/element-ui/lib/theme-chalk/tag.css","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":150,"gzipSize":38},{"label":"utils","path":"./node_modules/element-ui/lib/utils","statSize":121218,"groups":[{"id":"02w1","label":"resize-event.js","path":"./node_modules/element-ui/lib/utils/resize-event.js","statSize":1850,"parsedSize":804,"gzipSize":424},{"id":"2kvA","label":"dom.js","path":"./node_modules/element-ui/lib/utils/dom.js","statSize":6856,"parsedSize":3056,"gzipSize":1293},{"id":"6Twh","label":"scrollbar-width.js","path":"./node_modules/element-ui/lib/utils/scrollbar-width.js","statSize":990,"parsedSize":578,"gzipSize":345},{"label":"popup","path":"./node_modules/element-ui/lib/utils/popup","statSize":11295,"groups":[{"id":"7J9s","label":"index.js","path":"./node_modules/element-ui/lib/utils/popup/index.js","statSize":6025,"parsedSize":3308,"gzipSize":1160},{"id":"OAzY","label":"popup-manager.js","path":"./node_modules/element-ui/lib/utils/popup/popup-manager.js","statSize":5270,"parsedSize":2478,"gzipSize":1049}],"parsedSize":5786,"gzipSize":2016},{"id":"835U","label":"types.js","path":"./node_modules/element-ui/lib/utils/types.js","statSize":1769,"parsedSize":1000,"gzipSize":428},{"id":"AMCD","label":"date-util.js","path":"./node_modules/element-ui/lib/utils/date-util.js","statSize":11697,"parsedSize":5476,"gzipSize":1772},{"id":"DQJY","label":"aria-dialog.js","path":"./node_modules/element-ui/lib/utils/aria-dialog.js","statSize":3312,"parsedSize":1659,"gzipSize":624},{"id":"E/in","label":"shared.js","path":"./node_modules/element-ui/lib/utils/shared.js","statSize":268,"parsedSize":173,"gzipSize":157},{"id":"H8dH","label":"after-leave.js","path":"./node_modules/element-ui/lib/utils/after-leave.js","statSize":1129,"parsedSize":398,"gzipSize":260},{"id":"ISYW","label":"clickoutside.js","path":"./node_modules/element-ui/lib/utils/clickoutside.js","statSize":2314,"parsedSize":1205,"gzipSize":549},{"id":"NMof","label":"popper.js","path":"./node_modules/element-ui/lib/utils/popper.js","statSize":50286,"parsedSize":12654,"gzipSize":4110},{"id":"eNfa","label":"date.js","path":"./node_modules/element-ui/lib/utils/date.js","statSize":11141,"parsedSize":4792,"gzipSize":1886},{"id":"fKx3","label":"vue-popper.js","path":"./node_modules/element-ui/lib/utils/vue-popper.js","statSize":5929,"parsedSize":3111,"gzipSize":1223},{"id":"fUqW","label":"vdom.js","path":"./node_modules/element-ui/lib/utils/vdom.js","statSize":567,"parsedSize":386,"gzipSize":241},{"id":"hyEB","label":"aria-utils.js","path":"./node_modules/element-ui/lib/utils/aria-utils.js","statSize":3049,"parsedSize":1381,"gzipSize":680},{"id":"jmaC","label":"merge.js","path":"./node_modules/element-ui/lib/utils/merge.js","statSize":396,"parsedSize":212,"gzipSize":178},{"id":"ylDJ","label":"util.js","path":"./node_modules/element-ui/lib/utils/util.js","statSize":7319,"parsedSize":3672,"gzipSize":1552},{"id":"zTCi","label":"scroll-into-view.js","path":"./node_modules/element-ui/lib/utils/scroll-into-view.js","statSize":1051,"parsedSize":452,"gzipSize":283}],"parsedSize":46795,"gzipSize":14040},{"id":"0kY3","label":"input-number.js","path":"./node_modules/element-ui/lib/input-number.js","statSize":21663,"parsedSize":8041,"gzipSize":2795},{"label":"mixins","path":"./node_modules/element-ui/lib/mixins","statSize":3539,"groups":[{"id":"1oZe","label":"focus.js","path":"./node_modules/element-ui/lib/mixins/focus.js","statSize":193,"parsedSize":126,"gzipSize":122},{"id":"aW5l","label":"migrating.js","path":"./node_modules/element-ui/lib/mixins/migrating.js","statSize":1997,"parsedSize":156,"gzipSize":143},{"id":"fPll","label":"emitter.js","path":"./node_modules/element-ui/lib/mixins/emitter.js","statSize":1008,"parsedSize":440,"gzipSize":263},{"id":"y+7x","label":"locale.js","path":"./node_modules/element-ui/lib/mixins/locale.js","statSize":341,"parsedSize":190,"gzipSize":170}],"parsedSize":912,"gzipSize":424},{"id":"EKTV","label":"checkbox.js","path":"./node_modules/element-ui/lib/checkbox.js","statSize":18590,"parsedSize":6624,"gzipSize":2308},{"id":"GegP","label":"progress.js","path":"./node_modules/element-ui/lib/progress.js","statSize":16557,"parsedSize":6172,"gzipSize":2290},{"id":"HJMx","label":"input.js","path":"./node_modules/element-ui/lib/input.js","statSize":29157,"parsedSize":11738,"gzipSize":3840},{"id":"RDoK","label":"radio.js","path":"./node_modules/element-ui/lib/radio.js","statSize":13429,"parsedSize":4533,"gzipSize":1821},{"id":"STLj","label":"option.js","path":"./node_modules/element-ui/lib/option.js","statSize":14585,"parsedSize":5335,"gzipSize":2066},{"id":"SXzR","label":"popover.js","path":"./node_modules/element-ui/lib/popover.js","statSize":18227,"parsedSize":7261,"gzipSize":2489},{"label":"locale","path":"./node_modules/element-ui/lib/locale","statSize":5989,"groups":[{"id":"SvnF","label":"format.js","path":"./node_modules/element-ui/lib/locale/format.js","statSize":1436,"parsedSize":640,"gzipSize":389},{"label":"lang","path":"./node_modules/element-ui/lib/locale/lang","statSize":2837,"groups":[{"id":"Vi3T","label":"zh-CN.js","path":"./node_modules/element-ui/lib/locale/lang/zh-CN.js","statSize":2837,"parsedSize":1458,"gzipSize":993}],"parsedSize":1458,"gzipSize":993},{"id":"urW8","label":"index.js","path":"./node_modules/element-ui/lib/locale/index.js","statSize":1716,"parsedSize":769,"gzipSize":435}],"parsedSize":2867,"gzipSize":1649},{"label":"transitions","path":"./node_modules/element-ui/lib/transitions","statSize":2728,"groups":[{"id":"Zcwg","label":"collapse-transition.js","path":"./node_modules/element-ui/lib/transitions/collapse-transition.js","statSize":2728,"parsedSize":1723,"gzipSize":493}],"parsedSize":1723,"gzipSize":493},{"id":"aMwW","label":"tooltip.js","path":"./node_modules/element-ui/lib/tooltip.js","statSize":12070,"parsedSize":5069,"gzipSize":1848},{"id":"e0Bm","label":"select.js","path":"./node_modules/element-ui/lib/select.js","statSize":63373,"parsedSize":27009,"gzipSize":7451},{"id":"fEB+","label":"scrollbar.js","path":"./node_modules/element-ui/lib/scrollbar.js","statSize":12193,"parsedSize":5074,"gzipSize":1862},{"id":"kNJA","label":"cascader-panel.js","path":"./node_modules/element-ui/lib/cascader-panel.js","statSize":45779,"parsedSize":18443,"gzipSize":6043},{"id":"mtrD","label":"button.js","path":"./node_modules/element-ui/lib/button.js","statSize":10287,"parsedSize":3188,"gzipSize":1366},{"id":"orbS","label":"tag.js","path":"./node_modules/element-ui/lib/tag.js","statSize":9375,"parsedSize":2818,"gzipSize":1284},{"id":"s3ue","label":"checkbox-group.js","path":"./node_modules/element-ui/lib/checkbox-group.js","statSize":9676,"parsedSize":2663,"gzipSize":1220},{"id":"zAL+","label":"button-group.js","path":"./node_modules/element-ui/lib/button-group.js","statSize":8594,"parsedSize":2125,"gzipSize":990},{"id":"zL8q","label":"element-ui.common.js","path":"./node_modules/element-ui/lib/element-ui.common.js","statSize":1214160,"parsedSize":513544,"gzipSize":124845}],"parsedSize":682084,"gzipSize":162358}],"parsedSize":682084,"gzipSize":162358},{"label":"crypto-js","path":"./node_modules/crypto-js","statSize":234785,"groups":[{"id":"02Hb","label":"core.js","path":"./node_modules/crypto-js/core.js","statSize":23629,"parsedSize":3911,"gzipSize":1559},{"id":"0Iyz","label":"pad-nopadding.js","path":"./node_modules/crypto-js/pad-nopadding.js","statSize":554,"parsedSize":147,"gzipSize":121},{"id":"0hgu","label":"sha224.js","path":"./node_modules/crypto-js/sha224.js","statSize":1915,"parsedSize":439,"gzipSize":311},{"id":"1J88","label":"x64-core.js","path":"./node_modules/crypto-js/x64-core.js","statSize":8888,"parsedSize":559,"gzipSize":329},{"id":"3NE9","label":"rabbit-legacy.js","path":"./node_modules/crypto-js/rabbit-legacy.js","statSize":6714,"parsedSize":2185,"gzipSize":949},{"id":"3ytT","label":"enc-base64url.js","path":"./node_modules/crypto-js/enc-base64url.js","statSize":4605,"parsedSize":1081,"gzipSize":598},{"id":"4pyl","label":"tripledes.js","path":"./node_modules/crypto-js/tripledes.js","statSize":24878,"parsedSize":10317,"gzipSize":3916},{"id":"5Pol","label":"rc4.js","path":"./node_modules/crypto-js/rc4.js","statSize":3569,"parsedSize":857,"gzipSize":487},{"id":"6qVS","label":"lib-typedarrays.js","path":"./node_modules/crypto-js/lib-typedarrays.js","statSize":2228,"parsedSize":693,"gzipSize":347},{"id":"8lT+","label":"format-hex.js","path":"./node_modules/crypto-js/format-hex.js","statSize":1822,"parsedSize":275,"gzipSize":190},{"id":"96it","label":"pad-ansix923.js","path":"./node_modules/crypto-js/pad-ansix923.js","statSize":1265,"parsedSize":288,"gzipSize":213},{"id":"Av7u","label":"index.js","path":"./node_modules/crypto-js/index.js","statSize":1668,"parsedSize":409,"gzipSize":251},{"id":"E+Sk","label":"pad-zeropadding.js","path":"./node_modules/crypto-js/pad-zeropadding.js","statSize":1110,"parsedSize":312,"gzipSize":221},{"id":"E3Xu","label":"mode-cfb.js","path":"./node_modules/crypto-js/mode-cfb.js","statSize":2115,"parsedSize":578,"gzipSize":300},{"id":"FQmK","label":"aes.js","path":"./node_modules/crypto-js/aes.js","statSize":8649,"parsedSize":2189,"gzipSize":1005},{"id":"Ff/Y","label":"sha1.js","path":"./node_modules/crypto-js/sha1.js","statSize":4063,"parsedSize":1046,"gzipSize":651},{"id":"Gqr1","label":"pad-iso97971.js","path":"./node_modules/crypto-js/pad-iso97971.js","statSize":918,"parsedSize":263,"gzipSize":197},{"id":"HYom","label":"pad-iso10126.js","path":"./node_modules/crypto-js/pad-iso10126.js","statSize":1118,"parsedSize":304,"gzipSize":217},{"id":"NMkZ","label":"blowfish.js","path":"./node_modules/crypto-js/blowfish.js","statSize":21076,"parsedSize":12579,"gzipSize":6593},{"id":"PIk1","label":"hmac.js","path":"./node_modules/crypto-js/hmac.js","statSize":3984,"parsedSize":665,"gzipSize":381},{"id":"QA75","label":"sha512.js","path":"./node_modules/crypto-js/sha512.js","statSize":13469,"parsedSize":4682,"gzipSize":2541},{"id":"Trqf","label":"mode-ecb.js","path":"./node_modules/crypto-js/mode-ecb.js","statSize":893,"parsedSize":308,"gzipSize":186},{"id":"YeRv","label":"mode-ofb.js","path":"./node_modules/crypto-js/mode-ofb.js","statSize":1330,"parsedSize":377,"gzipSize":263},{"id":"bBGs","label":"pbkdf2.js","path":"./node_modules/crypto-js/pbkdf2.js","statSize":4558,"parsedSize":708,"gzipSize":432},{"id":"drMw","label":"enc-utf16.js","path":"./node_modules/crypto-js/enc-utf16.js","statSize":4086,"parsedSize":755,"gzipSize":373},{"id":"fGru","label":"cipher-core.js","path":"./node_modules/crypto-js/cipher-core.js","statSize":29875,"parsedSize":4181,"gzipSize":1557},{"id":"gkUh","label":"rabbit.js","path":"./node_modules/crypto-js/rabbit.js","statSize":6678,"parsedSize":2248,"gzipSize":955},{"id":"gykg","label":"md5.js","path":"./node_modules/crypto-js/md5.js","statSize":9418,"parsedSize":2955,"gzipSize":1205},{"id":"hjGT","label":"ripemd160.js","path":"./node_modules/crypto-js/ripemd160.js","statSize":9387,"parsedSize":3922,"gzipSize":1984},{"id":"kVWZ","label":"mode-ctr.js","path":"./node_modules/crypto-js/mode-ctr.js","statSize":1467,"parsedSize":408,"gzipSize":277},{"id":"mP1F","label":"sha256.js","path":"./node_modules/crypto-js/sha256.js","statSize":5538,"parsedSize":1403,"gzipSize":810},{"id":"s9og","label":"mode-ctr-gladman.js","path":"./node_modules/crypto-js/mode-ctr-gladman.js","statSize":2335,"parsedSize":802,"gzipSize":495},{"id":"uFh6","label":"enc-base64.js","path":"./node_modules/crypto-js/enc-base64.js","statSize":4081,"parsedSize":922,"gzipSize":548},{"id":"v1IJ","label":"sha3.js","path":"./node_modules/crypto-js/sha3.js","statSize":10645,"parsedSize":2278,"gzipSize":1161},{"id":"wj1U","label":"evpkdf.js","path":"./node_modules/crypto-js/evpkdf.js","statSize":3993,"parsedSize":601,"gzipSize":365},{"id":"x067","label":"sha384.js","path":"./node_modules/crypto-js/sha384.js","statSize":2264,"parsedSize":650,"gzipSize":395}],"parsedSize":66297,"gzipSize":25784},{"label":"function-bind","path":"./node_modules/function-bind","statSize":2169,"groups":[{"id":"PiFy","label":"index.js","path":"./node_modules/function-bind/index.js","statSize":126,"parsedSize":82,"gzipSize":97},{"id":"fRHV","label":"implementation.js","path":"./node_modules/function-bind/implementation.js","statSize":2043,"parsedSize":922,"gzipSize":454}],"parsedSize":1004,"gzipSize":475},{"label":"has","path":"./node_modules/has","statSize":129,"groups":[{"label":"src","path":"./node_modules/has/src","statSize":129,"groups":[{"id":"XfW5","label":"index.js","path":"./node_modules/has/src/index.js","statSize":129,"parsedSize":109,"gzipSize":117}],"parsedSize":109,"gzipSize":117}],"parsedSize":109,"gzipSize":117},{"label":"klinecharts","path":"./node_modules/klinecharts","statSize":192709,"groups":[{"label":"dist","path":"./node_modules/klinecharts/dist","statSize":192532,"groups":[{"id":"1zby","label":"klinecharts.min.js","path":"./node_modules/klinecharts/dist/klinecharts.min.js","statSize":192532,"parsedSize":191150,"gzipSize":43451}],"parsedSize":191150,"gzipSize":43451},{"id":"KE1O","label":"index.js","path":"./node_modules/klinecharts/index.js","statSize":177,"parsedSize":49,"gzipSize":69}],"parsedSize":191199,"gzipSize":43475},{"label":"axios","path":"./node_modules/axios","statSize":41116,"groups":[{"label":"lib","path":"./node_modules/axios/lib","statSize":41076,"groups":[{"label":"core","path":"./node_modules/axios/lib/core","statSize":11808,"groups":[{"id":"21It","label":"settle.js","path":"./node_modules/axios/lib/core/settle.js","statSize":686,"parsedSize":199,"gzipSize":163},{"id":"DUeU","label":"mergeConfig.js","path":"./node_modules/axios/lib/core/mergeConfig.js","statSize":2410,"parsedSize":927,"gzipSize":481},{"id":"FtD3","label":"createError.js","path":"./node_modules/axios/lib/core/createError.js","statSize":625,"parsedSize":115,"gzipSize":111},{"id":"Oi+a","label":"buildFullPath.js","path":"./node_modules/axios/lib/core/buildFullPath.js","statSize":695,"parsedSize":107,"gzipSize":113},{"id":"TNV1","label":"transformData.js","path":"./node_modules/axios/lib/core/transformData.js","statSize":550,"parsedSize":116,"gzipSize":109},{"id":"XmWM","label":"Axios.js","path":"./node_modules/axios/lib/core/Axios.js","statSize":2615,"parsedSize":1072,"gzipSize":507},{"id":"fuGk","label":"InterceptorManager.js","path":"./node_modules/axios/lib/core/InterceptorManager.js","statSize":1251,"parsedSize":352,"gzipSize":208},{"id":"t8qj","label":"enhanceError.js","path":"./node_modules/axios/lib/core/enhanceError.js","statSize":1042,"parsedSize":382,"gzipSize":224},{"id":"xLtR","label":"dispatchRequest.js","path":"./node_modules/axios/lib/core/dispatchRequest.js","statSize":1934,"parsedSize":668,"gzipSize":343}],"parsedSize":3938,"gzipSize":1437},{"label":"helpers","path":"./node_modules/axios/lib/helpers","statSize":8924,"groups":[{"id":"5VQ+","label":"normalizeHeaderName.js","path":"./node_modules/axios/lib/helpers/normalizeHeaderName.js","statSize":357,"parsedSize":161,"gzipSize":141},{"id":"DQCr","label":"buildURL.js","path":"./node_modules/axios/lib/helpers/buildURL.js","statSize":1671,"parsedSize":657,"gzipSize":409},{"id":"GHBc","label":"isURLSameOrigin.js","path":"./node_modules/axios/lib/helpers/isURLSameOrigin.js","statSize":2305,"parsedSize":667,"gzipSize":372},{"id":"JP+z","label":"bind.js","path":"./node_modules/axios/lib/helpers/bind.js","statSize":256,"parsedSize":168,"gzipSize":142},{"id":"dIwP","label":"isAbsoluteURL.js","path":"./node_modules/axios/lib/helpers/isAbsoluteURL.js","statSize":563,"parsedSize":98,"gzipSize":107},{"id":"oJlt","label":"parseHeaders.js","path":"./node_modules/axios/lib/helpers/parseHeaders.js","statSize":1393,"parsedSize":554,"gzipSize":350},{"id":"p1b6","label":"cookies.js","path":"./node_modules/axios/lib/helpers/cookies.js","statSize":1435,"parsedSize":587,"gzipSize":359},{"id":"pxG4","label":"spread.js","path":"./node_modules/axios/lib/helpers/spread.js","statSize":564,"parsedSize":95,"gzipSize":92},{"id":"qRfI","label":"combineURLs.js","path":"./node_modules/axios/lib/helpers/combineURLs.js","statSize":380,"parsedSize":111,"gzipSize":107}],"parsedSize":3098,"gzipSize":1317},{"label":"adapters","path":"./node_modules/axios/lib/adapters","statSize":5837,"groups":[{"id":"7GwW","label":"xhr.js","path":"./node_modules/axios/lib/adapters/xhr.js","statSize":5837,"parsedSize":1884,"gzipSize":949}],"parsedSize":1884,"gzipSize":949},{"id":"KCLY","label":"defaults.js","path":"./node_modules/axios/lib/defaults.js","statSize":2542,"parsedSize":1197,"gzipSize":649},{"id":"cGG2","label":"utils.js","path":"./node_modules/axios/lib/utils.js","statSize":8815,"parsedSize":2169,"gzipSize":755},{"label":"cancel","path":"./node_modules/axios/lib/cancel","statSize":1727,"groups":[{"id":"cWxy","label":"CancelToken.js","path":"./node_modules/axios/lib/cancel/CancelToken.js","statSize":1240,"parsedSize":413,"gzipSize":258},{"id":"dVOP","label":"Cancel.js","path":"./node_modules/axios/lib/cancel/Cancel.js","statSize":385,"parsedSize":180,"gzipSize":150},{"id":"pBtG","label":"isCancel.js","path":"./node_modules/axios/lib/cancel/isCancel.js","statSize":102,"parsedSize":79,"gzipSize":92}],"parsedSize":672,"gzipSize":344},{"id":"tIFN","label":"axios.js","path":"./node_modules/axios/lib/axios.js","statSize":1423,"parsedSize":419,"gzipSize":279}],"parsedSize":13377,"gzipSize":4533},{"id":"mtWM","label":"index.js","path":"./node_modules/axios/index.js","statSize":40,"parsedSize":36,"gzipSize":56}],"parsedSize":13413,"gzipSize":4544},{"label":"mint-ui","path":"./node_modules/mint-ui","statSize":241215,"groups":[{"label":"lib","path":"./node_modules/mint-ui/lib","statSize":241215,"groups":[{"label":"toast","path":"./node_modules/mint-ui/lib/toast","statSize":41,"groups":[{"id":"34+y","label":"style.css","path":"./node_modules/mint-ui/lib/toast/style.css","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":15,"gzipSize":35},{"id":"Au9i","label":"mint-ui.common.js","path":"./node_modules/mint-ui/lib/mint-ui.common.js","statSize":241133,"parsedSize":97420,"gzipSize":22682},{"label":"message-box","path":"./node_modules/mint-ui/lib/message-box","statSize":41,"groups":[{"id":"OgVB","label":"style.css","path":"./node_modules/mint-ui/lib/message-box/style.css","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":15,"gzipSize":35}],"parsedSize":97450,"gzipSize":22688}],"parsedSize":97450,"gzipSize":22688},{"label":"@vue","path":"./node_modules/@vue","statSize":1118,"groups":[{"label":"babel-helper-vue-jsx-merge-props","path":"./node_modules/@vue/babel-helper-vue-jsx-merge-props","statSize":1118,"groups":[{"label":"dist","path":"./node_modules/@vue/babel-helper-vue-jsx-merge-props/dist","statSize":1118,"groups":[{"id":"AA6R","label":"helper.js","path":"./node_modules/@vue/babel-helper-vue-jsx-merge-props/dist/helper.js","statSize":1118,"parsedSize":984,"gzipSize":469}],"parsedSize":984,"gzipSize":469}],"parsedSize":984,"gzipSize":469}],"parsedSize":984,"gzipSize":469},{"label":"vue-lazyload","path":"./node_modules/vue-lazyload","statSize":39801,"groups":[{"id":"AXdl","label":"vue-lazyload.esm.js","path":"./node_modules/vue-lazyload/vue-lazyload.esm.js","statSize":39801,"parsedSize":19645,"gzipSize":6198}],"parsedSize":19645,"gzipSize":6198},{"label":"qs","path":"./node_modules/qs","statSize":27248,"groups":[{"label":"lib","path":"./node_modules/qs/lib","statSize":27248,"groups":[{"id":"CwSZ","label":"stringify.js","path":"./node_modules/qs/lib/stringify.js","statSize":10358,"parsedSize":4209,"gzipSize":1749},{"id":"DDCP","label":"parse.js","path":"./node_modules/qs/lib/parse.js","statSize":9380,"parsedSize":3979,"gzipSize":1559},{"id":"XgCd","label":"formats.js","path":"./node_modules/qs/lib/formats.js","statSize":476,"parsedSize":230,"gzipSize":179},{"id":"mw3O","label":"index.js","path":"./node_modules/qs/lib/index.js","statSize":211,"parsedSize":111,"gzipSize":122},{"id":"p8xL","label":"utils.js","path":"./node_modules/qs/lib/utils.js","statSize":6823,"parsedSize":2592,"gzipSize":1234}],"parsedSize":11121,"gzipSize":3999}],"parsedSize":11121,"gzipSize":3999},{"label":"vue-svg-icon","path":"./node_modules/vue-svg-icon","statSize":10379,"groups":[{"label":"lib","path":"./node_modules/vue-svg-icon/lib","statSize":6309,"groups":[{"id":"D1BE","label":"convertShapeToPath.js","path":"./node_modules/vue-svg-icon/lib/convertShapeToPath.js","statSize":3196,"parsedSize":1588,"gzipSize":628},{"id":"i227","label":"parse.js","path":"./node_modules/vue-svg-icon/lib/parse.js","statSize":3113,"parsedSize":906,"gzipSize":411}],"parsedSize":2494,"gzipSize":956},{"id":"PTGa","label":"Icon.vue","path":"./node_modules/vue-svg-icon/Icon.vue","statSize":4070,"parsedSize":1833,"gzipSize":925}],"parsedSize":4327,"gzipSize":1770},{"label":"throttle-debounce","path":"./node_modules/throttle-debounce","statSize":5046,"groups":[{"id":"HzcN","label":"index.js","path":"./node_modules/throttle-debounce/index.js","statSize":140,"parsedSize":78,"gzipSize":95},{"id":"ON3O","label":"debounce.js","path":"./node_modules/throttle-debounce/debounce.js","statSize":1376,"parsedSize":101,"gzipSize":101},{"id":"uY1a","label":"throttle.js","path":"./node_modules/throttle-debounce/throttle.js","statSize":3530,"parsedSize":316,"gzipSize":221}],"parsedSize":495,"gzipSize":286},{"label":"array-find-index","path":"./node_modules/array-find-index","statSize":464,"groups":[{"id":"MII+","label":"index.js","path":"./node_modules/array-find-index/index.js","statSize":464,"parsedSize":309,"gzipSize":218}],"parsedSize":309,"gzipSize":218},{"label":"vuex","path":"./node_modules/vuex","statSize":37246,"groups":[{"label":"dist","path":"./node_modules/vuex/dist","statSize":37246,"groups":[{"id":"NYxO","label":"vuex.esm.js","path":"./node_modules/vuex/dist/vuex.esm.js","statSize":37246,"parsedSize":12304,"gzipSize":3814}],"parsedSize":12304,"gzipSize":3814}],"parsedSize":12304,"gzipSize":3814},{"label":"regenerator-runtime","path":"./node_modules/regenerator-runtime","statSize":25253,"groups":[{"id":"SldL","label":"runtime.js","path":"./node_modules/regenerator-runtime/runtime.js","statSize":24124,"parsedSize":6249,"gzipSize":2326},{"id":"jyFz","label":"runtime-module.js","path":"./node_modules/regenerator-runtime/runtime-module.js","statSize":1129,"parsedSize":339,"gzipSize":199}],"parsedSize":6588,"gzipSize":2412},{"label":"vue-loader","path":"./node_modules/vue-loader","statSize":2896,"groups":[{"label":"lib","path":"./node_modules/vue-loader/lib","statSize":2896,"groups":[{"id":"VU/8","label":"component-normalizer.js","path":"./node_modules/vue-loader/lib/component-normalizer.js","statSize":2896,"parsedSize":777,"gzipSize":429}],"parsedSize":777,"gzipSize":429}],"parsedSize":777,"gzipSize":429},{"label":"clipboard","path":"./node_modules/clipboard","statSize":9160,"groups":[{"label":"dist","path":"./node_modules/clipboard/dist","statSize":9160,"groups":[{"id":"W1rN","label":"clipboard.min.js","path":"./node_modules/clipboard/dist/clipboard.min.js","statSize":9160,"parsedSize":8883,"gzipSize":3092}],"parsedSize":8883,"gzipSize":3092}],"parsedSize":8883,"gzipSize":3092},{"label":"process","path":"./node_modules/process","statSize":5418,"groups":[{"id":"W2nU","label":"browser.js","path":"./node_modules/process/browser.js","statSize":5418,"parsedSize":1674,"gzipSize":699}],"parsedSize":1674,"gzipSize":699},{"label":"side-channel","path":"./node_modules/side-channel","statSize":3391,"groups":[{"id":"Zoe1","label":"index.js","path":"./node_modules/side-channel/index.js","statSize":3391,"parsedSize":1142,"gzipSize":494}],"parsedSize":1142,"gzipSize":494},{"label":"deepmerge","path":"./node_modules/deepmerge","statSize":3309,"groups":[{"label":"dist","path":"./node_modules/deepmerge/dist","statSize":3309,"groups":[{"id":"i3rX","label":"cjs.js","path":"./node_modules/deepmerge/dist/cjs.js","statSize":3309,"parsedSize":1074,"gzipSize":540}],"parsedSize":1074,"gzipSize":540}],"parsedSize":1074,"gzipSize":540},{"label":"async-validator","path":"./node_modules/async-validator","statSize":35833,"groups":[{"label":"es","path":"./node_modules/async-validator/es","statSize":35833,"groups":[{"id":"jwfv","label":"index.js + 24 modules","path":"./node_modules/async-validator/es/index.js + 24 modules","statSize":35833,"parsedSize":12070,"gzipSize":3744}],"parsedSize":12070,"gzipSize":3744}],"parsedSize":12070,"gzipSize":3744},{"label":"raf.js","path":"./node_modules/raf.js","statSize":1288,"groups":[{"id":"migS","label":"raf.js","path":"./node_modules/raf.js/raf.js","statSize":1288,"parsedSize":374,"gzipSize":236}],"parsedSize":374,"gzipSize":236},{"label":"babel-helper-vue-jsx-merge-props","path":"./node_modules/babel-helper-vue-jsx-merge-props","statSize":1282,"groups":[{"id":"nvbp","label":"index.js","path":"./node_modules/babel-helper-vue-jsx-merge-props/index.js","statSize":1282,"parsedSize":577,"gzipSize":321}],"parsedSize":577,"gzipSize":321},{"label":"animate.css","path":"./node_modules/animate.css","statSize":41,"groups":[{"id":"oPmM","label":"animate.css","path":"./node_modules/animate.css/animate.css","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":15,"gzipSize":35},{"label":"dayjs","path":"./node_modules/dayjs","statSize":6670,"groups":[{"id":"oqQY","label":"dayjs.min.js","path":"./node_modules/dayjs/dayjs.min.js","statSize":6670,"parsedSize":6493,"gzipSize":2868}],"parsedSize":6493,"gzipSize":2868},{"label":"lib-flexible","path":"./node_modules/lib-flexible","statSize":3821,"groups":[{"id":"sVYa","label":"flexible.js","path":"./node_modules/lib-flexible/flexible.js","statSize":3821,"parsedSize":1806,"gzipSize":925}],"parsedSize":1806,"gzipSize":925},{"label":"vue-clipboard2","path":"./node_modules/vue-clipboard2","statSize":3207,"groups":[{"id":"wvfG","label":"vue-clipboard.js","path":"./node_modules/vue-clipboard2/vue-clipboard.js","statSize":3207,"parsedSize":1461,"gzipSize":556}],"parsedSize":1461,"gzipSize":556},{"label":"resize-observer-polyfill","path":"./node_modules/resize-observer-polyfill","statSize":33654,"groups":[{"label":"dist","path":"./node_modules/resize-observer-polyfill/dist","statSize":33654,"groups":[{"id":"z+gd","label":"ResizeObserver.es.js","path":"./node_modules/resize-observer-polyfill/dist/ResizeObserver.es.js","statSize":33654,"parsedSize":7733,"gzipSize":2479}],"parsedSize":7733,"gzipSize":2479}],"parsedSize":7733,"gzipSize":2479}],"parsedSize":1564246,"gzipSize":414398},{"label":"src","path":"./src","statSize":607210,"groups":[{"label":"assets","path":"./src/assets","statSize":5282,"groups":[{"label":"foot","path":"./src/assets/foot","statSize":564,"groups":[{"id":"+4oY","label":"3.png","path":"./src/assets/foot/3.png","statSize":70,"parsedSize":57,"gzipSize":77},{"id":"9ud/","label":"44.png","path":"./src/assets/foot/44.png","statSize":71,"parsedSize":58,"gzipSize":78},{"id":"HTxG","label":"1.png","path":"./src/assets/foot/1.png","statSize":70,"parsedSize":57,"gzipSize":77},{"id":"eUXi","label":"4.png","path":"./src/assets/foot/4.png","statSize":70,"parsedSize":57,"gzipSize":77},{"id":"jCt6","label":"33.png","path":"./src/assets/foot/33.png","statSize":71,"parsedSize":58,"gzipSize":78},{"id":"jVi7","label":"22.png","path":"./src/assets/foot/22.png","statSize":71,"parsedSize":58,"gzipSize":78},{"id":"p27Q","label":"11.png","path":"./src/assets/foot/11.png","statSize":71,"parsedSize":58,"gzipSize":78},{"id":"qPcn","label":"2.png","path":"./src/assets/foot/2.png","statSize":70,"parsedSize":57,"gzipSize":77}],"parsedSize":460,"gzipSize":146},{"label":"images","path":"./src/assets/images","statSize":1241,"groups":[{"label":"qiquan26","path":"./src/assets/images/qiquan26","statSize":1241,"groups":[{"id":"+yag","label":"2.png","path":"./src/assets/images/qiquan26/2.png","statSize":70,"parsedSize":57,"gzipSize":77},{"id":"/hCz","label":"bb2.png","path":"./src/assets/images/qiquan26/bb2.png","statSize":72,"parsedSize":59,"gzipSize":79},{"id":"1M8o","label":"icon-real.png","path":"./src/assets/images/qiquan26/icon-real.png","statSize":78,"parsedSize":65,"gzipSize":85},{"id":"7a2y","label":"17336181294013AA00582.png","path":"./src/assets/images/qiquan26/17336181294013AA00582.png","statSize":90,"parsedSize":77,"gzipSize":97},{"id":"DMo1","label":"icon2.png","path":"./src/assets/images/qiquan26/icon2.png","statSize":74,"parsedSize":61,"gzipSize":81},{"id":"FCQB","label":"icon4.png","path":"./src/assets/images/qiquan26/icon4.png","statSize":74,"parsedSize":61,"gzipSize":81},{"id":"GjJs","label":"1.png","path":"./src/assets/images/qiquan26/1.png","statSize":70,"parsedSize":57,"gzipSize":77},{"id":"I2VX","label":"icon-history.png","path":"./src/assets/images/qiquan26/icon-history.png","statSize":81,"parsedSize":68,"gzipSize":88},{"id":"Iu0c","label":"icon-weituo.png","path":"./src/assets/images/qiquan26/icon-weituo.png","statSize":80,"parsedSize":67,"gzipSize":87},{"id":"KbNd","label":"icon3.png","path":"./src/assets/images/qiquan26/icon3.png","statSize":74,"parsedSize":61,"gzipSize":81},{"id":"MKjE","label":"17336181584430A720474.png","path":"./src/assets/images/qiquan26/17336181584430A720474.png","statSize":90,"parsedSize":77,"gzipSize":97},{"id":"TKgR","label":"icon-pay.png","path":"./src/assets/images/qiquan26/icon-pay.png","statSize":77,"parsedSize":64,"gzipSize":84},{"id":"Twhn","label":"icon1.png","path":"./src/assets/images/qiquan26/icon1.png","statSize":74,"parsedSize":61,"gzipSize":81},{"id":"WkmO","label":"6.jpg","path":"./src/assets/images/qiquan26/6.jpg","statSize":70,"parsedSize":57,"gzipSize":77},{"id":"cxo6","label":"icon-bankcard.png","path":"./src/assets/images/qiquan26/icon-bankcard.png","statSize":82,"parsedSize":69,"gzipSize":89},{"id":"qbmI","label":"icon-bankhistory.png","path":"./src/assets/images/qiquan26/icon-bankhistory.png","statSize":85,"parsedSize":72,"gzipSize":92}],"parsedSize":1033,"gzipSize":259}],"parsedSize":1033,"gzipSize":259},{"label":"home","path":"./src/assets/home","statSize":562,"groups":[{"label":"new","path":"./src/assets/home/<USER>","statSize":562,"groups":[{"id":"3Tmf","label":"6.png","path":"./src/assets/home/<USER>/6.png","statSize":70,"parsedSize":57,"gzipSize":77},{"id":"C0+C","label":"13.png","path":"./src/assets/home/<USER>/13.png","statSize":71,"parsedSize":58,"gzipSize":78},{"id":"H5Ho","label":"7.png","path":"./src/assets/home/<USER>/7.png","statSize":70,"parsedSize":57,"gzipSize":77},{"id":"O46z","label":"5.png","path":"./src/assets/home/<USER>/5.png","statSize":70,"parsedSize":57,"gzipSize":77},{"id":"TASz","label":"3.png","path":"./src/assets/home/<USER>/3.png","statSize":70,"parsedSize":57,"gzipSize":77},{"id":"YVan","label":"8.png","path":"./src/assets/home/<USER>/8.png","statSize":70,"parsedSize":57,"gzipSize":77},{"id":"hyEl","label":"9.png","path":"./src/assets/home/<USER>/9.png","statSize":70,"parsedSize":57,"gzipSize":77},{"id":"zIhM","label":"12.png","path":"./src/assets/home/<USER>/12.png","statSize":71,"parsedSize":58,"gzipSize":78}],"parsedSize":458,"gzipSize":146}],"parsedSize":458,"gzipSize":146},{"label":"img","path":"./src/assets/img","statSize":150,"groups":[{"id":"Md1r","label":"eslogo.png","path":"./src/assets/img/eslogo.png","statSize":75,"parsedSize":62,"gzipSize":82},{"id":"ZAYU","label":"close3.png","path":"./src/assets/img/close3.png","statSize":75,"parsedSize":62,"gzipSize":82}],"parsedSize":124,"gzipSize":101},{"label":"ico","path":"./src/assets/ico","statSize":79,"groups":[{"id":"QMf/","label":"fangdajing.png","path":"./src/assets/ico/fangdajing.png","statSize":79,"parsedSize":66,"gzipSize":86}],"parsedSize":66,"gzipSize":86},{"label":"iconfont","path":"./src/assets/iconfont","statSize":2575,"groups":[{"id":"VM3u","label":"iconfont.ttf","path":"./src/assets/iconfont/iconfont.ttf","statSize":2534,"parsedSize":2542,"gzipSize":1180},{"id":"muQq","label":"iconfont.css","path":"./src/assets/iconfont/iconfont.css","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":2557,"gzipSize":1186},{"label":"css","path":"./src/assets/css","statSize":41,"groups":[{"id":"VaBq","label":"style.css","path":"./src/assets/css/style.css","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":15,"gzipSize":35},{"label":"newtemp","path":"./src/assets/newtemp","statSize":70,"groups":[{"id":"u0MG","label":"1.png","path":"./src/assets/newtemp/1.png","statSize":70,"parsedSize":57,"gzipSize":77}],"parsedSize":57,"gzipSize":77}],"parsedSize":4770,"gzipSize":1615},{"label":"svg","path":"./src/svg","statSize":33757,"groups":[{"id":"+BYM","label":"car.svg","path":"./src/svg/car.svg","statSize":1391,"parsedSize":1381,"gzipSize":619},{"id":"+OWZ","label":"tel.svg","path":"./src/svg/tel.svg","statSize":711,"parsedSize":701,"gzipSize":419},{"id":"+cB9","label":"activity.svg","path":"./src/svg/activity.svg","statSize":1548,"parsedSize":1540,"gzipSize":737},{"id":"47L6","label":"down.svg","path":"./src/svg/down.svg","statSize":634,"parsedSize":624,"gzipSize":346},{"id":"5YYo","label":"point-green.svg","path":"./src/svg/point-green.svg","statSize":206,"parsedSize":196,"gzipSize":173},{"id":"CMat","label":"point.svg","path":"./src/svg/point.svg","statSize":206,"parsedSize":196,"gzipSize":173},{"id":"EFzj","label":"code.svg","path":"./src/svg/code.svg","statSize":706,"parsedSize":696,"gzipSize":353},{"id":"GvEB","label":"order.svg","path":"./src/svg/order.svg","statSize":1122,"parsedSize":1112,"gzipSize":530},{"id":"HSYN","label":"safe.svg","path":"./src/svg/safe.svg","statSize":518,"parsedSize":508,"gzipSize":321},{"id":"JhqY","label":"name.svg","path":"./src/svg/name.svg","statSize":802,"parsedSize":792,"gzipSize":401},{"id":"Mg4O","label":"phone.svg","path":"./src/svg/phone.svg","statSize":1011,"parsedSize":1001,"gzipSize":512},{"id":"R8hI","label":"kefu.svg","path":"./src/svg/kefu.svg","statSize":3331,"parsedSize":3321,"gzipSize":1757},{"id":"WV5+","label":"back.svg","path":"./src/svg/back.svg","statSize":587,"parsedSize":579,"gzipSize":279},{"id":"Yg50","label":"beizhu.svg","path":"./src/svg/beizhu.svg","statSize":1868,"parsedSize":1858,"gzipSize":521},{"id":"aPfv","label":"right66.svg","path":"./src/svg/right66.svg","statSize":778,"parsedSize":768,"gzipSize":424},{"id":"axam","label":"time.svg","path":"./src/svg/time.svg","statSize":287,"parsedSize":277,"gzipSize":217},{"id":"chrK","label":"useraddress.svg","path":"./src/svg/useraddress.svg","statSize":691,"parsedSize":681,"gzipSize":419},{"id":"et2M","label":"car-chose.svg","path":"./src/svg/car-chose.svg","statSize":2566,"parsedSize":2556,"gzipSize":1092},{"id":"jWHt","label":"close.svg","path":"./src/svg/close.svg","statSize":674,"parsedSize":666,"gzipSize":358},{"id":"mXyT","label":"xieyi.svg","path":"./src/svg/xieyi.svg","statSize":2803,"parsedSize":2793,"gzipSize":1297},{"id":"niyp","label":"point-red.svg","path":"./src/svg/point-red.svg","statSize":206,"parsedSize":196,"gzipSize":173},{"id":"rd1a","label":"price.svg","path":"./src/svg/price.svg","statSize":320,"parsedSize":310,"gzipSize":219},{"id":"uaMB","label":"shoufei.svg","path":"./src/svg/shoufei.svg","statSize":929,"parsedSize":919,"gzipSize":528},{"id":"wXLU","label":"left.svg","path":"./src/svg/left.svg","statSize":676,"parsedSize":666,"gzipSize":436},{"id":"ySts","label":"right.svg","path":"./src/svg/right.svg","statSize":778,"parsedSize":768,"gzipSize":425},{"id":"ygZh","label":"aboutus.svg","path":"./src/svg/aboutus.svg","statSize":7713,"parsedSize":7703,"gzipSize":2922},{"id":"yjSE","label":"profile.svg","path":"./src/svg/profile.svg","statSize":695,"parsedSize":687,"gzipSize":371}],"parsedSize":33495,"gzipSize":12335},{"id":"0GCx","label":"event.js","path":"./src/event.js","statSize":588,"parsedSize":375,"gzipSize":242},{"label":"components","path":"./src/components","statSize":3090,"groups":[{"label":"icons","path":"./src/components/icons","statSize":164,"groups":[{"id":"2vNp","label":"EyeIcon.vue","path":"./src/components/icons/EyeIcon.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"KE0c","label":"RefreshIcon.vue","path":"./src/components/icons/RefreshIcon.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"b6dz","label":"SearchIcon.vue","path":"./src/components/icons/SearchIcon.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"mTbs","label":"ArrowIcon.vue","path":"./src/components/icons/ArrowIcon.vue","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":60,"gzipSize":38},{"id":"DVXi","label":"foot.vue","path":"./src/components/foot.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"ICZX","label":"stock-tag-info.vue + 2 modules","path":"./src/components/stock-tag-info.vue + 2 modules","statSize":2762,"parsedSize":839,"gzipSize":407},{"id":"L4O4","label":"stock-tag-info.vue","path":"./src/components/stock-tag-info.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"MyyL","label":"elalert.vue","path":"./src/components/elalert.vue","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"SYDW","label":"loginDialog.vue","path":"./src/components/loginDialog.vue","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":959,"gzipSize":414},{"label":"page","path":"./src/page","statSize":164,"groups":[{"label":"list","path":"./src/page/list","statSize":41,"groups":[{"id":"36li","label":"trading-list.vue","path":"./src/page/list/trading-list.vue","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":15,"gzipSize":35},{"label":"newUser","path":"./src/page/newUser","statSize":41,"groups":[{"id":"DgHH","label":"index.vue","path":"./src/page/newUser/index.vue","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":15,"gzipSize":35},{"label":"home","path":"./src/page/home","statSize":82,"groups":[{"id":"kj91","label":"home.vue","path":"./src/page/home/<USER>","statSize":41,"parsedSize":15,"gzipSize":35},{"id":"v3Oq","label":"jiaoyi.vue","path":"./src/page/home/<USER>","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":30,"gzipSize":38}],"parsedSize":60,"gzipSize":38},{"label":"utils","path":"./src/utils","statSize":6154,"groups":[{"id":"7mjt","label":"imgupload.js","path":"./src/utils/imgupload.js","statSize":1644,"parsedSize":731,"gzipSize":473},{"id":"oAV5","label":"utils.js","path":"./src/utils/utils.js","statSize":4510,"parsedSize":2057,"gzipSize":1046}],"parsedSize":2788,"gzipSize":1418},{"label":"store","path":"./src/store","statSize":1125,"groups":[{"id":"IcnI","label":"index.js","path":"./src/store/index.js","statSize":1125,"parsedSize":653,"gzipSize":382}],"parsedSize":653,"gzipSize":382},{"id":"NHnr","label":"main.js + 13 modules","path":"./src/main.js + 13 modules","statSize":202100,"parsedSize":178150,"gzipSize":58371},{"label":"svg .","path":"./src/svg .","statSize":612,"groups":[{"label":"node_modules","path":"./src/svg ./node_modules","statSize":612,"groups":[{"label":"xml-loader ^\\.\\","path":"./src/svg ./node_modules/xml-loader ^\\.\\","statSize":612,"groups":[{"id":"TNPD","label":".*\\.svg$","path":"./src/svg ./node_modules/xml-loader ^\\.\\/.*\\.svg$","statSize":612,"parsedSize":808,"gzipSize":453}],"parsedSize":808,"gzipSize":453}],"parsedSize":808,"gzipSize":453}],"parsedSize":808,"gzipSize":453},{"label":"router","path":"./src/router","statSize":237942,"groups":[{"id":"YaEn","label":"index.js + 25 modules","path":"./src/router/index.js + 25 modules","statSize":237942,"parsedSize":100460,"gzipSize":24065}],"parsedSize":100460,"gzipSize":24065},{"label":"axios","path":"./src/axios","statSize":25276,"groups":[{"id":"c2Ch","label":"api.js","path":"./src/axios/api.js","statSize":14389,"parsedSize":6464,"gzipSize":1159},{"id":"dnAJ","label":"api.url.js","path":"./src/axios/api.url.js","statSize":1346,"parsedSize":90,"gzipSize":100},{"id":"eOoE","label":"index.js + 1 modules","path":"./src/axios/index.js + 1 modules","statSize":9541,"parsedSize":2322,"gzipSize":1086}],"parsedSize":8876,"gzipSize":2228},{"label":"locales","path":"./src/locales","statSize":90315,"groups":[{"id":"qoVm","label":"index.js + 4 modules","path":"./src/locales/index.js + 4 modules","statSize":90315,"parsedSize":42574,"gzipSize":15622}],"parsedSize":42574,"gzipSize":15622},{"label":"config","path":"./src/config","statSize":764,"groups":[{"id":"rAph","label":"api-config.js","path":"./src/config/api-config.js","statSize":764,"parsedSize":345,"gzipSize":240}],"parsedSize":345,"gzipSize":240},{"id":"seia","label":"App.vue","path":"./src/App.vue","statSize":41,"parsedSize":15,"gzipSize":35}],"parsedSize":374328,"gzipSize":116319},{"id":0,"label":"util.inspect (ignored)","path":"./util.inspect (ignored)","statSize":15,"parsedSize":15,"gzipSize":35},{"label":"buildin","path":"./buildin","statSize":509,"groups":[{"id":"DuR2","label":"global.js","path":"./buildin/global.js","statSize":509,"parsedSize":158,"gzipSize":136}],"parsedSize":158,"gzipSize":136}]},{"label":"static/js/21.b54576778107b3e26fc2.*************.y9bzfe.js","statSize":1651447,"parsedSize":766333,"gzipSize":220863,"groups":[{"label":"node_modules","path":"./node_modules","statSize":1651447,"groups":[{"label":"pdfjs-dist","path":"./node_modules/pdfjs-dist","statSize":1651447,"groups":[{"label":"build","path":"./node_modules/pdfjs-dist/build","statSize":1651447,"groups":[{"id":"wk5V","label":"pdf.worker.js","path":"./node_modules/pdfjs-dist/build/pdf.worker.js","statSize":1651447,"parsedSize":766230,"gzipSize":220774}],"parsedSize":766230,"gzipSize":220774}],"parsedSize":766230,"gzipSize":220774}],"parsedSize":766230,"gzipSize":220774}]},{"label":"static/js/manifest.162bd56c19a1cedc3adb.*************.y9bzfe.js","statSize":2012,"parsedSize":2012,"gzipSize":1182,"groups":[]}];
      window.defaultSizes = "parsed";
    </script>
  </body>
</html>
