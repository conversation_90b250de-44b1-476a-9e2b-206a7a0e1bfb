<template>
    <div class="trading-page">
        <div class="trading-layout">
            <div class="asset-overview">
                <div class="asset-card">
                    <div class="asset-title">
                        <div class="asset-label">
                            总市值（元）
                            <span class="eye-toggle" @click="toggleAssetVisibility">
                                <van-icon v-if="showAssets" name="eye-o" class="eye-icon" />
                                <van-icon v-else name="closed-eye" class="eye-icon" />
                            </span>
                        </div>
                        <div class="asset-card-item">
                            <div class="asset-amount">{{ showAssets ? formattedTotalAsset : '***' }}</div>
                        </div>
                    </div>
                    <div class="asset-grid">
                        <div class="asset-item">
                            <div>可用余额</div>
                            <span>{{ showAssets ? formattedAvailableBalance : '***' }}</span>
                        </div>
                        <div class="asset-item">
                            <div>新股申购</div>
                            <span>{{ showAssets ? formattedNewStockAmount : '***' }}</span>
                        </div>
                        <div class="asset-item">
                            <div>可取金额</div>
                            <span>{{ showAssets ? formattedWithdrawFunds : '***' }}</span>
                        </div>
                        <div class="asset-item">
                            <div>总盈亏</div>
                            <span>{{ showAssets ? formattedTotalProfitLoss : '***' }}</span>
                        </div>
                        <div class="asset-item">
                            <div>浮动盈亏</div>
                            <span>{{ showAssets ? formattedFloatingProfitLoss : '***' }}</span>
                        </div>
                        <div class="asset-item">
                            <div>持仓总市值</div>
                            <span>{{ showAssets ? formattedTotalMarketValue : '***' }}</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="trading-tabs-container">
                <div class="trading-tabs">
                    <div :class="`tab-item ${itemIndex == 0 ? 'active' : ''}`" @click="changeItemIndex(0)">
                        <div>
                            <span>当前持仓</span>
                        </div>
                    </div>
                    <div :class="`tab-item ${itemIndex == 1 ? 'active' : ''}`" @click="changeItemIndex(1)">
                        <div>
                            <span>历史持仓</span>
                        </div>
                    </div>
                    <div :class="`tab-item ${itemIndex == 2 ? 'active' : ''}`" @click="changeItemIndex(2)">
                        <div>
                            <span>申购记录</span>
                        </div>
                    </div>
                </div>
                <div class="position-list" v-if="itemIndex == 0">
                    <div class="position-header">
                        <div class="header-col">名称</div>
                        <div class="header-col  data-value-cell">持仓 | 市值</div>
                        <div class="header-col  data-value-cell">现价 | 成本</div>
                        <div class="header-col  data-value-cell">盈亏 | 涨幅</div>
                    </div>
                    <div class="position-body" v-infinite-scroll="loadCurrentPositions"
                        infinite-scroll-disabled="loading" infinite-scroll-distance="10">
                        <div class="position-row" v-for="value in list" :key="value.id" @click="chicangDetail(value)">
                            <div class="position-cell stock-info-cell">
                                <div class="stock-info">
                                    <div class="stock-name">{{ value.stockName }}</div>
                                    <stock-tag-info :stock-code="value.stockGid" />
                                </div>
                            </div>
                            <div class="position-cell data-value-cell">
                                <span>{{ parseNumber(value.buyNum) }}</span>
                                <span>{{ parseNumber(value.buyPrice) }}</span>
                            </div>
                            <div class="position-cell data-value-cell">
                                <span>{{ parseNumber(value.now_price) }}</span>
                                <span>{{ parseNumber(value.buyOrderPrice) }}</span>
                            </div>
                            <div
                                :class="`position-cell data-value-cell ${value.profitAndLossRatio > 0 ? 'profit' : 'loss'}`">
                                <span>{{ parseNumber(value.profitAndLose) }}</span>
                                <span>{{ parseNumber(value.profitAndLossRatio) }}%</span>
                            </div>
                        </div>

                        <!-- 加载更多指示器 -->
                        <div v-if="loading" class="loading-indicator">
                            <div class="loading-spinner"></div>
                            <span>加载中...</span>
                        </div>

                        <!-- 没有更多数据提示 -->
                        <div v-if="finished && list.length > 0" class="no-more-data">
                            没有更多数据了
                        </div>

                        <!-- 空数据提示 -->
                        <div v-if="!loading && list.length === 0" class="empty-data">
                            暂无持仓记录
                        </div>
                    </div>
                </div>
                <div class="history-list" v-if="itemIndex == 1">
                    <div class="position-header">
                        <div class="header-col">股票 | 代码</div>
                        <div class="header-col data-value-cell">本金 | 数量</div>
                        <div class="header-col data-value-cell">买入 | 卖出价</div>
                        <div class="header-col data-value-cell">收益 | 涨幅</div>
                    </div>
                    <div class="position-body" v-infinite-scroll="loadHistoryPositions"
                        infinite-scroll-disabled="loading" infinite-scroll-distance="10">
                        <div v-for="value in list" :key="value.id" class="history-record">
                            <div class="position-row">
                                <div class="position-cell stock-info-cell">
                                    <div class="stock-info">
                                        <div class="stock-name">{{ value.stockName }}</div>
                                        <stock-tag-info :stock-code="value.stockGid" />
                                    </div>
                                </div>
                                <div class="position-cell data-value-cell">
                                    <span>{{ parseNumber(value.buyPrice) }}</span>
                                    <span>{{ parseNumber(value.buyNum) }}</span>
                                </div>
                                <div class="position-cell data-value-cell">
                                    <span>{{ parseNumber(value.buyOrderPrice) }}</span>
                                    <span>{{ parseNumber(value.sellOrderPrice) }}</span>
                                </div>
                                <div
                                    :class="`position-cell data-value-cell ${value.profitAndLossRatio > 0 ? 'profit' : 'loss'}`">
                                    <span>{{ parseNumber(value.profitAndLose) }}</span>
                                    <span>{{ parseNumber(value.profitAndLossRatio) }}%</span>
                                </div>
                            </div>
                            <div class="trade-time">
                                <div>{{ dayjs(value.buyOrderTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
                                <div>{{ dayjs(value.sellOrderTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
                            </div>
                            <div class="detail-button" @click="chicangDetail(value)">查看详情</div>
                        </div>

                        <!-- 加载更多指示器 -->
                        <div v-if="loading" class="loading-indicator">
                            <div class="loading-spinner"></div>
                            <span>加载中...</span>
                        </div>

                        <!-- 没有更多数据提示 -->
                        <div v-if="finished && list.length > 0" class="no-more-data">
                            没有更多数据了
                        </div>

                        <!-- 空数据提示 -->
                        <div v-if="!loading && list.length === 0" class="empty-data">
                            暂无历史记录
                        </div>
                    </div>
                </div>
                <div class="subscription-list" v-if="itemIndex == 2">
                    <div class="subscription-container" v-infinite-scroll="loadSubscriptionRecords"
                        infinite-scroll-disabled="loading" infinite-scroll-distance="10">
                        <div class="subscription-item" v-for="value in list" :key="value.id">
                            <div class="subscription-header">
                                <div style="display: flex; align-items: center;">
                                    <stock-tag-info :stock-code="value.newCode" />
                                    <span style="margin-left: 0.1162rem;">{{ value.newName }} [{{ value.newCode
                                        }}]</span>
                                </div>
                            </div>
                            <div class="subscription-data">
                                <span>￥{{ value.buyPrice }}</span>
                                <span>{{ value.winningRate }}</span>
                                <span>{{ value.orderNumber }}万股</span>
                            </div>
                            <div class="subscription-labels">
                                <span>发行价格</span>
                                <span>中签率</span>
                                <span>发行总数</span>
                            </div>
                        </div>

                        <!-- 加载更多指示器 -->
                        <div v-if="loading" class="loading-indicator">
                            <div class="loading-spinner"></div>
                            <span>加载中...</span>
                        </div>

                        <!-- 没有更多数据提示 -->
                        <div v-if="finished && list.length > 0" class="no-more-data">
                            没有更多数据了
                        </div>

                        <!-- 空数据提示 -->
                        <div v-if="!loading && list.length === 0" class="empty-data">
                            暂无申购记录
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bottom-blank-area"></div>
    </div>
</template>

<script>
import * as api from "@/axios/api";
import { MessageBox, Toast } from "mint-ui";
import StockTagInfo from "@/components/stock-tag-info.vue";

export default {
    components: {
        StockTagInfo
    },
    data() {
        return {
            itemIndex: 0,
            list: [],
            pageNum: 1,
            finished: false,
            loading: false,
            currentRequestId: 0, // 添加请求ID来追踪最新请求
            pageSize: 10,
            showAssets: false // 默认隐藏资产信息
        };
    },

    computed: {
        userInfo() {
            return this.$store.state.userInfo || {};
        },
        formattedTotalAsset() {
            return this.useFormatMoney(this.userInfo.userAmt || 0);
        },
        formattedAvailableBalance() {
            return this.useFormatMoney(this.userInfo.enableAmt || 0);
        },
        formattedNewStockAmount() {
            return this.useFormatMoney(this.userInfo.newStockAmount || 0);
        },
        formattedWithdrawFunds() {
            return this.useFormatMoney(this.userInfo.withdrawFunds || 0);
        },
        formattedTotalProfitLoss() {
            return this.useFormatMoney(this.userInfo.accountAllProfitAndLose || 0);
        },
        formattedFloatingProfitLoss() {
            return this.useFormatMoney(this.userInfo.allProfitAndLose || 0);
        },
        formattedTotalMarketValue() {
            return this.useFormatMoney(this.userInfo.allFreezAmt || 0);
        }
    },

    async mounted() {
        try {
            // 读取用户的资产显示偏好
            const savedPreference = localStorage.getItem('showAssets');
            if (savedPreference !== null) {
                this.showAssets = savedPreference === 'true';
            }

            await this.getUserInfo();
            this.changeItemIndex(0);
        } catch (error) {
            console.error('初始化失败:', error);
            Toast('加载失败，请重试');
        }
    },

    beforeDestroy() {
        // 组件销毁时重置请求ID，确保不处理过时响应
        this.currentRequestId = 0;
    },

    methods: {
        parseNumber(number) {
            return parseFloat(number || 0).toFixed(2);
        },

        async getUserInfo() {
            try {
                const res = await api.getUserInfo();
                if (res.status === 0) {
                    this.$store.state.userInfo = res.data;
                } else {
                    throw new Error(res.msg || '获取用户信息失败');
                }
            } catch (error) {
                console.error('获取用户信息失败:', error);
                Toast(error.message || '获取用户信息失败');
                throw error;
            }
        },

        chicangDetail(value) {
            this.$router.push({
                path: "/chicangDetail?type=dazong&item=" + JSON.stringify(value),
            });
        },

        useFormatMoney(price, useCurrencySymbol = true, currency = "CNY") {
            const options = {
                minimumFractionDigits: 2,
                maximumFractionDigits: 6,
            };
            if (useCurrencySymbol) {
                options.style = "currency";
                options.currency = currency;
            }
            const number = Number(price || 0);
            if (isNaN(number)) {
                console.error("Invalid input: price must be a number--->", price);
                return '0.00';
            }
            return number.toLocaleString(undefined, options);
        },

        changeItemIndex(index) {
            this.resetPagination();
            this.itemIndex = index;
            
            // 生成新的请求ID
            this.currentRequestId = Date.now();

            const actions = {
                0: () => this.loadCurrentPositions(),
                1: () => this.loadHistoryPositions(),
                2: () => this.loadSubscriptionRecords()
            };

            const action = actions[this.itemIndex];
            if (action) {
                action();
            }
        },

        resetPagination() {
            this.list = [];
            this.pageNum = 1;
            this.finished = false;
        },

        // 已移除 CancelToken 相关方法，使用 currentRequestId 处理竞态条件

        // 加载当前持仓
        async loadCurrentPositions() {
            if (this.loading || this.finished) return;
            await this.getOrderList(0);
        },

        // 加载历史持仓
        async loadHistoryPositions() {
            if (this.loading || this.finished) return;
            await this.getOrderList(1);
        },

        // 加载申购记录
        async loadSubscriptionRecords() {
            if (this.loading || this.finished) return;
            await this.getzqjl();
        },

        async getOrderList(state) {
            this.loading = true;
            
            // 记录当前请求ID
            const requestId = this.currentRequestId;

            const params = {
                state: state,
                stockCode: "",
                stockSpell: "",
                pageNum: this.pageNum,
                pageSize: this.pageSize,
            };

            try {
                const res = await api.getOrderList(params);
                
                // 检查是否为最新请求
                if (requestId !== this.currentRequestId) {
                    return; // 忽略过时的响应
                }

                if (res.data.list.length < this.pageSize) {
                    this.finished = true;
                }

                this.list.push(...res.data.list);
                this.pageNum++;
            } catch (error) {
                // 检查是否为最新请求
                if (requestId !== this.currentRequestId) {
                    return; // 忽略过时的响应
                }
                console.error('获取订单列表失败:', error);
                Toast('加载失败，请重试');
            } finally {
                this.loading = false;
            }
        },

        async getUserNewGuList() {
            try {
                const res = await api.getUserNewGuList();
                this.list = res.data;
            } catch (error) {
                console.error('获取新股列表失败:', error);
                Toast('获取新股列表失败');
            }
        },

        async getzqjl() {
            this.loading = true;
            
            // 记录当前请求ID
            const requestId = this.currentRequestId;

            try {
                const res = await api.getUserAllRecord({
                    pageSize: this.pageSize,
                    pageNum: this.pageNum,
                });
                
                // 检查是否为最新请求
                if (requestId !== this.currentRequestId) {
                    return; // 忽略过时的响应
                }

                if (res.data.list.length < this.pageSize) {
                    this.finished = true;
                }

                this.list.push(...res.data.list);
                this.pageNum++;
            } catch (error) {
                // 检查是否为最新请求
                if (requestId !== this.currentRequestId) {
                    return; // 忽略过时的响应
                }
                console.error('获取申购记录失败:', error);
                Toast('加载失败，请重试');
            } finally {
                this.loading = false;
            }
        },

        getrenjiao(id) {
            MessageBox.confirm(this.$t("hj251") + "?", this.$t("hj165"), {
                confirmButtonText: this.$t("hj161"),
                cancelButtonText: this.$t("hj106"),
            })
                .then(async () => {
                    try {
                        const res = await api.submitSubscribe({ id });
                        Toast(res.msg);
                        this.changeItemIndex(1);
                    } catch (error) {
                        console.error('提交申购失败:', error);
                        Toast('操作失败，请重试');
                    }
                })
                .catch(() => { });
        },

        // 切换资产显示/隐藏
        toggleAssetVisibility() {
            this.showAssets = !this.showAssets;

            // 可选：保存用户偏好到本地存储
            localStorage.setItem('showAssets', this.showAssets.toString());

            // 添加小幅度震动反馈（如果支持）
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
        },
    },
};
</script>


<style lang="less" scoped>
.trading-page {}

.trading-layout {}

.asset-overview {
    position: relative;
    padding: 25px 20px;

    &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        height: 240px;
        width: 100%;
        background: url("~@/assets/newtemp/4.png") no-repeat center center;
        background-size: 100% 100%;
        z-index: 0;
        mask: linear-gradient(to bottom, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.1) 80%, rgba(0, 0, 0, 0) 100%);
        -webkit-mask: linear-gradient(to bottom, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.1) 80%, rgba(0, 0, 0, 0) 100%);
    }

    &::after {
        content: "";
        position: absolute;
        top: 0;
        right: 0;
        width: 98px;
        height: 98px;
        background: url("~@/assets/newtemp/5.png") no-repeat center center;
        background-size: 100% 100%;
        z-index: 0;

    }
}

.asset-card {
    z-index: 1;
    position: relative;
}

.asset-title {

    /** 文本1 */
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 19.21px;
    color: rgba(0, 0, 0, 1);
    text-align: left;
    vertical-align: top;

}

.asset-label {
    display: flex;
    align-items: center;
    gap: 8px;

    .eye-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        cursor: pointer;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);

        &:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

            .eye-icon {
                color: rgba(0, 0, 0, 1);
            }
        }

        &:active {
            transform: scale(0.95);
        }

        .eye-icon {
            font-size: 16px;
            transition: all 0.2s ease;
            user-select: none;
            color: rgba(0, 0, 0, 0.8);

            /* 添加动画效果 */
            animation: fadeIn 0.3s ease;
        }
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: rotate(-10deg);
    }

    to {
        opacity: 1;
        transform: rotate(0deg);
    }
}

.asset-card-item {
    /** 文本1 */
    font-size: 32px;
    font-weight: bolder;
    letter-spacing: 0px;
    line-height: 39.07px;
    color: rgba(0, 0, 0, 1);
    text-align: left;
    vertical-align: top;

}

.asset-item {
    &>div {


        /** 文本1 */
        font-size: 11px;
        font-weight: 400;
        letter-spacing: 0px;
        line-height: 15.09px;
        color: rgba(0, 0, 0, 0.4);
        margin-bottom: 2px;
        display: flex;
        justify-content: center;
        align-items: center;

    }

    &>span {

        /** 文本1 */
        font-size: 14px;
        font-weight: bolder;
        letter-spacing: 0px;
        line-height: 17.09px;
        color: rgba(56, 56, 56, 1);
        display: flex;
        justify-content: center;
        align-items: center;

    }
}

.asset-amount {}

.asset-currency {}

.asset-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-top: 27px;
}

.trading-tabs-container {
    background: rgba(255, 255, 255, 1);
    padding: 10px;
    margin-bottom: 88px;
}

.trading-tabs {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    justify-content: center;
    align-items: center;
    margin-bottom: 10px;
}

.trading-tabs .tab-item {
    display: flex;
    justify-content: center;
    align-items: center;



    /** 文本1 */
    font-size: 13px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 18.82px;
    color: rgba(128, 128, 128, 1);


    border-radius: 8px;
    background: rgba(243, 244, 248, 1);
    padding: 5px 0px;


}

.trading-tabs .tab-item.active {
    background: rgba(224, 57, 54, 0.05);
    border: 0.5px solid rgba(252, 61, 55, 1);
    color: rgba(224, 57, 54, 1);

}

.position-list {}

.history-list {}

.position-header {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    justify-content: center;
    align-items: center;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(242, 242, 242, 1);
}

.position-body {}

.header-col {
    font-size: 13px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 17.84px;
    color: rgba(179, 179, 179, 1);

}

.position-row {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    justify-content: center;
    align-items: center;
    margin-bottom: 10px;
    border-bottom: 1px solid rgba(242, 242, 242, 1);
    padding: 10px 0;
}

.position-cell {


    /** 文本1 */
    font-size: 13px;
    font-weight: bold;
    letter-spacing: 0px;
    line-height: 20px;
    color: rgba(29, 36, 51, 1);

}

.stock-info-cell {}

.data-value-cell {
    display: flex;
    justify-content: center;
    align-items: flex-end;
    flex-direction: column;
}

.data-value-cell.profit {
    color: rgba(217, 22, 1, 1);
}

.data-value-cell.loss {
    color: rgba(0, 164, 68, 1);
}

.stock-info {}

.stock-name {
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 19.21px;
    color: rgba(29, 36, 51, 1);

}

.history-record {}

.trade-time {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    margin: 10px 0;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 3px solid #4A90E2;
    position: relative;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

    /* 中间分隔线 */
    &::before {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 1px;
        height: 16px;
        background: #ddd;
    }

    div {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        flex: 1;

        &:first-child {
            align-items: flex-start;

            &::before {
                content: "买入";
                font-size: 10px;
                font-weight: 500;
                color: #00A444;
                background: rgba(0, 164, 68, 0.1);
                padding: 2px 8px;
                border-radius: 4px;
                margin-bottom: 4px;
            }
        }

        &:last-child {
            align-items: flex-end;

            &::before {
                content: "卖出";
                font-size: 10px;
                font-weight: 500;
                color: #D91601;
                background: rgba(217, 22, 1, 0.1);
                padding: 2px 8px;
                border-radius: 4px;
                margin-bottom: 4px;
            }
        }

        /* 时间文本样式 */
        &>* {
            font-size: 12px;
            font-weight: 400;
            line-height: 16px;
            color: #666;
        }
    }
}

.detail-button {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 8px 16px;
    margin: 10px 0;
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
    color: white;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(74, 144, 226, 0.3);

    &:hover {
        background: linear-gradient(135deg, #357ABD 0%, #2E6BA8 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(74, 144, 226, 0.4);
    }

    &:active {
        transform: translateY(0);
        box-shadow: 0 1px 2px rgba(74, 144, 226, 0.3);
    }
}

.subscription-list {
    padding: 10px 0;
}

.subscription-item {
    background: #fff;
    border-radius: 8px;
    margin-bottom: 12px;
    padding: 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border-left: 3px solid #4A90E2;
}

.subscription-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    span {
        font-size: 14px;
        font-weight: 500;
        color: #2c3e50;
        line-height: 20px;
    }
}

.subscription-data {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    margin-bottom: 8px;

    span {
        font-size: 15px;
        font-weight: 600;
        color: #e74c3c;
        text-align: center;
        padding: 8px 0;
        background: rgba(231, 76, 60, 0.05);
        border-radius: 6px;

        &:nth-child(2) {
            color: #27ae60;
            background: rgba(39, 174, 96, 0.05);
        }

        &:nth-child(3) {
            color: #3498db;
            background: rgba(52, 152, 219, 0.05);
        }
    }
}

.subscription-labels {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;

    span {
        font-size: 11px;
        font-weight: 400;
        color: #7f8c8d;
        text-align: center;
        line-height: 16px;
    }
}

/* 加载指示器样式 */
.loading-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #666;
    font-size: 14px;

    .loading-spinner {
        width: 16px;
        height: 16px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #4A90E2;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-right: 8px;
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* 没有更多数据样式 */
.no-more-data {
    text-align: center;
    padding: 20px;
    color: #999;
    font-size: 13px;
    margin-top: 10px;
    position: relative;

    &::before {
        content: "";
        position: absolute;
        top: -1px;
        left: 50%;
        transform: translateX(-50%);
        width: 60px;
        height: 2px;
        background: linear-gradient(90deg, transparent, #ddd, transparent);
    }
}

/* 空数据样式 */
.empty-data {
    text-align: center;
    padding: 60px 20px;
    color: #999;
    font-size: 14px;

    &::before {
        content: "📊";
        display: block;
        font-size: 48px;
        margin-bottom: 16px;
        opacity: 0.3;
    }
}

/* 确保无限滚动容器有最小高度 */
.position-body,
.subscription-container {
    min-height: 200px;
}
</style>