<template>
    <div class="speed-test-container">
        <div class="header">
            <van-nav-bar title="网络测速" left-arrow @click-left="$router.go(-1)">
                <template #left>
                    <van-icon name="arrow-left" size="20" color="#333" />
                </template>
                <template #title>
                    <span class="nav-title">网络连接测速</span>
                </template>
            </van-nav-bar>
        </div>

        <div class="content-wrapper">
            <div class="server-card">
                <div class="card-header">
                    <div class="card-title">
                        <van-icon name="setting-o" />
                        <span>当前服务器</span>
                    </div>
                    <div class="refresh-btn" @click="refreshTest">
                        <van-icon name="replay" />
                        <span>重新测试</span>
                    </div>
                </div>
                <div class="server-info">
                    <div class="server-url">{{ getCurrentServerName() || '正在加载...' }}</div>
                    <div class="connection-status" :class="connectionClass">
                        <div class="status-dot"></div>
                        <span>{{ connectionStatus }}</span>
                    </div>
                </div>
            </div>

            <div class="servers-list-card">
                <div class="card-header">
                    <div class="card-title">
                        <van-icon name="cluster-o" />
                        <span>全部服务器</span>
                    </div>
                    <div class="servers-count">共 {{ speedTestResults.length }} 个</div>
                </div>

                <div class="servers-table">
                    <div class="mobile-server-list">
                        <div v-if="loading" class="loading-state">
                            <van-loading type="spinner" color="#f6020c" />
                            <span>正在测速中...</span>
                        </div>
                        <template v-else>
                            <div class="server-item" v-for="(result, index) in speedTestResults" :key="index" :class="[result.url === currentBaseUrl ? 'current-server' : '',
                            result.isValid ? '' : 'invalid-server']">
                                <div class="server-item-rank">
                                    <span class="rank-number">{{ index + 1 }}</span>
                                </div>
                                <div class="server-item-content">
                                    <div class="server-item-header">
                                        <div class="server-name">{{ getServerName(result.url, index) }}</div>
                                        <div class="server-status"
                                            :class="result.isValid ? 'status-ok' : 'status-fail'">
                                            {{ result.status }}
                                        </div>
                                    </div>
                                    <div class="server-item-details">
                                        <div class="response-time">
                                            <van-icon name="clock-o" />
                                            <span>{{ Math.round(result.responseTime) }}ms</span>
                                        </div>
                                        <div class="server-action">
                                            <van-button type="primary" size="small"
                                                :disabled="result.url === currentBaseUrl || !result.isValid"
                                                @click="setAsDefault(result.url)">
                                                {{ result.url === currentBaseUrl ? '当前使用' : '设为默认' }}
                                            </van-button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>

            <div class="speed-info-card">
                <div class="card-header">
                    <div class="card-title">
                        <van-icon name="info-o" />
                        <span>测速说明</span>
                    </div>
                </div>
                <div class="info-content">
                    <p>为提供更佳的交易体验，系统会自动选择响应速度最快的服务器。您可以通过本页面查看当前网络环境下各服务器的连接速度。</p>
                    <p>如果您遇到交易延迟或断连问题，建议点击"重新测试"按钮刷新测速结果。</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import axios from '@/axios/index';
import { Toast, Dialog } from 'vant';
import Vue from 'vue';
import apiConfig from '@/config/api-config';

export default {
    name: 'SpeedTest',
    data() {
        return {
            loading: false,
            connectionStatus: '已连接',
            connectionClass: 'status-connected',
            currentBaseUrl: '',
            speedTestResults: [] // 本地存储测试结果
        };
    },
    computed: {
        apiSpeedTestResults() {
            return this.$apiSpeedTestResults || [];
        }
    },
    watch: {
        apiSpeedTestResults: {
            handler(newResults) {
                if (newResults && newResults.length > 0) {
                    this.speedTestResults = [...newResults];
                    this.updateConnectionStatus();
                }
            },
            deep: true
        }
    },
    mounted() {
        this.currentBaseUrl = this.$currentBaseUrl || axios.defaults.baseURL || '未知服务器';

        if (this.$apiSpeedTestResults && this.$apiSpeedTestResults.length > 0) {
            this.speedTestResults = [...this.$apiSpeedTestResults];
            this.updateConnectionStatus();
        } else {
            this.refreshTest();
        }
    },
    methods: {
        async refreshTest() {
            this.loading = true;

            try {
                const urls = this.$apiUrls || apiConfig.API_URLS || [];
                if (urls.length === 0) {
                    Toast.fail('无可用服务器地址');
                    this.loading = false;
                    return;
                }

                const results = [];
                let fastestUrl = null;
                let fastestTime = Infinity;

                const promises = urls.map(async (url) => {
                    const startTime = performance.now();
                    try {
                        const response = await axios.get(`${url}${this.$apiHealthCheckPath || apiConfig.HEALTH_CHECK_PATH}`, {
                            timeout: 20000,
                            withCredentials: false
                        });

                        const endTime = performance.now();
                        const responseTime = endTime - startTime;
                        const isValid = response.data && response.data.data === (this.$apiHealthCheckResponse || apiConfig.HEALTH_CHECK_RESPONSE);

                        const result = {
                            url,
                            responseTime,
                            status: isValid ? '可用' : '不可用',
                            isValid
                        };

                        results.push(result);

                        if (isValid && responseTime < fastestTime) {
                            fastestUrl = url;
                            fastestTime = responseTime;
                        }
                    } catch (error) {
                        const endTime = performance.now();

                        results.push({
                            url,
                            responseTime: endTime - startTime,
                            status: '不可用',
                            error: error.message,
                            isValid: false
                        });
                    }
                });

                await Promise.all(promises);

                const sortedResults = results.sort((a, b) => a.responseTime - b.responseTime);
                this.speedTestResults = sortedResults;

                Vue.prototype.$apiSpeedTestResults = sortedResults;
                this.$apiSpeedTestResults = sortedResults;

                this.updateConnectionStatus();

                if (fastestUrl && fastestUrl !== this.currentBaseUrl) {
                    Dialog.confirm({
                        title: '发现更快的服务器',
                        message: `已发现响应更快的服务器：${this.getServerNameByUrl(fastestUrl)}，是否切换？`,
                        confirmButtonText: '切换',
                        cancelButtonText: '保持当前',
                        confirmButtonColor: '#f6020c'
                    }).then(() => {
                        this.setAsDefault(fastestUrl);
                    }).catch(() => {
                        Toast.success('测速完成');
                    });
                } else {
                    Toast.success('测速完成');
                }
            } catch (error) {
                console.error('测速失败:', error);
                Toast.fail('测速失败，请稍后重试');
            } finally {
                this.loading = false;
            }
        },

        updateConnectionStatus() {
            const currentServer = this.speedTestResults.find(item => item.url === this.currentBaseUrl);

            if (!currentServer) {
                this.connectionStatus = '未知状态';
                this.connectionClass = 'status-unknown';
                return;
            }

            if (currentServer.isValid) {
                if (currentServer.responseTime < 200) {
                    this.connectionStatus = '极速连接';
                    this.connectionClass = 'status-excellent';
                } else if (currentServer.responseTime < 500) {
                    this.connectionStatus = '良好连接';
                    this.connectionClass = 'status-good';
                } else if (currentServer.responseTime < 1000) {
                    this.connectionStatus = '正常连接';
                    this.connectionClass = 'status-normal';
                } else {
                    this.connectionStatus = '缓慢连接';
                    this.connectionClass = 'status-slow';
                }
            } else {
                this.connectionStatus = '连接异常';
                this.connectionClass = 'status-error';
            }
        },

        setAsDefault(url) {
            if (!url) return;

            Dialog.confirm({
                title: '设置默认服务器',
                message: `确定要将 ${this.getServerNameByUrl(url)} 设置为默认服务器吗？`,
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                confirmButtonColor: '#f6020c'
            }).then(() => {
                try {
                    axios.defaults.baseURL = url;

                    Vue.prototype.$currentBaseUrl = url;
                    if (typeof window !== 'undefined') {
                        window.$currentBaseUrl = url;
                    }

                    this.currentBaseUrl = url;

                    this.updateConnectionStatus();

                    Toast.success('服务器已切换');
                } catch (error) {
                    console.error('设置服务器失败:', error);
                    Toast.fail('设置失败，请重试');
                }
            }).catch(() => {
                // 用户取消操作
            });
        },

        formatUrl(url) {
            if (!url) return '未知';

            try {
                const parsedUrl = new URL(url);
                return parsedUrl.hostname;
            } catch (e) {
                return url.replace(/^https?:\/\//, '').split('/')[0];
            }
        },

        getCurrentServerName() {
            if (!this.currentBaseUrl) return '未知线路';

            // 从测速结果中找到当前服务器的索引
            const currentIndex = this.speedTestResults.findIndex(item => item.url === this.currentBaseUrl);
            if (currentIndex !== -1) {
                return `线路${currentIndex + 1}`;
            }

            // 如果在测速结果中没找到，尝试从配置中的URL列表找
            const urls = this.$apiUrls || apiConfig.API_URLS || [];
            const configIndex = urls.findIndex(url => url === this.currentBaseUrl);
            if (configIndex !== -1) {
                return `线路${configIndex + 1}`;
            }

            return '未知线路';
        },

        getServerName(url, index) {
            if (!url) return '未知线路';
            return `线路${index + 1}`;
        },

        getServerNameByUrl(url) {
            if (!url) return '未知线路';

            // 从测速结果中找到对应URL的索引
            const index = this.speedTestResults.findIndex(item => item.url === url);
            if (index !== -1) {
                return `线路${index + 1}`;
            }

            // 如果在测速结果中没找到，尝试从配置中的URL列表找
            const urls = this.$apiUrls || apiConfig.API_URLS || [];
            const configIndex = urls.findIndex(configUrl => configUrl === url);
            if (configIndex !== -1) {
                return `线路${configIndex + 1}`;
            }

            return '未知线路';
        }
    }
};
</script>

<style lang="less" scoped>
// 颜色变量
@primary-color: #f6020c;
@primary-light: #ff6a6a;
@background-color: #f5f7fa;
@card-background: #fff;
@text-color: #333;
@secondary-text: #666;
@light-text: #999;
@border-color: #ebedf0;
@success-color: #4caf50;
@warning-color: #ff9800;
@error-color: #f44336;
@info-color: #2196f3;

.speed-test-container {
    min-height: 100vh;
    background-color: @background-color;
    padding-bottom: 20px;

    .header {
        position: sticky;
        top: 0;
        z-index: 100;

        .nav-title {
            font-size: 18px;
            font-weight: 500;
        }
    }

    .content-wrapper {
        padding: 16px 10px;
    }

    // 卡片通用样式
    .server-card,
    .servers-list-card,
    .speed-info-card {
        background-color: @card-background;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        margin-bottom: 16px;
        overflow: hidden;
    }

    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 16px 16px 0 16px;
        border-bottom: 1px solid @border-color;

        .card-title {
            display: flex;
            align-items: center;
            font-size: 16px;
            font-weight: 500;
            color: @text-color;

            .van-icon {
                margin-right: 8px;
                color: @primary-color;
            }
        }

        .refresh-btn {
            display: flex;
            align-items: center;
            color: @primary-color;
            font-size: 14px;

            .van-icon {
                margin-right: 4px;
            }
        }

        .servers-count {
            color: @light-text;
            font-size: 14px;
        }
    }

    // 当前服务器卡片
    .server-card {
        .server-info {
            padding: 16px;

            .server-url {
                font-size: 18px;
                font-weight: 500;
                color: @text-color;
                margin-bottom: 12px;
                word-break: break-all;
            }

            .connection-status {
                display: flex;
                align-items: center;
                font-size: 14px;

                .status-dot {
                    width: 10px;
                    height: 10px;
                    border-radius: 50%;
                    margin-right: 8px;
                }

                &.status-excellent {
                    color: @success-color;

                    .status-dot {
                        background-color: @success-color;
                    }
                }

                &.status-good {
                    color: @info-color;

                    .status-dot {
                        background-color: @info-color;
                    }
                }

                &.status-normal {
                    color: @info-color;

                    .status-dot {
                        background-color: @info-color;
                    }
                }

                &.status-slow {
                    color: @warning-color;

                    .status-dot {
                        background-color: @warning-color;
                    }
                }

                &.status-error {
                    color: @error-color;

                    .status-dot {
                        background-color: @error-color;
                    }
                }

                &.status-unknown {
                    color: @light-text;

                    .status-dot {
                        background-color: @light-text;
                    }
                }

                &.status-connected {
                    color: @success-color;

                    .status-dot {
                        background-color: @success-color;
                    }
                }
            }
        }
    }

    // 服务器列表卡片 - 新的移动友好设计
    .servers-list-card {
        .mobile-server-list {
            padding: 8px 0;

            .loading-state {
                padding: 24px 0;
                display: flex;
                flex-direction: column;
                align-items: center;

                span {
                    margin-top: 12px;
                    color: @secondary-text;
                }
            }

            .server-item {
                display: flex;
                padding: 12px 16px;
                border-bottom: 1px solid @border-color;

                &:last-child {
                    border-bottom: none;
                }

                &.current-server {
                    background-color: rgba(@primary-color, 0.05);
                }

                &.invalid-server {
                    opacity: 0.7;
                }

                .server-item-rank {
                    width: 30px;
                    margin-right: 12px;
                    display: flex;
                    align-items: center;

                    .rank-number {
                        width: 24px;
                        height: 24px;
                        border-radius: 50%;
                        background-color: #f5f5f5;
                        color: @secondary-text;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 14px;
                        font-weight: 500;
                    }
                }

                .server-item-content {
                    flex: 1;

                    .server-item-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 8px;

                        .server-name {
                            font-size: 15px;
                            font-weight: 500;
                            color: @text-color;
                        }

                        .server-status {
                            font-size: 13px;
                            padding: 2px 6px;
                            border-radius: 10px;

                            &.status-ok {
                                color: @success-color;
                                background-color: rgba(@success-color, 0.1);
                            }

                            &.status-fail {
                                color: @error-color;
                                background-color: rgba(@error-color, 0.1);
                            }
                        }
                    }

                    .server-item-details {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;

                        .response-time {
                            display: flex;
                            align-items: center;
                            font-size: 14px;
                            color: @secondary-text;

                            .van-icon {
                                margin-right: 4px;
                                font-size: 14px;
                            }
                        }

                        .server-action {
                            .van-button {
                                height: 32px;
                                font-size: 13px;
                                background: @primary-color;
                                border-color: @primary-color;
                            }

                            .van-button--disabled {
                                opacity: 0.5;
                            }
                        }
                    }
                }
            }
        }
    }

    // 测速说明卡片
    .speed-info-card {
        .info-content {
            padding: 16px;

            p {
                margin: 0 0 12px 0;
                color: @secondary-text;
                font-size: 14px;
                line-height: 1.5;

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }
}
</style>