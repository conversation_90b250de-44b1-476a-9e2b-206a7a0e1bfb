/* auto-build-1753669979406-rdxg2j */
webpackJsonp([18],{Rulh:function(t,s,e){"use strict";Object.defineProperty(s,"__esModule",{value:!0});var i={render:function(){var t=this,s=t.$createElement,e=t._self._c||s;return e("div",{staticClass:"jijincontainer"},[e("div",{staticClass:"headf"},[e("div",[e("h2",[e("span",{staticClass:"hbnh",on:{click:function(s){return t.$router.go(-1)}}},[e("a",{staticClass:"fan"})]),t._v(" 基金\n            ")])])]),t._v(" "),e("div",{staticClass:"hjkl"}),t._v(" "),t._m(0)])},staticRenderFns:[function(){var t=this.$createElement,s=this._self._c||t;return s("div",{staticClass:"boxk"},[s("h6",{staticClass:"tile"},[this._v("优选基金")]),this._v(" "),s("p",{staticStyle:{color:"rgb(153, 153, 153)","margin-top":"10px","font-size":"0.32rem"}},[this._v("优选好基，追求更好回报")])])}]};var a=e("VU/8")(null,i,!1,function(t){e("blOm")},"data-v-4e2517da",null);s.default=a.exports},blOm:function(t,s){}});
/* auto-build-1753669979406-rdxg2j */