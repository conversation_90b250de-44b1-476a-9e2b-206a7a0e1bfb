<template>
    <svg :width="size" :height="size" :style="{ fill: color }" t="1743077219751" class="icon" viewBox="0 0 1024 1024"
        version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7112">
        <path
            d="M941.696 82.176v859.648H82.176V82.176h859.52z m0-82.176H82.176C36.736 0 0 36.736 0 82.176v859.648C0 987.264 36.736 1024 82.176 1024h859.648c45.44 0 82.176-36.736 82.176-82.176V82.176A82.304 82.304 0 0 0 941.696 0z"
            p-id="7113">
        </path>
        <path
            d="M471.424 548.672H248.384a39.872 39.872 0 0 1-39.808-39.936c0-22.08 17.92-40 40.064-40h222.784V244.864a37.312 37.312 0 1 1 74.688 0v528a37.312 37.312 0 0 1-74.688 0V548.672z m303.296-75.84a35.968 35.968 0 1 1 0 71.936l-163.84 3.2a35.968 35.968 0 0 1 0-71.936l163.84-3.2z"
            p-id="7114">
        </path>
    </svg>
</template>

<script>
export default {
    name: 'IconAdd',
    props: {
        size: {
            type: [String, Number],
            default: 64
        },
        color: {
            type: String,
            default: '#333'
        }
    }
}
</script>