<template>
    <div class="block-trading-page">
        <div class="block-trading-header">
            <van-nav-bar title="大宗交易" left-arrow @click-left="$router.go(-1)" fixed right-text="记录"
                @click-right="$router.push({ path: '/dazong?type=2' })"></van-nav-bar>
        </div>
        <div class="block-trading-content">
            <div class="trading-table-header">
                <div class="header-cell">股票名称</div>
                <div class="header-cell">最新价格</div>
                <div class="header-cell">操作</div>
            </div>
            <div class="trading-table-body">
                <van-list v-model="loading" :finished="finished" :immediate-check="false" :finished-text="$t('hj43')"
                    @load="getOrderList(1)">
                    <div v-for="value in list" class="trading-row" :key="value.id">
                        <div class="stock-info-cell">
                            <div class="stock-details">
                                <div class="stock-name">{{ value.stockName }}</div>
                                <div class="stock-code-container">
                                    <stock-tag-info :stock-code="value.stockGid" />
                                </div>
                            </div>
                        </div>
                        <div class="price-cell">{{ parseNumber(value.price) }}</div>
                        <div class="action-cell">
                            <div class="buy-button" @click="getdetail(value)">买入</div>
                        </div>
                    </div>
                </van-list>
            </div>
        </div>

        <van-popup v-model="show" round position="bottom">
            <div class="trading-modal">
                <div class="modal-header">大宗交易<span @click="show = false"></span></div>
                <div class="position-selector-container">
                    <div class="position-label">仓位</div>
                    <div class="position-options">
                        <van-tag v-if="itemIndex == 0" size="medium" type="danger" @click="calculatePortion(1 / 4, 0)"
                            class="position-tag">1/4</van-tag>
                        <van-tag v-else size="medium" plain type="primary" @click="calculatePortion(1 / 4, 0)"
                            class="position-tag">1/4</van-tag>

                        <van-tag v-if="itemIndex == 1" size="medium" type="danger" @click="calculatePortion(1 / 3, 1)"
                            class="position-tag position-tag--middle">1/3</van-tag>
                        <van-tag v-else size="medium" plain type="primary" @click="calculatePortion(1 / 3, 1)"
                            class="position-tag position-tag--middle">1/3</van-tag>

                        <van-tag v-if="itemIndex == 2" size="medium" type="danger" @click="calculatePortion(1 / 2, 2)"
                            class="position-tag">1/2</van-tag>
                        <van-tag v-else size="medium" plain type="primary" @click="calculatePortion(1 / 2, 2)"
                            class="position-tag">1/2</van-tag>

                        <van-tag v-if="itemIndex == 3" size="medium" type="danger" @click="calculatePortion(1, 3)"
                            class="position-tag position-tag--last">全仓</van-tag>
                        <van-tag v-else size="medium" plain type="primary" @click="calculatePortion(1, 3)"
                            class="position-tag position-tag--last">全仓</van-tag>
                    </div>
                </div>
                <h5 class="price-title">买入价格</h5>
                <h6 class="price-display">{{ currentItem.price || '' }}</h6>
                <div class="quantity-input-container quantity-input--active">
                    <input placeholder="请输入数量" type="number" class="quantity-input" v-model="num"
                        @input="handleNumberInput">
                    <span class="input-unit">手</span>
                </div>
                <p class="amount-info">
                    <span>购买金额</span>
                    <span class="amount-value">{{ purchaseAmount }}</span>
                </p>
                <p class="amount-info">
                    <span>可用资金</span>
                    <span class="amount-value">{{ formattedEnableAmount }}</span>
                </p>
                <div class="buy-confirm-button" @click="getxiadan">买入</div>
            </div>
        </van-popup>
    </div>
</template>

<script>
import * as api from "@/axios/api";
import BigNumber from "bignumber.js";
import StockTagInfo from "@/components/stock-tag-info.vue";

export default {
    name: "BlockTradingPage",
    components: {
        StockTagInfo
    },
    data() {
        return {
            loading: false,
            finished: false,
            list: [],
            userinfo: {},
            show: false,
            itemIndex: -1,
            currentItem: {},
            num: "",
            settingDetail: {},
            bigNumbuy: 0,
            buyNum: 0,
        };
    },
    computed: {
        purchaseAmount() {
            if (!this.currentItem.price || !this.num) return '0.00';
            return (this.currentItem.price * this.num * 100).toFixed(2);
        },
        formattedEnableAmount() {
            return this.userinfo.enableAmt || '0.00';
        }
    },
    async mounted() {
        try {
            await this.initializeData();
        } catch (error) {
            console.error('页面初始化失败:', error);
            this.$toast('页面加载失败，请重试');
        }
    },
    methods: {
        async initializeData() {
            await this.getSetting();
            await Promise.all([
                this.stockgetDzList(),
                this.getUserInfo()
            ]);
        },
        parseNumber(number) {
            return parseFloat(number).toFixed(2);
        },
        async getSetting() {
            try {
                const data = await api.getSetting({});
                this.settingDetail = data.data;
                this.buyNum = this.settingDetail.buyMinNum / 100;
            } catch (error) {
                console.error('获取设置失败:', error);
                throw error;
            }
        },
        async stockgetDzList() {
            this.loading = true;
            try {
                const res = await api.stockgetDzList();
                this.list = res.data;
                this.finished = true;
            } catch (error) {
                console.error('获取大宗交易列表失败:', error);
                this.$toast('获取数据失败，请重试');
            } finally {
                this.loading = false;
            }
        },
        async getUserInfo() {
            try {
                const data = await api.getUserInfo();
                if (data.status === 0) {
                    this.userinfo = data.data;
                } else {
                    this.$toast(data.msg || '获取用户信息失败');
                }
            } catch (error) {
                console.error('获取用户信息失败:', error);
                this.$toast('获取用户信息失败');
            }
        },
        getdetail(item) {
            try {
                this.calculateMaxBuyAmount(item);
                item.price = this.parseNumber(item.price);
                this.currentItem = item;
                this.show = true;
                this.resetForm();
            } catch (error) {
                console.error('获取股票详情失败:', error);
                this.$toast('获取详情失败');
            }
        },
        calculateMaxBuyAmount(item) {
            const enableAmt = new BigNumber(this.userinfo.enableAmt || 0);
            const nowPrice = new BigNumber(this.parseNumber(item.price));
            const buyFee = new BigNumber(this.settingDetail.buyFee || 0);

            if (nowPrice.isZero()) {
                this.bigNumbuy = new BigNumber(0);
                return;
            }

            const singleStockCost = nowPrice
                .multipliedBy(100)
                .plus(nowPrice.multipliedBy(100).multipliedBy(buyFee));

            const buyTotal = enableAmt.dividedBy(singleStockCost);
            this.bigNumbuy = buyTotal.integerValue(BigNumber.ROUND_FLOOR);
        },
        resetForm() {
            this.num = "";
            this.itemIndex = -1;
        },
        handleNumberInput(event) {
            const value = event.target.value.replace(/^(0+)|[^\d]+/g, '');
            this.num = value;
        },
        calculatePortion(fraction, index) {
            if (!this.bigNumbuy || this.bigNumbuy.isZero()) {
                this.$toast('无法计算可购买数量');
                return;
            }

            const portion = this.bigNumbuy
                .multipliedBy(fraction)
                .integerValue(BigNumber.ROUND_FLOOR);

            this.num = portion.toString();
            this.itemIndex = index;
        },
        async getxiadan() {
            if (!this.validateOrder()) return;

            try {
                const params = {
                    stockCode: this.currentItem.stockCode,
                    password: "",
                    num: this.num * 100,
                };

                const res = await api.buyStockDz(params);

                if (res.status === 0) {
                    this.$toast("买入成功");
                    await this.getUserInfo();
                } else {
                    this.$toast(res.msg || '买入失败');
                }
            } catch (error) {
                console.error('买入失败:', error);
                this.$toast('买入失败，请重试');
            } finally {
                this.show = false;
                this.resetForm();
            }
        },
        validateOrder() {
            if (!this.num || Number(this.num) <= 0) {
                this.$toast("请输入有效数量");
                return false;
            }

            const maxNum = this.bigNumbuy ? this.bigNumbuy.toNumber() : 0;
            if (Number(this.num) > maxNum) {
                this.$toast(`最大只能购买${maxNum}手`);
                return false;
            }

            return true;
        },
    },
};
</script>

<style lang="less" scoped>
.block-trading-page {
    font-size: 0.3256rem;
    padding: 0;
    background: #F6F6F6;
    min-height: 100vh;

    .block-trading-header {
        width: 100%;
        height: 1.07rem;
    }

    .block-trading-content {
        padding: 0.2326rem;

        .trading-table-header {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            background: linear-gradient(135deg, #EA001B 0%, #C8001A 100%);
            border-radius: 0.2326rem 0.2326rem 0 0;
            box-shadow: 0 2px 8px rgba(234, 0, 27, 0.3);

            .header-cell {
                flex: 1;
                text-align: center;
                line-height: 0.9302rem;
                color: #fff;
                font-weight: 500;
                font-size: 0.3488rem;
            }
        }

        .trading-table-body {
            background: #fff;
            border-radius: 0 0 0.2326rem 0.2326rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            overflow: hidden;

            .trading-row {
                display: grid;
                grid-template-columns: 1fr 1fr 1fr;
                border-bottom: 1px solid rgba(240, 242, 247, 1);
                padding: 0.4651rem 0;
                transition: all 0.3s ease;
                position: relative;

                &:hover {
                    background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
                    transform: translateY(-1px);
                }

                &:last-child {
                    border-bottom: none;
                }

                .stock-info-cell {
                    display: flex;
                    justify-content: left;

                    .stock-details {
                        padding-left: 0.4651rem;

                        .stock-name {
                            font-size: 0.3721rem;
                            font-weight: 500;
                            color: #2c3e50;
                            margin-bottom: 0.1162rem;
                        }

                        .stock-code-container {
                            margin-top: 0.1162rem;
                            font-size: 0.3255rem;
                            display: flex;
                            align-items: center;
                            gap: 0.1162rem;
                        }
                    }
                }

                .price-cell {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    line-height: 0.4651rem;
                    font-size: 0.3721rem;
                    font-weight: 600;
                    color: #EA001B;
                }

                .action-cell {
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    .buy-button {
                        background: linear-gradient(135deg, #EA001B 0%, #C8001A 100%);
                        height: 0.7907rem;
                        color: #fff;
                        text-align: center;
                        line-height: 0.7907rem;
                        border-radius: 0.3953rem;
                        width: 1.6279rem;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        font-weight: 500;
                        font-size: 0.3256rem;
                        box-shadow: 0 3px 6px rgba(234, 0, 27, 0.3);
                        border: none;

                        &:hover {
                            background: linear-gradient(135deg, #C8001A 0%, #A50015 100%);
                            transform: translateY(-2px);
                            box-shadow: 0 6px 12px rgba(234, 0, 27, 0.4);
                        }

                        &:active {
                            transform: translateY(0);
                        }
                    }
                }
            }

            .trading-time {
                display: flex;
                justify-content: space-between;
                padding: 0 0.3488rem;
                color: rgba(125, 125, 125, 1);
            }
        }
    }
}

.trading-modal {
    background: #fff;
    border-radius: 0.4651rem 0.4651rem 0 0;
    padding-bottom: 0.6977rem;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
    position: relative;

    &::before {
        content: '';
        position: absolute;
        top: 0.2326rem;
        left: 50%;
        transform: translateX(-50%);
        width: 0.9302rem;
        height: 0.0930rem;
        background: #d1d5db;
        border-radius: 0.0465rem;
    }

    .modal-header {
        height: 1.3953rem;
        border-bottom: 1px solid rgba(240, 242, 247, 1);
        text-align: center;
        line-height: 1.3953rem;
        color: #2c3e50;
        font-size: 0.4651rem;
        font-weight: 600;
        position: relative;
        background: #fff;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        padding-left: 20px;
        span {
            position: absolute;
            width: 0.6977rem;
            height: 0.6977rem;
            right: 0.4651rem;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            border-radius: 50%;
            background: #EA001B;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;

            &::before {
                content: '×';
                color: #fff;
                font-size: 0.4651rem;
                font-weight: bold;
            }

            &:hover {
                background: #C8001A;
                transform: translateY(-50%) scale(1.1);
            }
        }
    }

    .position-selector-container {
        width: 9.48rem;
        margin: 0 auto;
        padding: 0.4651rem;
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        border-radius: 0.2326rem;
        margin-top: 0.4651rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

        .position-label {
            font-size: 0.4186rem;
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 0.4651rem;
            text-align: center;
        }

        .position-options {
            display: flex;
            gap: 0.2326rem;

            .position-tag {
                flex: 1;
                font-size: 0.3488rem;
                font-weight: 500;
                justify-content: center;
                height: 0.9302rem;
                line-height: 0.9302rem;
                text-align: center;
                border-radius: 0.2326rem;
                cursor: pointer;
                transition: all 0.3s ease;
                border: 2px solid transparent;

                &.position-tag--middle {
                    margin: 0;
                }

                &.position-tag--last {
                    margin-left: 0;
                }
            }
        }
    }

    .price-title {
        color: #2c3e50;
        font-size: 0.4186rem;
        font-weight: 600;
        width: 9.48rem;
        margin: 0 auto;
        margin-top: 0.6977rem;
        text-align: center;
    }

    .price-display {
        color: #EA001B;
        font-size: 0.5581rem;
        width: 9.48rem;
        margin: 0 auto;
        margin-top: 0.2326rem;
        font-weight: 700;
        text-align: center;
        text-shadow: 0 2px 4px rgba(234, 0, 27, 0.3);
    }

    .quantity-input-container {
        width: 9.21rem;
        height: 1.1627rem;
        border: 2px solid #e9ecef;
        border-radius: 0.2326rem;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 0.6977rem;
        background: #fff;
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;

        &:focus-within {
            border-color: #EA001B;
            box-shadow: 0 0 0 3px rgba(234, 0, 27, 0.1);
        }

        .quantity-input {
            height: 100%;
            width: 7rem;
            margin-left: 0.4651rem;
            background: transparent;
            font-size: 0.4186rem;
            font-weight: 500;
            color: #2c3e50;
            border: none;
            outline: none;

            &::placeholder {
                color: #95a5a6;
                font-weight: 400;
            }
        }

        .input-unit {
            height: 0.7907rem;
            border-left: 2px solid #e9ecef;
            width: 1.3953rem;
            text-align: center;
            font-size: 0.3721rem;
            font-weight: 500;
            color: #7f8c8d;
            line-height: 0.7907rem;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
        }
    }

    .quantity-input--active {
        border: 2px solid #EA001B;
        margin-top: 0.4651rem;
    }

    .amount-info {
        width: 8.94rem;
        margin: 0 auto;
        margin-top: 0.4651rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.3488rem 0.4651rem;
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        border-radius: 0.2326rem;

        span {
            color: #7f8c8d;
            font-size: 0.3488rem;
            font-weight: 500;
        }

        .amount-value {
            color: #EA001B;
            font-weight: 700;
            font-size: 0.3721rem;
        }
    }

    .buy-confirm-button {
        width: 9.21rem;
        height: 1.2791rem;
        background: linear-gradient(135deg, #EA001B 0%, #C8001A 100%);
        border-radius: 0.2326rem;
        margin: 0 auto;
        margin-top: 0.9302rem;
        text-align: center;
        line-height: 1.2791rem;
        color: #fff;
        font-size: 0.4186rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(234, 0, 27, 0.4);
        border: none;
        text-transform: uppercase;
        letter-spacing: 0.0465rem;

        &:hover {
            background: linear-gradient(135deg, #C8001A 0%, #A50015 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(234, 0, 27, 0.6);
        }

        &:active {
            transform: translateY(0);
        }

        &:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
    }
}

/* 全局van-tag样式覆盖 */
:deep(.van-tag) {
    border: 2px solid #e9ecef !important;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
    color: #7f8c8d !important;

    &.van-tag--danger {
        background: linear-gradient(135deg, #EA001B 0%, #C8001A 100%) !important;
        color: #fff !important;
        border-color: #EA001B !important;
        box-shadow: 0 2px 8px rgba(234, 0, 27, 0.3) !important;
    }

    &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
    }
}

/* loading和finished状态样式 */
:deep(.van-list__finished-text) {
    color: #95a5a6;
    font-size: 0.3256rem;
    padding: 0.4651rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    margin: 0.2326rem;
    border-radius: 0.2326rem;
    text-align: center;
}

:deep(.van-loading) {
    padding: 0.4651rem 0;
}
</style>