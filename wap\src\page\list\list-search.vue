<template>
    <div class="search-container">
        <!-- 搜索头部 -->
        <div class="search-header">
            <div class="search-box">
                <div class="search-icon">
                    <svg class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M653.54 707.45C498.1 833.17 270.18 809.07 144.46 653.63 18.84 498.32 42.96 270.17 198.28 144.55 353.72 18.84 581.64 42.93 707.36 198.37c107.37 132.76 107.37 322.5 0 455.26l241.48 241.35c14.87 14.86 14.88 38.96 0.03 53.84-14.87 14.89-38.99 14.9-53.87 0.02L653.54 707.45zM425.8 139.62c-157.93 0-285.96 128.03-285.96 285.96 0 157.93 128.03 285.96 285.96 285.96 157.84 0 285.84-127.89 285.96-285.74-0.08-157.92-128.02-285.97-285.96-286.18z">
                        </path>
                    </svg>
                </div>
                <input class="search-input" placeholder="请输入股票代码/名称" v-model="keyWords" @input="handleSearch"
                    maxlength="20">
                <div class="clear-btn" v-if="keyWords" @click="clearSearch">
                    <svg viewBox="0 0 1024 1024">
                        <path
                            d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m165.4 618.2l-66-0.3L512 563.4l-99.3 118.4-66.1 0.3c-4.4 0-8-3.5-8-8 0-1.9 0.7-3.7 1.9-5.2l130.1-155L340.5 359a8.32 8.32 0 0 1 1.9-11.3c1.5-1.2 3.3-1.9 5.2-1.9l66.1 0.3L512 464.6l99.3-118.4 66-0.3c4.4 0 8 3.5 8 8 0 1.9-0.7 3.7-1.9 5.2L553.5 514l130.1 155c1.2 1.5 1.9 3.3 1.9 5.2 0 4.4-3.6 8-8.1 8z">
                        </path>
                    </svg>
                </div>
            </div>
            <div class="cancel-btn" @click="$router.go(-1)">取消</div>
        </div>

        <!-- 搜索结果 -->
        <div class="search-results" v-if="keyWords">
            <!-- 表头 -->
            <div class="table-header">
                <div class="col-name">股票名称</div>
                <div class="col-price">最新价</div>
                <div class="col-change">涨跌幅</div>
            </div>

            <!-- 股票列表 -->
            <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad" offset="500"
                :immediate-check="false" class="stock-list">
                <div v-for="(item, index) in listArr" :key="index" class="stock-item" @click="goDetail(item)">
                    <div class="stock-info">
                        <div class="stock-name">{{ item.name }}</div>
                        <StockTagInfo :stockCode="item.gid" />
                    </div>
                    <div class="stock-price" :class="getPriceClass(item.hcrate)">
                        {{ item.nowPrice }}
                    </div>
                    <div class="stock-change" :class="getPriceClass(item.hcrate)">
                        <span class="change-percent">{{ item.hcrate }}%</span>
                    </div>
                </div>
            </van-list>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" v-if="!keyWords">
            <div class="empty-icon">🔍</div>
            <div class="empty-text">请输入股票代码或名称进行搜索</div>
        </div>
    </div>
</template>

<script>
import { Toast } from "vant";
import { MessageBox } from "mint-ui";
import handleDt from "@/utils/deTh";
import * as api from "@/axios/api";
import StockTagInfo from "@/components/stock-tag-info.vue";
export default {
    components: {
        StockTagInfo,
    },
    props: {},
    data() {
        return {
            keyWords: "",
            pageNum: 1,
            loading: false,
            finished: false,
            listArr: [],
        };
    },
    mounted() { },
    watch: {
        keyWords(val) {
            if (val == "") {
                this.listArr = [];
            }
        },
    },
    methods: {
        //进入详情
        goDetail(item) {
            //   if (this.userData.length == 0) {

            //     return;
            //   }

            var codes = "";
            var names = "";
            var stock_type = "";
            var soks = "";
            var if_zhishu = "0";
            var if_us = "";
            codes = item.code;
            names = item.name;
            stock_type = item.gid.substring(0, 2);
            soks = this.filterSH(item.gid);
            if_zhishu = "0";
            if_us = item.stock_type == "us" ? "1" : "";
            this.$router.push({
                path: "/kline",
                query: {
                    name: names,
                    stockplate: item.stock_plate,
                    code: codes,
                    type: stock_type,
                    sok: soks,
                    if_us: if_us,
                    usType: item.type,
                    if_zhishu: if_zhishu,
                },
            });
        },
        filterSH(val) {
            if (val.indexOf("sh") >= 0) {
                return 1;
            } else if (val.indexOf("bj") >= 0 || val.indexOf("sz") >= 0) {
                return 0;
            }
        },
        onLoad() {
            // 异步更新数据
            // setTimeout 仅做示例，真实场景中一般为 ajax 请求
            // setTimeout(() => {
            //     for (let i = 0; i < 10; i++) {
            //         this.list.push(this.list.length + 1);
            //     }

            //     // 加载状态结束
            //     this.loading = false;

            //     // 数据全部加载完成
            //     if (this.list.length >= 40) {
            //         this.finished = true;
            //     }
            // }, 1000);
            this.loading = true;
            this.pageNum++;
            this.getStock();
        },
        getStock: handleDt.debounce(async function () {
            // this.listArr = []
            let opt = {
                pageNum: this.pageNum,
                pageSize: 15,
                keyWords: this.keyWords,
            };
            let data = await api.getStock(opt);
            if (data.status == 0) {
                this.loading = false;
                if (data.data.list.length == 0) {
                    this.finished = true;
                    return;
                } else if (
                    data.data.list.length > 0 &&
                    data.data.list.length < 15
                ) {
                    this.finished = true;
                    data.data.list.forEach((element) => {
                        this.listArr.push(element);
                    });
                } else {
                    data.data.list.forEach((element) => {
                        this.listArr.push(element);
                    });
                }
            }
        }, 2000),
        handleSearch() {
            this.listArr = [];
            this.getStock();
        },
        clearSearch() {
            this.keyWords = "";
            this.listArr = [];
        },
        getPriceClass(hcrate) {
            if (hcrate > 0) {
                return 'red';
            } else if (hcrate < 0) {
                return 'green';
            } else {
                return '';
            }
        },
    },
};
</script>

<style lang="less" scoped>
.search-container {
    font-size: 0.3256rem;
    padding: 0;
    background: #fff;
    min-height: 100%;

    .red {
        color: #D91601;
    }

    .green {
        color: #00A444;
    }

    .search-header {
        padding: 0.4651rem;
        display: flex;
        gap: 0.3256rem;
        background: linear-gradient(145deg, #ffffff, #f8f9fa);
        border-bottom: 1px solid #e9ecef;

        .search-box {
            flex: 1;
            position: relative;
            background: #ffffff;
            border: 2px solid #e9ecef;
            border-radius: 0.6977rem;
            height: 1.0465rem;
            display: flex;
            align-items: center;
            padding: 0 0.4651rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 0.0465rem 0.1860rem rgba(0, 0, 0, 0.04);


            .search-icon {
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 0.2791rem;

                svg {
                    width: 0.4186rem;
                    height: 0.4186rem;
                    fill: #adb5bd;
                    transition: fill 0.3s ease;
                }
            }


            .search-input {
                flex: 1;
                border: none;
                outline: none;
                font-size: 0.3721rem;
                color: #2c3e50;
                background: transparent;
                height: 100%;

                &::placeholder {
                    color: #adb5bd;
                    font-weight: 400;
                }
            }

            .clear-btn {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 0.5581rem;
                height: 0.5581rem;
                border-radius: 50%;
                background: rgba(173, 181, 189, 0.1);
                cursor: pointer;
                transition: all 0.3s ease;
                margin-left: 0.2326rem;

                svg {
                    width: 0.3256rem;
                    height: 0.3256rem;
                    fill: #adb5bd;
                    transition: fill 0.3s ease;
                }

                &:hover {
                    background: rgba(173, 181, 189, 0.2);
                    transform: scale(1.1);

                    svg {
                        fill: #6c757d;
                    }
                }

                &:active {
                    transform: scale(0.95);
                }
            }
        }

        .cancel-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 0.4651rem;
            color: #EA001B;
            font-weight: 500;
            font-size: 0.3721rem;
            cursor: pointer;
            border-radius: 0.6977rem;
            transition: all 0.3s ease;
            min-width: 1.3953rem;

            &:hover {
                background: rgba(234, 0, 27, 0.05);
                color: #d00019;
            }

            &:active {
                background: rgba(234, 0, 27, 0.1);
                transform: scale(0.98);
            }
        }
    }

    .search-results {
        .table-header {
            display: flex;
            padding: 0.3488rem;
            color: rgba(181, 181, 181, 1);

            .col-name {
                flex: 1;
                text-align: left;
            }

            .col-price {
                flex: 1;
                text-align: center;
            }

            .col-change {
                flex: 1;
                text-align: right;
            }
        }

        .stock-list {
            .stock-item {
                padding: 0.3488rem;
                display: flex;
                border-bottom: solid 1px rgba(245, 247, 250, 1);
                align-items: center;

                .stock-info {
                    flex: 1;
                    display: flex;
                    align-items: flex-start;
                    flex-direction: column;

                    .stock-name {
                        font-size: 0.372rem;
                        margin-bottom: 0.2325rem;
                    }
                }

                .stock-price {
                    flex: 1;
                    text-align: center;
                    font-size: 0.372rem;
                }

                .stock-change {
                    flex: 1;
                    text-align: right;
                    font-size: 0.372rem;

                    .change-percent {
                        margin-right: 0.2325rem;
                    }

                    .change-icon {
                        font-size: 0.372rem;
                    }
                }
            }
        }
    }

    .empty-state {
        text-align: center;
        padding: 0.3488rem;
        color: rgba(181, 181, 181, 1);

        .empty-icon {
            font-size: 2rem;
            margin-bottom: 0.2325rem;
        }

        .empty-text {
            font-size: 0.372rem;
        }
    }
}
</style>