<template>
    <div class="settings-page">
        <div class="header">
            <van-nav-bar title="设置" left-arrow @click-left="$router.go(-1)" fixed></van-nav-bar>
        </div>

        <div class="settings-content">
            <!-- 密码管理 -->
            <div class="settings-section">
                <div class="section-title">
                    <span class="icon">🔐</span>
                    密码管理
                </div>
                <div class="menu-card">
                    <div class="menu-item" @click="navigateToResetPassword">
                        <div class="item-left">
                            <settings-icon icon-type="login-password" />
                            <div class="item-content">
                                <div class="item-title">修改登录密码</div>
                                <div class="item-desc">修改您的账户登录密码</div>
                            </div>
                        </div>
                        <div class="item-right">
                            <van-icon name="arrow" />
                        </div>
                    </div>

                    <div class="menu-item" @click="navigateToSetPassword">
                        <div class="item-left">
                            <settings-icon icon-type="fund-password" />
                            <div class="item-content">
                                <div class="item-title">修改资金密码</div>
                                <div class="item-desc">修改您的交易资金密码</div>
                            </div>
                        </div>
                        <div class="item-right">
                            <van-icon name="arrow" />
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统操作 -->
            <div class="settings-section">
                <div class="section-title">
                    <span class="icon">⚙️</span>
                    系统操作
                </div>
                <div class="menu-card">
                    <div class="menu-item logout-item" @click="confirmLogout">
                        <div class="item-left">
                            <settings-icon icon-type="logout" />
                            <div class="item-content">
                                <div class="item-title">退出登录</div>
                                <div class="item-desc">安全退出当前账户</div>
                            </div>
                        </div>
                        <div class="item-right">
                            <van-icon name="arrow" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import * as api from "@/axios/api";
import { MessageBox, Toast } from "mint-ui";
import SettingsIcon from "@/components/settings-icon.vue";

export default {
    name: "SettingsPage",
    components: {
        SettingsIcon
    },
    data() {
        return {
            isLoggingOut: false,
        };
    },
    mounted() {
        this.initializePage();
    },
    methods: {
        // 初始化页面
        initializePage() {
            console.log('设置页面初始化');
        },

        // 导航到重置密码页面
        navigateToResetPassword() {
            try {
                this.$router.push({ path: '/resetpass' });
            } catch (error) {
                console.error('导航到重置密码页面失败:', error);
                this.$toast('页面跳转失败');
            }
        },

        // 导航到设置密码页面
        navigateToSetPassword() {
            try {
                this.$router.push({ path: '/setPassword' });
            } catch (error) {
                console.error('导航到设置密码页面失败:', error);
                this.$toast('页面跳转失败');
            }
        },

        // 确认退出登录
        confirmLogout() {
            if (this.isLoggingOut) {
                return;
            }

            MessageBox.confirm(
                this.$t("hj149") + "?",
                this.$t("hj165"),
                {
                    confirmButtonText: this.$t("hj161"),
                    cancelButtonText: this.$t("hj106"),
                }
            ).then(() => {
                this.executeLogout();
            }).catch(() => {
                console.log('用户取消退出登录');
            });
        },

        // 执行退出登录
        async executeLogout() {
            this.isLoggingOut = true;

            try {
                // 清除本地存储
                this.clearLocalStorage();

                // 调用注销接口
                await this.callLogoutAPI();

                // 跳转到登录页
                this.navigateToLogin();

            } catch (error) {
                this.handleLogoutError(error);
            } finally {
                this.isLoggingOut = false;
            }
        },

        // 清除本地存储
        clearLocalStorage() {
            try {
                window.localStorage.removeItem("USERTOKEN");
                this.clearCookie();
                console.log('本地存储清除成功');
            } catch (error) {
                console.error('清除本地存储失败:', error);
            }
        },

        // 调用注销API
        async callLogoutAPI() {
            try {
                const response = await api.logout();

                if (response && response.status === 0) {
                    console.log('注销API调用成功:', response.msg);
                } else {
                    console.warn('注销API返回异常:', response);
                }

            } catch (error) {
                console.error('注销API调用失败:', error);
                // 即使API调用失败，也继续执行本地注销
            }
        },

        // 跳转到登录页
        navigateToLogin() {
            try {
                this.$router.push("/login");
                Toast('已安全退出');
            } catch (error) {
                console.error('跳转登录页失败:', error);
                // 强制刷新页面作为后备方案
                window.location.href = '/login';
            }
        },

        // 处理退出登录错误
        handleLogoutError(error) {
            console.error('退出登录失败:', error);

            // 即使出错也尝试跳转到登录页
            this.navigateToLogin();
        },

        // 清除Cookie（如果有的话）
        clearCookie() {
            try {
                // 这里可以添加清除Cookie的逻辑
                // document.cookie = "token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
            } catch (error) {
                console.error('清除Cookie失败:', error);
            }
        }
    },
};
</script>

<style lang="less" scoped>
.settings-page {
    background: #f5f5f5;
    min-height: 100vh;
    font-size: 0.3256rem;

    .header {
        width: 100%;
        height: 1.07rem;
    }

    .settings-content {
        padding: 0.2326rem;

        .settings-section {
            margin-bottom: 0.4651rem;

            .section-title {
                display: flex;
                align-items: center;
                gap: 0.1162rem;
                font-size: 0.3488rem;
                font-weight: 600;
                color: #2c3e50;
                margin-bottom: 0.2326rem;
                padding: 0 0.2326rem;

                .icon {
                    font-size: 0.3721rem;
                }
            }

            .menu-card {
                background: #fff;
                border-radius: 0.1860rem;
                box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
                overflow: hidden;

                .menu-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 0.4651rem;
                    border-bottom: 1px solid #f0f0f0;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    position: relative;

                    &:last-child {
                        border-bottom: none;
                    }

                    &:hover {
                        background: #f8f9fa;
                        transform: translateX(0.0697rem);
                    }

                    &:active {
                        background: #e9ecef;
                        transform: translateX(0.0348rem);
                    }

                    &.logout-item {
                        &:hover {
                            background: rgba(234, 0, 27, 0.05);

                            .settings-icon.logout {
                                background: rgba(234, 0, 27, 0.1);
                                border-color: rgba(234, 0, 27, 0.2);

                                .icon {
                                    fill: #EA001B;
                                }
                            }

                            .item-title {
                                color: #EA001B;
                            }
                        }
                    }

                    .item-left {
                        display: flex;
                        align-items: center;
                        gap: 0.3488rem;

                        .item-content {
                            .item-title {
                                font-size: 0.3721rem;
                                font-weight: 600;
                                color: #2c3e50;
                                margin-bottom: 0.0930rem;
                                transition: all 0.3s ease;
                            }

                            .item-desc {
                                font-size: 0.3256rem;
                                color: #7f8c8d;
                                line-height: 1.2;
                            }
                        }
                    }

                    .item-right {
                        .van-icon {
                            color: #bdc3c7;
                            font-size: 0.4186rem;
                            transition: all 0.3s ease;
                        }

                        &:hover {
                            .van-icon {
                                color: #95a5a6;
                                transform: translateX(0.0697rem);
                            }
                        }
                    }
                }
            }
        }
    }
}

/* 加载状态 */
.menu-item.loading {
    pointer-events: none;
    opacity: 0.6;

    .item-right {
        .van-icon {
            animation: spin 1s linear infinite;
        }
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

/* 响应式适配 */
@media (max-width: 375px) {
    .settings-page {
        .settings-content {
            padding: 0.1162rem;

            .settings-section {
                .menu-card {
                    .menu-item {
                        padding: 0.3488rem;

                        .item-left {
                            gap: 0.2326rem;
                        }
                    }
                }
            }
        }
    }
}
</style>