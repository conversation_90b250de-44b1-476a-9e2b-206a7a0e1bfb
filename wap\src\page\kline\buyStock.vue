<template>
    <div class="container">
        <div class="header">
            <van-nav-bar title="账户交易" left-arrow fixed @click-left="$router.go(-1)" />
        </div>
        <div class="menu">
            <div :class="`option ${tabIndex == 0 ? 'active' : ''}`" @click="changeTabIndex(0)">
                <div class="item">
                    <span>买入</span>
                    <span></span>
                </div>
            </div>
            <div :class="`option ${tabIndex == 1 ? 'active' : ''}`" @click="changeTabIndex(1)">
                <div class="item">
                    <span>卖出</span>
                    <span></span>
                </div>
            </div>
        </div>
        <div class="ebox_container" v-if="tabIndex == 0">
            <!-- 股票信息卡片 -->
            <div class="info-card">
                <div class="card-title">股票信息</div>
                <div class="ebox">
                    <div class="clabel">代码</div>
                    <div class="tag_container">
                        <stock-tag-info :stock-code="symbol" :show-code="false" />
                        <div>{{ name }}[{{ code }}]</div>
                    </div>
                </div>
                <div class="ebox">
                    <div class="clabel">现价</div>
                    <div class="price-value">¥{{ nowPrice }}</div>
                </div>
                <div class="ebox">
                    <div class="clabel">买入价格</div>
                    <div class="price-value">¥{{ nowPrice }}</div>
                </div>
            </div>

            <!-- 交易设置卡片 -->
            <div class="info-card">
                <div class="card-title">交易设置</div>
                <div class="ebox">
                    <div class="clabel">仓位</div>
                    <div class="cang">
                        <div :class="`cang_item ${itemIndex == 0 ? 'active' : ''}`" @click="calculatePortion(1 / 4, 0)">
                            1/4仓</div>
                        <div :class="`cang_item ${itemIndex == 1 ? 'active' : ''}`" @click="calculatePortion(1 / 3, 1)">
                            1/3仓</div>
                        <div :class="`cang_item ${itemIndex == 2 ? 'active' : ''}`" @click="calculatePortion(1 / 2, 2)">
                            1/2仓</div>
                        <div :class="`cang_item ${itemIndex == 3 ? 'active' : ''}`" @click="calculatePortion(1, 3)">全仓
                        </div>
                    </div>
                </div>
                <div class="ebox">
                    <div class="clabel">买入手数</div>
                    <div class="input-container">
                        <input class="trade-input" placeholder="请输入买入手数" :value="buyNum" @input="handleInput"
                            type="number" />
                        <span class="input-suffix">手</span>
                    </div>
                </div>
            </div>

            <!-- 费用信息卡片 -->
            <div class="info-card">
                <div class="card-title">费用明细</div>
                <div class="ebox">
                    <div class="clabel">手续费</div>
                    <div class="fee-value">¥{{ calculatedFee }}</div>
                </div>
                <div class="ebox">
                    <div class="clabel">可用金额</div>
                    <div class="balance-value">¥{{ formattedEnableAmount }}</div>
                </div>
                <div class="ebox total-row">
                    <div class="clabel total-label">应付金额</div>
                    <div class="total-value">¥{{ payableAmount }}</div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="action-container">
                <div class="ebtn" @click="setBuy" :class="{ disabled: !canTrade }">
                    {{ buyButtonText }}
                </div>
            </div>
        </div>
        <div class="ebox_container" v-if="tabIndex == 1">
            <div class="positions-header">
                <div class="header-title">持仓列表</div>
                <div class="header-subtitle">可平仓的股票持仓</div>
            </div>

            <van-list v-model="loading" :finished="finished" :finished-text="$t('hj43')" @load="getSellList"
                :immediate-check="immediate">
                <div class="position-card" v-for="value in sellList" :key="value.id">
                    <!-- 股票信息头部 -->
                    <div class="position-header">
                        <div class="stock-info">
                            <stock-tag-info :stock-code="value.stockGid" :show-code="false" />
                            <div class="stock-details">
                                <div class="stock-name">{{ value.stockName }}</div>
                                <div class="stock-code">{{ value.stockGid }}</div>
                            </div>
                        </div>
                        <div class="current-price">
                            <div class="price-label">最新价格</div>
                            <div class="price-value red">¥{{ value.now_price }}</div>
                        </div>
                    </div>

                    <!-- 交易信息 -->
                    <div class="position-details">
                        <div class="detail-row">
                            <div class="detail-item">
                                <div class="item-label">买入价格</div>
                                <div class="item-value">¥{{ value.buyOrderPrice }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="item-label">买入数量</div>
                                <div class="item-value">{{ value.buyNum / 100 }}手</div>
                            </div>
                        </div>

                        <div class="detail-row">
                            <div class="detail-item full-width">
                                <div class="item-label">市值</div>
                                <div class="item-value">¥{{ value.buyPrice }}</div>
                            </div>
                        </div>

                        <div class="detail-row profit-row">
                            <div class="detail-item">
                                <div class="item-label">浮动盈亏</div>
                                <div class="item-value profit-value">{{ value.profitAndLose }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="item-label">总盈亏</div>
                                <div class="item-value profit-value">{{ value.allProfitAndLose }}</div>
                            </div>
                        </div>

                        <div class="detail-row time-row">
                            <div class="detail-item full-width">
                                <div class="item-label">交易时间</div>
                                <div class="item-value time-value">{{ value.buyOrderTime | gettime }}</div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="position-actions">
                        <div class="close-btn" @click.stop="handleClosePosition(value.positionSn)">
                            我要平仓
                        </div>
                    </div>
                </div>
            </van-list>
        </div>
    </div>
</template>

<script>
import * as api from "@/axios/api";
import { Toast } from "vant";
import { MessageBox } from "mint-ui";
import BigNumber from "bignumber.js";
import StockTagInfo from "@/components/stock-tag-info.vue";
import dayjs from "dayjs";

export default {
    name: "BuyStock",

    components: {
        StockTagInfo
    },

    data() {
        return {
            itemIndex: -1,
            showPicker: false,
            currentItem: "",
            userinfo: {},
            settingdetail: {},
            siteLeverList: [],
            nowPrice: 0,
            buyType: 0, // 0 买入 1 卖出
            buyNum: "", //买入数量
            lever: "", //杠杆倍数
            name: "",
            code: "",
            id: "",
            buying: false,
            buyfee: 0,
            bigNumbuy: 0,
            symbol: "",
            tabIndex: 0,
            finished: false,
            immediate: false,
            sellList: [],
            loading: false,
            // 添加计算相关状态
            siteLever: []
        };
    },

    computed: {
        // 计算手续费
        calculatedFee() {
            if (!this.nowPrice || !this.buyNum || !this.settingdetail.buyFee) {
                return 0;
            }
            return (this.nowPrice * this.buyNum * 100 * this.settingdetail.buyFee).toFixed(2);
        },

        // 计算应付金额
        payableAmount() {
            if (!this.nowPrice || !this.buyNum || !this.lever) {
                return 0;
            }
            return (this.nowPrice * this.buyNum * 100 / this.lever).toFixed(2);
        },

        // 格式化可用金额
        formattedEnableAmount() {
            return this.userinfo.enableAmt || 0;
        },

        // 交易按钮文案
        buyButtonText() {
            return this.buying ? '下单中...' : '买入下单';
        },

        // 是否可以交易 - 修复刷新页面状态问题
        canTrade() {
            // 检查基本交易条件
            const basicConditions = !this.buying &&
                this.buyNum &&
                Number(this.buyNum) > 0;

            // 检查用户身份认证状态 - 优先使用本地userinfo，降级到store
            const userIdCard = (this.userinfo && this.userinfo.idCard) ||
                (this.$store.state.userInfo && this.$store.state.userInfo.idCard);

            return basicConditions && userIdCard;
        },

        // 当前股票完整代码（用于标签显示）
        fullStockCode() {
            return this.symbol || this.code;
        }
    },

    watch: {
        // 监听买入数量变化，自动计算费用
        buyNum: {
            handler(newVal) {
                if (newVal && this.nowPrice && this.settingdetail.buyFee) {
                    this.calculateFee();
                }
            },
            immediate: true
        },

        // 监听tab切换
        tabIndex: {
            handler(newIndex) {
                this.handleTabChange(newIndex);
            }
        }
    },

    async mounted() {
        try {
            this.initializeRouteParams();
            // 始终获取用户信息，解决刷新页面状态异常问题
            await this.ensureUserInfo();
            await this.init();
        } catch (error) {
            console.error('页面初始化失败:', error);
            Toast('页面加载失败，请重试');
        }
    },

    methods: {
        // 确保用户信息已加载
        async ensureUserInfo() {
            // 如果store中没有用户信息或本地没有用户信息，则获取
            const storeHasIdCard = this.$store.state.userInfo && this.$store.state.userInfo.idCard;
            const localHasIdCard = this.userinfo && this.userinfo.idCard;

            if (!storeHasIdCard || !localHasIdCard) {
                await this.getUserInfo();
            }
        },

        // 初始化路由参数
        initializeRouteParams() {
            const { query } = this.$route;

            this.code = query.code || '';
            this.name = query.name || '';
            this.tabIndex = Number(query.tab) || 0;
            this.id = query.id || '';
            this.nowPrice = Number(query.m) || 0;
            this.symbol = query.symbol || '';
        },

        // 初始化数据
        async init() {
            if (this.tabIndex === 0) {
                await this.initBuyTab();
            } else {
                await this.initSellTab();
            }
        },

        // 初始化买入页面
        async initBuyTab() {
            try {
                await this.getUserSetting();
                // 如果之前没有获取用户信息，这里再次确保
                if (!(this.userinfo && this.userinfo.enableAmt)) {
                    await this.getUserInfo();
                }
            } catch (error) {
                console.error('买入页面初始化失败:', error);
                Toast('加载失败，请重试');
            }
        },

        // 初始化卖出页面
        async initSellTab() {
            this.sellList = [];
            this.finished = false;
            await this.getSellList();
        },

        // 处理tab切换
        async handleTabChange(index) {
            this.tabIndex = index;
            await this.init();
        },

        // Tab切换方法重命名
        changeTabIndex(index) {
            this.tabIndex = index;
        },

        // 获取卖出列表
        async getSellList() {
            if (this.loading) return;

            this.loading = true;
            try {
                const params = {
                    state: 0,
                    stockCode: this.code,
                    stockSpell: "",
                    pageNum: 1,
                    pageSize: 100,
                };

                const res = await api.getOrderList(params);

                if (res.data.list.length < 15) {
                    this.finished = true;
                }

                this.sellList.push(...res.data.list);
            } catch (error) {
                console.error('获取卖出列表失败:', error);
                Toast('获取数据失败，请重试');
            } finally {
                this.loading = false;
            }
        },

        // 平仓操作
        async handleClosePosition(positionSn) {
            try {
                await MessageBox.confirm(
                    this.$t("hj139") + "?",
                    this.$t("hj165"),
                    {
                        confirmButtonText: this.$t("hj161"),
                        cancelButtonText: this.$t("hj106"),
                    }
                );

                const data = await api.sell({ positionSn });

                if (data.status === 0) {
                    Toast(data.msg);
                    await this.refreshData();
                } else if (data.msg && data.msg.includes("不在交易时段内")) {
                    Toast(this.$t("hj140"));
                } else {
                    Toast(data.msg || '操作失败');
                }
            } catch (error) {
                if (error !== 'cancel') {
                    console.error('平仓操作失败:', error);
                    Toast('操作失败，请重试');
                }
            }
        },

        // 刷新数据
        async refreshData() {
            this.finished = false;

            // 这些方法可能在其他地方定义，添加安全检查
            if (typeof this.getListDetail === 'function') {
                this.getListDetail();
            }

            await this.init();
        },

        // 计算仓位
        calculatePortion(fraction, index) {
            if (!this.bigNumbuy) return;

            const portion = new BigNumber(this.bigNumbuy)
                .multipliedBy(fraction)
                .integerValue(BigNumber.ROUND_FLOOR);

            this.buyNum = portion.toString();
            this.itemIndex = index;
        },

        // 选择器确认
        onConfirm(item) {
            this.showPicker = false;
            this.lever = item.value;
        },

        // 输入处理优化
        handleInput(event) {
            const value = event.target.value;
            let numericValue = value.replace(/[^0-9]/g, "");

            // 移除前导零
            if (numericValue.length > 1 && numericValue.charAt(0) === "0") {
                numericValue = numericValue.slice(1);
            }

            this.buyNum = numericValue;

            // 检查最大购买数量
            const buyNum = new BigNumber(this.buyNum || 0);
            const maxBuyNum = new BigNumber(this.bigNumbuy || 0);

            if (buyNum.isGreaterThan(maxBuyNum)) {
                this.buyNum = this.bigNumbuy.toString();
                Toast(`最大只能购买${this.bigNumbuy}手`);
            }
        },

        // 获取用户设置
        async getUserSetting() {
            try {
                const data = await api.getSetting({});
                this.settingdetail = data.data;

                this.siteLever = this.settingdetail.siteLever.split("/");
                this.lever = this.siteLever[0];

                // 构建杠杆列表
                this.siteLeverList = this.siteLever.map(val => ({
                    text: `${val}倍`,
                    value: val,
                }));

                this.buyNum = (this.settingdetail.buyMinNum / 100).toString();
                this.calculateFee();
            } catch (error) {
                console.error('获取用户设置失败:', error);
                Toast('获取设置失败');
                throw error;
            }
        },

        // 计算手续费
        calculateFee() {
            if (!this.nowPrice || !this.buyNum || !this.settingdetail.buyFee) {
                this.buyfee = 0;
                return;
            }

            this.buyfee = (
                this.nowPrice *
                this.buyNum *
                100 *
                this.settingdetail.buyFee
            ).toFixed(2);
        },

        // 获取用户信息
        async getUserInfo() {
            try {
                const data = await api.getUserInfo();

                if (data.status !== 0) {
                    Toast(data.msg || '获取用户信息失败');
                    return;
                }

                // 同时更新本地和store状态，确保页面刷新后状态正确
                this.userinfo = data.data;
                this.$store.state.userInfo = data.data;

                this.calculateMaxBuyAmount();
            } catch (error) {
                console.error('获取用户信息失败:', error);
                Toast('获取用户信息失败');
                throw error;
            }
        },

        // 计算最大购买数量
        calculateMaxBuyAmount() {
            try {
                const enableAmt = new BigNumber(this.userinfo.enableAmt || 0);
                const nowPrice = new BigNumber(this.nowPrice);
                const buyFee = new BigNumber(this.settingdetail.buyFee || 0);

                if (nowPrice.isZero()) {
                    this.bigNumbuy = 0;
                    return;
                }

                // 计算买入总量
                const singleStockCost = nowPrice
                    .multipliedBy(100)
                    .plus(nowPrice.multipliedBy(100).multipliedBy(buyFee));

                const buyTotal = enableAmt.dividedBy(singleStockCost);

                this.bigNumbuy = buyTotal.integerValue(BigNumber.ROUND_FLOOR).toNumber();
            } catch (error) {
                console.error('计算最大购买数量失败:', error);
                this.bigNumbuy = 0;
            }
        },

        // 验证交易参数
        validateTradeParams() {
            const errors = [];

            // 使用与canTrade相同的逻辑检查用户身份认证
            const userIdCard = (this.userinfo && this.userinfo.idCard) ||
                (this.$store.state.userInfo && this.$store.state.userInfo.idCard);

            if (!userIdCard) {
                errors.push({ message: this.$t("hj111"), action: () => this.$router.push("/smrz") });
                return errors;
            }

            const buyNumShares = this.buyNum * 100;
            const minNum = this.settingdetail.buyMinNum;
            const maxNum = this.settingdetail.buyMaxNum;

            if (buyNumShares < minNum) {
                errors.push({
                    message: `交易数量不能小于${minNum / 100}手`
                });
            }

            if (buyNumShares > maxNum) {
                errors.push({
                    message: `交易数量不能大于${maxNum / 100}手`
                });
            }

            return errors;
        },

        // 买入下单
        async setBuy() {
            const errors = this.validateTradeParams();

            if (errors.length > 0) {
                const error = errors[0];
                Toast(error.message);
                if (error.action) error.action();
                return;
            }

            if (this.buying) return;

            this.buying = true;

            try {
                const params = {
                    buyNum: Number(this.buyNum) * 100,
                    lever: this.lever,
                    buyType: 0, // 买入
                    stockId: this.id,
                };

                await this.executeBuy(params);
            } catch (error) {
                console.error('买入下单失败:', error);
                Toast('下单失败，请重试');
            } finally {
                this.buying = false;
            }
        },

        // 执行买入
        async executeBuy(params) {
            const data = await api.buy(params);

            if (data.status === 0) {
                Toast(data.data || '下单成功');
                await this.getUserInfo(); // 刷新用户信息
                this.$router.go(-1);
            } else {
                const errorMessage = data.msg && data.msg.includes("不在交易时段内")
                    ? this.$t("hj113")
                    : data.msg || '下单失败';
                Toast(errorMessage);
            }
        }
    },

    filters: {
        gettime(time) {
            if (!time) {
                return "";
            }
            return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
        },
    },
};
</script>

<style lang="less" scoped>
.container {
    font-size: 0.3256rem;
    padding: 0;
    background: rgba(245, 247, 250, 1);

    .header {
        width: 100%;
        height: 1.07rem;
    }

    .menu {
        padding-top: 0.2326rem;
        border-bottom: 1px solid #f1f1f1;
        display: flex;
        background: #fff;

        .option {
            flex: 1;
            display: flex;
            justify-content: center;

            .item {
                display: flex;
                flex-direction: column;

                span:nth-last-of-type(1) {
                    display: none;
                    height: 2px;
                    width: 100%;
                    margin-top: 0.2326rem;
                    background: rgba(238, 0, 17, 1);
                }
            }

            &.active {
                .item {
                    color: rgba(238, 0, 17, 1);

                    span:nth-last-of-type(1) {
                        display: block;
                    }
                }
            }
        }
    }

    .ebox_container {
        padding: 0.3488rem;

        /* 信息卡片样式 */
        .info-card {
            background: #fff;
            border-radius: 0.2326rem;
            margin-bottom: 0.3488rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            overflow: hidden;

            &:last-of-type {
                margin-bottom: 0;
            }

            .card-title {
                background: rgba(248, 249, 250, 1);
                padding: 0.3488rem;
                font-size: 0.3256rem;
                font-weight: 500;
                color: rgba(51, 51, 51, 1);
                border-bottom: 1px solid rgba(240, 240, 240, 1);
            }
        }

        /* 信息行样式 */
        .ebox {
            display: flex;
            align-items: center;
            justify-content: space-between;
            min-height: 1.1627rem;
            padding: 0.3488rem;
            border-bottom: 1px solid rgba(245, 247, 250, 1);

            &:last-child {
                border-bottom: none;
            }

            .clabel {
                color: rgba(125, 125, 125, 1);
                font-size: 0.3256rem;
                width: 30%;
                flex-shrink: 0;
            }

            /* 价格显示 */
            .price-value {
                color: rgba(51, 51, 51, 1);
                font-size: 0.3721rem;
                font-weight: 500;
            }

            /* 费用显示 */
            .fee-value {
                color: rgba(255, 102, 0, 1);
                font-size: 0.3256rem;
            }

            /* 余额显示 */
            .balance-value {
                color: rgba(76, 175, 80, 1);
                font-size: 0.3256rem;
                font-weight: 500;
            }

            /* 总金额行 */
            &.total-row {
                background: rgba(248, 249, 250, 1);

                .total-label {
                    font-weight: 500;
                    color: rgba(51, 51, 51, 1);
                }

                .total-value {
                    color: rgba(215, 12, 24, 1);
                    font-size: 0.4186rem;
                    font-weight: bold;
                }
            }

            /* 股票标签容器 */
            .tag_container {
                display: flex;
                align-items: center;
                gap: 0.1162rem;
                flex: 1;
                justify-content: flex-end;
            }

            /* 仓位选择 */
            .cang {
                display: flex;
                gap: 0.1162rem;
                flex-wrap: wrap;

                .cang_item {
                    background: rgba(245, 247, 250, 1);
                    border: 1px solid rgba(224, 224, 224, 1);
                    border-radius: 0.1162rem;
                    font-size: 0.2791rem;
                    padding: 0.1395rem 0.2326rem;
                    cursor: pointer;
                    transition: all 0.3s;
                    min-width: 1.3953rem;
                    text-align: center;

                    &:hover {
                        background: rgba(224, 57, 54, 0.1);
                    }

                    &.active {
                        background: rgba(224, 57, 54, 0.1);
                        color: rgba(224, 57, 54, 1);
                        border-color: rgba(224, 57, 54, 1);
                    }
                }
            }

            /* 输入框容器 */
            .input-container {
                display: flex;
                align-items: center;
                flex: 1;
                justify-content: flex-end;
                position: relative;

                .trade-input {
                    width: 3.4883rem;
                    height: 0.9302rem;
                    border: 1px solid rgba(224, 224, 224, 1);
                    border-radius: 0.1162rem;
                    padding: 0 0.7441rem 0 0.2326rem;
                    font-size: 0.3256rem;
                    text-align: right;
                    outline: none;
                    background: #fff;

                    &:focus {
                        border-color: rgba(224, 57, 54, 1);
                        box-shadow: 0 0 0 2px rgba(224, 57, 54, 0.1);
                    }

                    &::placeholder {
                        color: rgba(192, 192, 192, 1);
                        font-size: 0.2791rem;
                    }
                }

                .input-suffix {
                    position: absolute;
                    right: 0.2326rem;
                    color: rgba(125, 125, 125, 1);
                    font-size: 0.3256rem;
                    pointer-events: none;
                }
            }
        }

        /* 操作按钮容器 */
        .action-container {
            margin-top: 0.6977rem;
        }

        /* 买入按钮 */
        .ebtn {
            border-radius: 0.1162rem;
            background: rgba(215, 12, 24, 1);
            box-shadow: 0px 4px 12px rgba(224, 57, 54, 0.3);
            height: 1.2791rem;
            line-height: 1.2791rem;
            text-align: center;
            color: #fff;
            font-size: 0.4186rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
            width: 100%;

            &:hover {
                background: rgba(190, 10, 20, 1);
                transform: translateY(-1px);
                box-shadow: 0px 6px 16px rgba(224, 57, 54, 0.4);
            }

            &:active {
                transform: translateY(0);
            }

            &.disabled {
                background: rgba(192, 192, 192, 1);
                box-shadow: none;
                cursor: not-allowed;

                &:hover {
                    background: rgba(192, 192, 192, 1);
                    transform: none;
                    box-shadow: none;
                }
            }
        }

        /* 持仓列表头部 */
        .positions-header {
            background: #fff;
            border-radius: 0.2326rem;
            margin-bottom: 0.3488rem;
            padding: 0.4651rem 0.3488rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

            .header-title {
                font-size: 0.4186rem;
                font-weight: 500;
                color: rgba(51, 51, 51, 1);
                margin-bottom: 0.1162rem;
            }

            .header-subtitle {
                font-size: 0.2791rem;
                color: rgba(125, 125, 125, 1);
            }
        }

        /* 持仓卡片样式 */
        .position-card {
            background: #fff;
            border-radius: 0.2326rem;
            margin-bottom: 0.3488rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            overflow: hidden;
        }

        /* 持仓头部 */
        .position-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.3488rem;
            border-bottom: 1px solid rgba(245, 247, 250, 1);

            .stock-info {
                display: flex;
                align-items: center;
                gap: 0.2326rem;

                .stock-details {
                    display: flex;
                    flex-direction: column;
                    gap: 0.0581rem;

                    .stock-name {
                        font-size: 0.3721rem;
                        font-weight: 500;
                        color: rgba(51, 51, 51, 1);
                    }

                    .stock-code {
                        font-size: 0.2791rem;
                        color: rgba(125, 125, 125, 1);
                    }
                }
            }

            .current-price {
                text-align: right;

                .price-label {
                    font-size: 0.2791rem;
                    color: rgba(125, 125, 125, 1);
                    margin-bottom: 0.0581rem;
                }

                .price-value {
                    font-size: 0.3721rem;
                    font-weight: 500;

                    &.red {
                        color: rgba(215, 12, 24, 1);
                    }
                }
            }
        }

        /* 持仓详情 */
        .position-details {
            padding: 0.3488rem;

            .detail-row {
                display: flex;
                justify-content: space-between;
                margin-bottom: 0.2326rem;

                &:last-child {
                    margin-bottom: 0;
                }

                &.profit-row {
                    background: rgba(248, 249, 250, 1);
                    margin: 0.2326rem -0.3488rem;
                    padding: 0.2326rem 0.3488rem;
                    border-radius: 0.1162rem;
                }

                &.time-row {
                    border-top: 1px solid rgba(245, 247, 250, 1);
                    padding-top: 0.2326rem;
                }
            }

            .detail-item {
                display: flex;
                flex-direction: column;
                gap: 0.0581rem;
                flex: 1;

                &.full-width {
                    flex: 2;
                }

                .item-label {
                    font-size: 0.2791rem;
                    color: rgba(125, 125, 125, 1);
                }

                .item-value {
                    font-size: 0.3256rem;
                    color: rgba(51, 51, 51, 1);
                    font-weight: 500;

                    &.profit-value {
                        font-weight: bold;
                    }

                    &.time-value {
                        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                        color: rgba(125, 125, 125, 1);
                        font-size: 0.2791rem;
                    }
                }
            }
        }

        /* 持仓操作 */
        .position-actions {
            padding: 0.3488rem;
            border-top: 1px solid rgba(245, 247, 250, 1);
            text-align: center;

            .close-btn {
                background: linear-gradient(135deg, rgba(37, 103, 255, 1) 0%, rgba(54, 120, 255, 1) 100%);
                color: #fff;
                border: none;
                border-radius: 0.1162rem;
                height: 0.9302rem;
                line-height: 0.9302rem;
                padding: 0 0.6977rem;
                font-size: 0.3256rem;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.3s;
                display: inline-block;

                &:hover {
                    background: linear-gradient(135deg, rgba(30, 90, 230, 1) 0%, rgba(45, 105, 230, 1) 100%);
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba(37, 103, 255, 0.3);
                }

                &:active {
                    transform: translateY(0);
                }
            }
        }

        .red {
            color: rgba(215, 12, 24, 1);
        }
    }
}
</style>