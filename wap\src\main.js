import Vue from 'vue'
import App from './App'
import router from './router'
import Icon from 'vue-svg-icon/Icon.vue'
import { MessageBox, Toast } from 'mint-ui'
// import Resource from 'vue-resource' // 已卸载用axios代替
import store from './store/index'
import axios from './axios/index'
import {
  Alert,
  Steps,
  Step,
  Upload,
  Tag as ElTag,
  Menu,
  MenuItem,
  Submenu,
  Input,
  InfiniteScroll
} from 'element-ui'
import VueClipboard from 'vue-clipboard2' // 复制
import state from './event'
import {
  Toast as VantToast,
  Dialog,
  Swipe,
  SwipeItem,
  Skeleton,
  Switch,
  Notify,
  Tab,
  Tabs,
  Popup,
  DatetimePicker,
  NavBar,
  Icon as VantIcon,
  Button,
  List,
  Loading,
  Checkbox,
  ActionSheet,
  Popover,
  Overlay,
  Tabbar,
  TabbarItem,
  Stepper,
  Tag as VanTag
} from 'vant'

import i18n from '@/locales'
import './assets/css/style.css'

// VConsole
// import VConsole from 'vconsole';
// const vConsole = new VConsole();

import 'mint-ui/lib/message-box/style.css'
import 'mint-ui/lib/toast/style.css'
// 按需引入 Element UI 组件样式
import 'element-ui/lib/theme-chalk/alert.css'
import 'element-ui/lib/theme-chalk/steps.css'
import 'element-ui/lib/theme-chalk/step.css'
import 'element-ui/lib/theme-chalk/upload.css'
import 'element-ui/lib/theme-chalk/tag.css'
import 'element-ui/lib/theme-chalk/menu.css'
import 'element-ui/lib/theme-chalk/menu-item.css'
import 'element-ui/lib/theme-chalk/submenu.css'
import 'element-ui/lib/theme-chalk/input.css'
import 'element-ui/lib/theme-chalk/infinite-scroll.css'
import 'lib-flexible'
// import md5 from 'js-md5'
import * as filters from '@/utils/utils'
import animated from 'animate.css' // npm install animate.css --save安装，在引入


// 按需引入 Vant 组件样式
import 'vant/lib/toast/style'
import 'vant/lib/dialog/style'
import 'vant/lib/swipe/style'
import 'vant/lib/swipe-item/style'
import 'vant/lib/skeleton/style'
import 'vant/lib/switch/style'
import 'vant/lib/notify/style'
import 'vant/lib/tab/style'
import 'vant/lib/tabs/style'
import 'vant/lib/popup/style'
import 'vant/lib/datetime-picker/style'
import 'vant/lib/nav-bar/style'
import 'vant/lib/icon/style'
import 'vant/lib/button/style'
import 'vant/lib/list/style'
import 'vant/lib/loading/style'
import 'vant/lib/checkbox/style'
import 'vant/lib/action-sheet/style'
import 'vant/lib/popover/style'
import 'vant/lib/overlay/style'
import 'vant/lib/tabbar/style'
import 'vant/lib/tabbar-item/style'
import 'vant/lib/tag/style'

// import '../static/css/public2.css'

import dayjs from 'dayjs'
// // 设置title
/* eslint-disable no-new */

import posthog from 'posthog-js'


Vue.prototype.dayjs = dayjs
// import VueTouch from 'vue-touch'
Vue.use(animated)
Vue.use(VueClipboard)

// 按需注册 Element UI 组件
Vue.use(Alert)
Vue.use(Steps)
Vue.use(Step)
Vue.use(Upload)
Vue.use(ElTag)
Vue.use(Menu)
Vue.use(MenuItem)
Vue.use(Submenu)
Vue.use(Input)
Vue.use(InfiniteScroll)

// Vue.use(VueTouch, { name: 'v-touch' })
// // Vue.prototype.$md5 = md5
// VueTouch.config.swipe = {
//   threshold: 100 // 手指左右滑动距离
// }

// 按需注册 Vant 组件
Vue.use(VantToast)
Vue.use(Dialog)
Vue.use(Swipe)
Vue.use(SwipeItem)
Vue.use(Skeleton)
Vue.use(Switch)
Vue.use(Notify)
Vue.use(Tab)
Vue.use(Tabs)
Vue.use(Popup)
Vue.use(DatetimePicker)
Vue.use(NavBar)
Vue.use(VantIcon)
Vue.use(Button)
Vue.use(List)
Vue.use(Loading)
Vue.use(Checkbox)
Vue.use(ActionSheet)
Vue.use(Popover)
Vue.use(Overlay)
Vue.use(Tabbar)
Vue.use(TabbarItem)
Vue.use(VanTag)
Vue.use(Stepper)

// 按需注册 Mint UI 组件
Vue.prototype.$messagebox = MessageBox
Vue.prototype.$toast = Toast

Vue.prototype._i18n = i18n

Vue.component('icon', Icon)
Vue.config.productionTip = false
Object.keys(filters).forEach((key) => {
  Vue.filter(key, filters[key])
})
Vue.prototype.$state = state
Vue.prototype.$setgoindex = function () {
  if (window.history.length <= 1) {
    if (location.href.indexOf('?') === -1) {
      window.location.href = location.href + '?goindex=true'
    } else if (
      location.href.indexOf('?') !== -1 &&
      location.href.indexOf('goindex') === -1
    ) {
      window.location.href = location.href + '&goindex=true'
    }
  }
}
Vue.prototype.setCookie = function (name, value, day) {
  if (day !== 0) {
    // 当设置的时间等于0时，不设置expires属性，cookie在浏览器关闭后删除
    var curDate = new Date()
    var curTamp = curDate.getTime()
    var curWeeHours = new Date(curDate.toLocaleDateString()).getTime() - 1
    var passedTamp = curTamp - curWeeHours
    var leftTamp = 24 * 60 * 60 * 1000 - passedTamp
    var leftTime = new Date()
    leftTime.setTime(leftTamp + curTamp)
    document.cookie =
      name + '=' + escape(value) + ';expires=' + leftTime.toGMTString()
  } else {
    document.cookie = name + '=' + escape(value)
  }
}
Vue.prototype.getCookie = function (name) {
  var arr
  var reg = new RegExp('(^| )' + name + '=([^;]*)(;|$)')
  arr = document.cookie.match(reg)
  if (arr) {
    return unescape(arr[2])
  } else {
    return null
  }
  // document.cookie = name + "=" + escape(value);
}
Vue.prototype.clearCookie = function () {
  this.setCookie('USER_TOKEN', '', -1)
}
Vue.prototype.checkCookie = function () {
  var user = this.getCookie('USER_TOKEN')
  if (user !== '') {
    alert('Welcome again ' + user)
  } else {
    user = prompt('Please enter your name:', '')
    if (user !== '' && user != null) {
      this.setCookie('USER_TOKEN', user, 365)
    }
  }
}
// router.beforeEach((to, from, next) => {
// console.log(to.path)
// store.state.select = to.path
// document.title = to.meta.title || '亿点通'
// // 判断是否登录
// console.log(document.cookie)
// // console.log(checkCookie(),'checkCookie()')
// if(!to.meta.requireAuth){
// next()
// return
// }
// if (document.cookie && to.meta.requireAuth) {
// if (to.path === '/login') {
// next({ path: '/' })
// } else {
// if (!to.query.url && from.query.url) {
// to.query.url = from.query.url
// }
// next()
// }
// }else{
// if (to.path === '/login') {
// next()
// } else {
// next({ path: '/login' })
// }
// }
// })
const whitelist = ['/login', '/register', '/xieyiMianze', '/service']
router.beforeEach((to, from, next) => {
  store.state.select = to.path
  document.title = to.meta.title
  const token = window.localStorage.getItem('USERTOKEN')
  if (!token && !whitelist.includes(to.path)) {
    next({ path: '/login' })
  } else {
    next()
  }
  // next()
  // if (!to.query.url && from.query.url) {
  //   to.query.url = from.query.url
  // }
})

// 初始化 PostHog
posthog.init('phc_UekjJSQYma7ND6QZrpv94BbQEbfmh4HRZKqLx6QBFEM', {
  api_host: 'https://us.i.posthog.com',
  autocapture: true, // 自动捕获用户交互
  mask_all_inputs: false // 关闭所有输入字段的默认屏蔽
})

// 将 posthog 挂载到 Vue 原型，方便全局使用
Vue.prototype.$posthog = posthog

// 引入 API 配置
import apiConfig from './config/api-config'

// 添加API相关配置到Vue原型，用于全局访问
Vue.prototype.$apiUrls = apiConfig.API_URLS
Vue.prototype.$apiConnectionPoolUrl = apiConfig.CONNECTION_POOL_URL
Vue.prototype.$apiHealthCheckPath = apiConfig.HEALTH_CHECK_PATH
Vue.prototype.$apiHealthCheckResponse = apiConfig.HEALTH_CHECK_RESPONSE

async function initializeBaseUrl() {
  let data = null
  try {
    const connectionPoolUrl = apiConfig.CONNECTION_POOL_URL
    const res = await axios.get(connectionPoolUrl, {
      withCredentials: false
    })
    data = res.data
  } catch (error) {
    console.error('Failed to dynamically set baseURL:', error.message)
  }

  // 使用配置文件中的URL列表
  const urlList = apiConfig.API_URLS

  let urls = [...urlList]
  if (data && data.url && Array.isArray(data.url)) {
    urls = [...urlList, ...data.url]

    // 将动态获取的URL也保存到Vue原型上
    Vue.prototype.$apiUrls = urls
  }

  try {
    let firstValidUrl = null
    const results = []

    // 创建一个Promise用于获取第一个可用的URL
    const firstValidPromise = new Promise((resolve, reject) => {
      let completedCount = 0
      let hasResolved = false

      // 为每个URL创建健康检查请求
      urls.forEach(async (url) => {
        const startTime = performance.now()
        try {
          const response = await axios.get(`${url}${apiConfig.HEALTH_CHECK_PATH}`, {
            timeout: 20000,
            withCredentials: false
          })

          const endTime = performance.now()
          const responseTime = endTime - startTime
          const isValid = response.data && response.data.data === apiConfig.HEALTH_CHECK_RESPONSE

          // 记录测试结果
          results.push({
            url,
            responseTime,
            status: isValid ? '可用' : '不可用',
            isValid
          })

          // 如果找到第一个有效的URL且还没有resolved，立即resolve
          if (isValid && !hasResolved) {
            hasResolved = true
            firstValidUrl = url
            resolve({ url, responseTime })
            return
          }

          completedCount++

          // 如果所有URL都测试完了且没有找到有效的，reject
          if (completedCount === urls.length && !hasResolved) {
            reject(new Error('No valid baseURL available.'))
          }

        } catch (error) {
          const endTime = performance.now()

          // 记录错误结果
          results.push({
            url,
            responseTime: endTime - startTime,
            status: '不可用',
            error: error.message,
            isValid: false
          })

          completedCount++
          console.error(`Health-check failed for ${url}:`, error.message)

          // 如果所有URL都测试完了且没有找到有效的，reject
          if (completedCount === urls.length && !hasResolved) {
            reject(new Error('No valid baseURL available.'))
          }
        }
      })
    })

    // 等待第一个可用的URL
    const firstResult = await firstValidPromise
    firstValidUrl = firstResult.url

    if (firstValidUrl) {
      // 保存当前使用的URL  
      Vue.prototype.$currentBaseUrl = firstValidUrl

      // 设置axios默认URL
      // axios.defaults.baseURL = 'http://localhost:8093'
      axios.defaults.baseURL = firstValidUrl

      console.log('Production: First valid baseURL set to', firstValidUrl, `(${firstResult.responseTime.toFixed(0)}ms)`)

      // 在后台继续收集其他URL的测试结果
      setTimeout(() => {
        // 排序并保存完整结果到Vue原型（用于调试和监控）
        const sortedResults = results.sort((a, b) => a.responseTime - b.responseTime)
        Vue.prototype.$apiSpeedTestResults = sortedResults

        console.log('所有API服务器测试结果:', sortedResults)
      }, 100)
    }

  } catch (error) {
    // 失败时使用第一个URL
    const defaultUrl = urlList[0]
    axios.defaults.baseURL = defaultUrl
    Vue.prototype.$currentBaseUrl = defaultUrl
    console.log('Production: Default baseURL set to', defaultUrl)
    console.error('Error setting baseURL:', error.message)
    VantToast.fail('网络不佳!')
  }
}

const isProduction = process.env.NODE_ENV === 'production';

// Vue 初始化逻辑
(async () => {
  // if (isProduction) {
  // 确保连接池逻辑优先完成
  await initializeBaseUrl()
  // }

  new Vue({
    el: '#app',
    i18n,
    store,
    router,
    axios,
    render: (h) => h(App)
  }).$mount('#app')
})()

// new Vue({
//   el: '#app',
//   i18n,
//   store,
//   router,
//   axios,
//   render: h => h(App)
// }).$mount('#app')

