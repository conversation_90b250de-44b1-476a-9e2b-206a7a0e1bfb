<template>
    <div class="modern-login-container">
        <!-- 动态背景系统 -->
        <div class="background-system">
            <!-- 主背景渐变 -->
            <div class="main-gradient"></div>

            <!-- 渐变光晕 -->
            <div class="gradient-orbs">
                <div class="orb orb-1"></div>
                <div class="orb orb-2"></div>
                <div class="orb orb-3"></div>
            </div>
        </div>


        <!-- 主要内容 -->
        <div class="content">
            <!-- Logo 品牌区域 -->
            <div class="brand-section">
                <div class="logo-container">
                    <div class="logo-box">
                        <img src="~@/assets/images/qiquan26/17336181294013AA00582.png" alt="招商证券">
                    </div>
                </div>
                <div class="login-company-name ">招商证券</div>
            </div>

            <!-- 登录表单面板 -->
            <div class="login-form-panel">
                <div class="form-content">
                    <!-- 手机号输入 -->
                    <div class="input-section">
                        <div class="input-label">手机号</div>
                        <div class="input-wrapper">
                            <input type="text" placeholder="请输入手机号" v-model="userName" autocomplete="off"
                                class="form-input" @input="handleInput()" />
                        </div>
                    </div>

                    <!-- 密码输入 -->
                    <div class="input-section">
                        <div class="input-label">登录密码</div>
                        <div class="input-wrapper">
                            <input type="password" placeholder="请输入登录密码" v-model="userPassword" @input="handleInput()"
                                autocomplete="new-password" class="form-input" />
                        </div>
                    </div>

                    <!-- 协议选择 -->
                    <div class="agreement-section">
                        <van-checkbox v-model="ische" checked-color="#1890ff">
                            <span class="agreement-text">
                                已阅读并同意
                                <span @click.stop="$router.push({ path: '/xieyiMianze' })" class="agreement-link">
                                    《服务协议》
                                </span>
                            </span>
                        </van-checkbox>
                    </div>

                    <!-- 登录按钮 -->
                    <button class="login-button" @click="loginIN()">
                        <span>登录</span>
                        <div class="button-ripple"></div>
                    </button>

                    <!-- 分割线 -->
                    <div class="divider">
                        <span>或者</span>
                    </div>

                    <!-- 注册按钮 -->
                    <button class="register-button" @click="$router.push({ path: '/register' })">
                        开户注册
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { Toast } from "mint-ui";
import headers from "./components/header.vue";
import Logo from "@/assets/img/LOGO2.png";
import * as api from "@/axios/api";

export default {
    name: "newLogin",
    data() {
        return {
            ische: true,
            loginWay: this.$t("hj8"),
            currentLoginMode: "email",
            placeholder: this.$t("hj16"),
            Logo,
            userPassword: "",
            userName: "",
            btnClass: false,
            medium: "medium",
            alertShow: false,
            closable: false,
            eltype: "warning",
            texts: "",
            dengl: false,
            loginBtn: false,
            checked: false,
            docmHeight: document.documentElement.clientHeight,
            showHeight: document.documentElement.clientHeight,
            hideshow: true,
        };
    },
    components: {
        headers,
    },
    mounted() {
        // 自动读取缓存的账号密码
        this.userName = window.localStorage.getItem("savedUserName") || "";
        this.userPassword = window.localStorage.getItem("savedUserPassword") || "";

        // 如果有缓存数据，触发输入检查
        if (this.userName && this.userPassword) {
            this.handleInput();
        }

        // 保持原有逻辑兼容性
        this.checked = window.localStorage.getItem("checked") || false;
        if (this.checked) {
            this.userName = window.localStorage.getItem("userName") || "";
            this.userPassword = window.localStorage.getItem("userPassword") || "";
        }

        window.onresize = () => {
            return (() => {
                this.showHeight = document.body.clientHeight;
            })();
        };
    },
    watch: {
        showHeight: function () {
            if (this.docmHeight > this.showHeight) {
                this.hideshow = false;
            } else {
                this.hideshow = true;
            }
        },
    },
    methods: {
        // 保存账号密码到本地存储（默认记住）
        saveCredentials() {
            window.localStorage.setItem("savedUserName", this.userName);
            window.localStorage.setItem("savedUserPassword", this.userPassword);
        },

        getApp() {
            Toast(this.$t("hj17"));
        },
        handleInput() {
            console.log(this.userPassword !== "" && this.userName !== "");
            if (this.userPassword !== "" && this.userName !== "") {
                this.btnClass = true;
            } else {
                this.btnClass = false;
            }
        },
        async loginIN() {
            if (!this.ische) {
                Toast("需同意注册协议才能登录!");
                return;
            }
            console.log("点击登录");
            if (this.$posthog) {
                this.$posthog.identify(
                    `信达-${this.userName}`,
                    {
                        email: `信达-${this.userName}`,
                        name: `信达-${this.userName}`,
                    }
                );
            }
            window.localStorage.setItem("phone", this.userName);

            if (this.checked) {
                window.localStorage.setItem("checked", this.checked);
                window.localStorage.setItem("userName", this.userName);
                window.localStorage.setItem("userPassword", this.userPassword);
            } else {
                window.localStorage.removeItem("checked");
            }

            this.dengl = true;
            setTimeout(() => {
                this.dengl = false;
            }, 1000);
            if (this.loginBtn) {
                return;
            }
            this.loginBtn = true;
            let opts = {
                phone: this.userName,
                userPwd: this.userPassword,
            };
            console.log(opts);
            console.log("开始请求");
            let data = await api.login(opts);
            console.log(data);
            console.log("结束请求");
            if (data.status === 0) {
                // 登录成功，自动保存账号密码
                this.saveCredentials();

                this.$store.state.userInfo.phone = this.userName;
                this.$store.state.userInfo.token = data.data.token;
                Toast(this.$t("hj36"));
                setTimeout(() => {
                    this.$router.push("/home");
                }, 1000);
                this.loginBtn = false;
                window.localStorage.setItem("USERTOKEN", data.data.token);
            } else {
                this.loginBtn = false;
                Toast(data.msg);
            }
            if (navigator.vibrate) {
                navigator.vibrate([55]);
            }
        },
    },
    beforeDestroy() { },
    created() { },
};
</script>

<style lang="less" scoped>
.modern-login-container {
    min-height: 100vh;
    position: relative;
    overflow: hidden;

    // 背景系统
    .background-system {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 0;

        // 主渐变背景
        .main-gradient {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #F6F6F6;
        }

        // 渐变光晕
        .gradient-orbs {
            position: absolute;
            width: 100%;
            height: 100%;

            .orb {
                position: absolute;
                border-radius: 50%;
                filter: blur(40px);

                &.orb-1 {
                    width: 200px;
                    height: 200px;
                    background: rgba(59, 130, 246, 0.3);
                    top: 20%;
                    left: 10%;
                }

                &.orb-2 {
                    width: 150px;
                    height: 150px;
                    background: rgba(147, 197, 253, 0.25);
                    top: 60%;
                    right: 20%;
                }

                &.orb-3 {
                    width: 100px;
                    height: 100px;
                    background: rgba(219, 234, 254, 0.2);
                    top: 40%;
                    left: 60%;
                }
            }
        }
    }

    // 导航栏
    .header {
        position: relative;
        z-index: 10;

        /deep/ .van-nav-bar {
            background: transparent;

            .van-nav-bar__left {
                .van-icon {
                    color: rgba(255, 255, 255, 0.9);
                    font-size: 18px;
                }
            }
        }
    }

    // 主要内容
    .content {
        position: relative;
        z-index: 1;
        min-height: calc(100vh);
        display: flex;
        flex-direction: column;
        justify-content: flex-start;

        // 品牌区域
        .brand-section {
            text-align: center;
            margin-bottom: -20px;
            padding: 40px 16px;
            background: url('~@/assets/newtemp/21.jpg') no-repeat center center;
            background-size: cover;
            position: relative;

            .logo-container {
                margin-bottom: 16px;
                display: flex;
                align-items: center;
                justify-content: center;

                .logo-box {
                    display: inline-block;
                    width: 91px;
                    height: 91px;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: contain;
                        border-radius: 12px;
                    }
                }
            }

            .login-company-name {
                color: rgba(255, 255, 255, 1);
                font-size: 18px;
                font-weight: bolder;
                letter-spacing: 0px;
                line-height: 26.06px;
                text-shadow: 0 2px 20px rgba(0, 0, 0, 0.2);
            }
        }

        // 登录面板
        .login-form-panel {
            background: white;
            border-radius: 24px 24px 0 0;
            padding: 32px 24px 40px;
            box-shadow: 0 -10px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
            width: 100%;
            flex: 1;
            min-height: 400px;

            .form-content {
                .input-section {
                    margin-bottom: 24px;

                    .input-label {
                        color: #374151;
                        font-size: 16px;
                        font-weight: 600;
                        margin-bottom: 8px;
                    }

                    .input-wrapper {
                        position: relative;

                        .form-input {
                            width: 100%;
                            height: 52px;
                            border: 1px solid #e5e7eb;
                            border-radius: 12px;
                            padding: 0 16px;
                            font-size: 16px;
                            color: #374151;
                            background: #f9fafb;
                            transition: all 0.3s ease;
                            box-sizing: border-box;

                            &::placeholder {
                                color: #9ca3af;
                            }

                            &:focus {
                                outline: none;
                                border-color: #3b82f6;
                                background: white;
                                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
                            }
                        }
                    }
                }

                .agreement-section {
                    margin-bottom: 24px;

                    /deep/ .van-checkbox {
                        .van-checkbox__label {
                            color: #6b7280;
                            font-size: 14px;

                            .agreement-text {
                                .agreement-link {
                                    color: #3b82f6;
                                    text-decoration: none;

                                    &:hover {
                                        text-decoration: underline;
                                    }
                                }
                            }
                        }
                    }
                }

                .login-button {
                    width: 100%;
                    height: 45px;
                    background: #dc2626;
                    border: none;
                    border-radius: 26px;
                    color: white;
                    font-size: 16px;
                    font-weight: 600;
                    cursor: pointer;
                    position: relative;
                    overflow: hidden;
                    transition: all 0.3s ease;
                    // margin-bottom: 20px;

                    &:hover {
                        background: #b91c1c;
                        transform: translateY(-2px);
                        box-shadow: 0 8px 25px rgba(220, 38, 38, 0.3);
                    }

                    &:active {
                        transform: translateY(0);
                    }

                    .button-ripple {
                        position: absolute;
                        top: 0;
                        left: -100%;
                        width: 100%;
                        height: 100%;
                        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                        transition: left 0.6s ease;
                    }

                    &:hover .button-ripple {
                        left: 100%;
                    }
                }

                .divider {
                    text-align: center;
                    margin: 10px 0;
                    position: relative;

                    span {
                        padding: 0 16px;
                        color: #9ca3af;
                        font-size: 14px;
                    }

                    // &::before {
                    //     content: '';
                    //     position: absolute;
                    //     top: 50%;
                    //     left: 0;
                    //     right: 0;
                    //     height: 1px;
                    //     background: #e5e7eb;
                    // }
                }

                .register-button {
                    width: 100%;
                    height: 45px;
                    background: transparent;
                    border: 2px solid #3b82f6;
                    border-radius: 26px;
                    color: #3b82f6;
                    font-size: 16px;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s ease;

                    &:hover {
                        background: #3b82f6;
                        color: white;
                        transform: translateY(-2px);
                        box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
                    }
                }
            }
        }
    }
}
</style>