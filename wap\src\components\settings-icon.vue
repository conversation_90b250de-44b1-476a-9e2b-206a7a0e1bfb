<template>
    <div class="settings-icon" :class="iconType">
        <!-- 登录密码图标 -->
        <svg v-if="iconType === 'login-password'" class="icon" viewBox="0 0 1024 1024">
            <path
                d="M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zM540 701v53c0 4.4-3.6 8-8 8h-40c-4.4 0-8-3.6-8-8v-53a48.01 48.01 0 1 1 56 0zm152-237H332V240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v224z">
            </path>
        </svg>

        <!-- 资金密码图标 -->
        <svg v-else-if="iconType === 'fund-password'" class="icon" viewBox="0 0 1024 1024">
            <path
                d="M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656zM492 400h184c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H492c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8zm0 144h184c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H492c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8zm0 144h184c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H492c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8zM340 368a40 40 0 1 0 80 0 40 40 0 1 0-80 0zm0 144a40 40 0 1 0 80 0 40 40 0 1 0-80 0zm0 144a40 40 0 1 0 80 0 40 40 0 1 0-80 0z">
            </path>
        </svg>

        <!-- 退出登录图标 -->
        <svg v-else-if="iconType === 'logout'" class="icon" viewBox="0 0 1024 1024">
            <path
                d="M868 732h-70.3c-4.8 0-9.3 2.1-12.3 5.8-7 8.5-14.5 16.7-22.4 24.5a353.84 353.84 0 0 1-112.7 75.9A352.8 352.8 0 0 1 512.4 866c-47.9 0-94.3-9.4-137.9-27.8a353.84 353.84 0 0 1-112.7-75.9 353.28 353.28 0 0 1-76-112.5C167.3 606.2 158 559.9 158 512s9.4-94.2 27.8-137.8c17.8-42.1 43.4-80 76-112.5s70.5-58.1 112.7-75.9c43.6-18.4 90-27.8 137.9-27.8 47.9 0 94.3 9.3 137.9 27.8 42.2 17.8 80.1 43.4 112.7 75.9 7.9 7.9 15.3 16.1 22.4 24.5 3 3.7 7.6 5.8 12.3 5.8H868c6.3 0 10.2-7 6.7-12.3C806 156.7 673.9 81.9 511.9 82 271.7 82.1 79.6 270.7 79.9 510.8 80.2 751.4 271.9 942.9 512.4 942.9c161.8 0 293.8-74.8 362.3-189.6 3.4-5.3-.4-12.3-6.7-12.3z">
            </path>
        </svg>

        <!-- 默认图标 -->
        <svg v-else class="icon" viewBox="0 0 1024 1024">
            <path
                d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z">
            </path>
        </svg>
    </div>
</template>

<script>
export default {
    name: "SettingsIcon",
    props: {
        iconType: {
            type: String,
            required: true,
            validator(value) {
                return ['login-password', 'fund-password', 'logout'].includes(value);
            }
        }
    }
};
</script>

<style lang="less" scoped>
.settings-icon {
    width: 1.0232rem;
    height: 1.0232rem;
    border-radius: 0.1860rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid;
    transition: all 0.3s ease;

    .icon {
        width: 0.5116rem;
        height: 0.5116rem;
        transition: all 0.3s ease;
    }

    &.login-password {
        background: rgba(52, 152, 219, 0.1);
        border-color: rgba(52, 152, 219, 0.2);

        .icon {
            fill: #3498db;
        }
    }

    &.fund-password {
        background: rgba(46, 204, 113, 0.1);
        border-color: rgba(46, 204, 113, 0.2);

        .icon {
            fill: #2ecc71;
        }
    }

    &.logout {
        background: rgba(149, 165, 166, 0.1);
        border-color: rgba(149, 165, 166, 0.2);

        .icon {
            fill: #95a5a6;
        }
    }
}
</style>