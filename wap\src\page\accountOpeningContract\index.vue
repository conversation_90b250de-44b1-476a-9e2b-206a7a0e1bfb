<template>
    <div class="account-opening-contract-page">
        <div class="headf">
            <div>
                <h2>
                    <span class="hbnh"><a class="fan" @click="$router.back()"></a></span>合同
                </h2>
            </div>
        </div>
        <div class="contract-list">
            <div class="contract-item" @click="goToDetail(1)">
                <div class="contract-left">
                    <i class="icon-doc"></i>
                    <span class="contract-title">证券投资顾问咨询服务协议</span>
                </div>
                <div class="status-tag" :class="signatureStatus.type1 ? 'signed' : 'unsigned'">
                    {{ signatureStatus.type1 ? '已签' : '未签' }}
                </div>
            </div>

            <div class="contract-item" @click="goToDetail(2)">
                <div class="contract-left">
                    <i class="icon-doc"></i>
                    <span class="contract-title">商业核心信息保密协议书</span>
                </div>
                <div class="status-tag" :class="signatureStatus.type2 ? 'signed' : 'unsigned'">
                    {{ signatureStatus.type2 ? '已签' : '未签' }}
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import contract from '@/components/contract/index'
import * as api from '@/axios/api'
export default {
    name: 'accountOpeningContract',
    components: {
        contract
    },
    data() {
        return {
            id: '',
            signatureStatus: {
                type1: false,
                type2: false
            }
        }
    },
    created() {
        const { id } = this.$route.query
        this.id = id
    },
    async mounted() {
        await this.getSignatureStatus()
    },
    methods: {
        goToDetail(type) {
            this.$router.push(`accountOpeningContractDetail?id=${this.id}&type=${type}`)
        },
        async getSignatureStatus() {
            // 获取第一个协议签名状态
            const res1 = await api.getSignature({
                userId: this.id,
                type: 1
            })

            if (res1.data) {
                this.signatureStatus.type1 = !!res1.data.signatureMsg
            }

            // 获取第二个协议签名状态
            const res2 = await api.getSignature({
                userId: this.id,
                type: 2
            })

            if (res2.data) {
                this.signatureStatus.type2 = !!res2.data.signatureMsg
            }
        }
    }
}
</script>

<style scoped lang="less">
.account-opening-contract-page {
    .headf {
        width: 100%;
        height: 1.1748rem;
        background: linear-gradient(-55deg, #ff4d4f, #ff4d4f);
        position: sticky;
        top: 0;
        z-index: 10;
    }

    h2 {
        text-align: center;
        height: 1.2549rem;
        width: 100%;
        position: relative;
        line-height: 1.2549rem;
        font-size: 0.4806rem;
        color: #fff;
        background: transparent;
        font-weight: 500;
        z-index: 3;
    }

    .hbnh {
        position: absolute;
        left: 0.4005rem;
        font-size: 0.4272rem;
        font-weight: 500;
    }

    .fan {
        width: 0.2403rem;
        height: 0.4272rem;
        background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAgCAYAAAAffCjxAAAAAXNSR0IArs4c6QAAAVdJREFUSEut1usqBVEYxvH/c1MuRJJDkhwTEpIkJUlyCEk++CJJckpycw49Gs3Wa+y9Z82ePd/Xr5n38KwRNR7bE0APMK1OHdtTwAWQGZcdQbZngPMc+QImK0O2Z4GzgIxLuqoE2Z4DTgMyJuk6K08yZHseOAnIqKSbRo2TINsLwHFARiTdxkaVQrYXgaOADEu6K3a7LWR7CTjMD2XdaYq0rZHtFWA/Rz6BIUn3reau6RvZXgX28kMfOfLQbnj/QbbXgN380HuOPJZtwB/I9jqwE5ABSc9lyJ8a2d4AtgPSL+klBfmFbG8CWwHpk/SaivxAtjMgg7Inq0mvpLcqSHehTOvKpzU+oSvFDlj99gesOJCDkp7Kip+yIknT3XL7C0tbum9lMbIMHKQkQEqwJWVSKZTPWTElq0dt6GYxt6uHf8DqX0cBq39BBqz+lR2w+j8RAfv9rfkGqF24CUdT9E4AAAAASUVORK5CYII=) no-repeat 50%;
        background-size: 100%;
        display: inline-block;
        margin-right: 0.1335rem;
        vertical-align: middle;
        margin-top: -0.0534rem;
    }

    .contract-list {
        padding: 0.4rem;
    }

    .contract-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem;
        margin-bottom: 0.3rem;
        border-radius: 0.2rem;
        background-color: #fff;
        border-bottom: 1px solid #f5f5f5;
    }

    .contract-left {
        display: flex;
        align-items: center;
    }

    .icon-doc {
        width: 0.6rem;
        height: 0.6rem;
        margin-right: 0.3rem;
        background: url(data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIj48cGF0aCBkPSJNNjQwIDUxMkg0NDhhMzIgMzIgMCAxIDEgMC02NGgxOTJhMzIgMzIgMCAwIDEgMCA2NHpNNjQwIDM1Mkg0NDhhMzIgMzIgMCAwIDEgMC02NGgxOTJhMzIgMzIgMCAwIDEgMCA2NHpNNjQwIDY3Mkg0NDhhMzIgMzIgMCAwIDEgMC02NGgxOTJhMzIgMzIgMCAwIDEgMCA2NHoiIGZpbGw9IiMzNDk2RkYiLz48cGF0aCBkPSJNMzIwIDM1MkgxOTJhMzIgMzIgMCAwIDEgMC02NGgxMjhhMzIgMzIgMCAwIDEgMCA2NHpNMzIwIDUxMkgxOTJhMzIgMzIgMCAwIDEgMC02NGgxMjhhMzIgMzIgMCAwIDEgMCA2NHpNMzIwIDY3MkgxOTJhMzIgMzIgMCAwIDEgMC02NGgxMjhhMzIgMzIgMCAwIDEgMCA2NHpNODk2IDI0MEg3NjhhMzIgMzIgMCAwIDEtMzItMzJWOTZhMzIgMzIgMCAwIDAtNTQuNjU2LTIyLjY1NmwtNzAuNjg4IDcwLjY4OEwxMjcuODQgMTUxLjg0QTY0IDY0IDAgMCAwIDk2IDIxMi42ODR2NTk4LjYzMmE2NCA2NCAwIDAgMCA2NCA2NEg4NjRhNjQgNjQgMCAwIDAgNjQtNjRWMjcyYTMyIDMyIDAgMCAwLTMyLTMyeiIgZmlsbD0iI0UxRjFGRiIvPjxwYXRoIGQ9Ik04OTYgMjQwSDc2OGEzMiAzMiAwIDAgMS0zMi0zMlY5NmEzMiAzMiAwIDAgMC01NC42NTYtMjIuNjU2bC0yNy4zMTIgMjcuMzEyQzY0Ny4xNjggOTQuNDk2IDY0MSA4Ni41MjggNjQxIDc2LjY4OFY0OGEzMiAzMiAwIDAgMC0zMi0zMkgxNjBhNjQgNjQgMCAwIDAtNjQgNjR2NTk4LjYzMmE2NCA2NCAwIDAgMCA2NCA2NEg4NjRhNjQgNjQgMCAwIDAgNjQtNjRWMjcyYTMyIDMyIDAgMCAwLTMyLTMyek03NjggMjQwSDEyOHY1NzZoNzY4VjI0MEg3Njh6IG0tMzItMTEyLjAzMlYxOTRoLTY0di02NHoiIGZpbGw9IiMzNDk2RkYiLz48L3N2Zz4=) no-repeat center;
        background-size: contain;
    }

    .contract-title {
        font-size: 0.35rem;
        color: #333;
    }

    .status-tag {
        padding: 0.1rem 0.3rem;
        border-radius: 0.5rem;
        font-size: 0.28rem;
    }

    .signed {
        background-color: #00c853;
        color: #fff;
    }

    .unsigned {
        background-color: #f5f5f5;
        color: #999;
    }
}
</style>