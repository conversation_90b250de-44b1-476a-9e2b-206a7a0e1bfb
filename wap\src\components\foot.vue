<template>
    <div class="foot">
        <van-tabbar v-model="active">
            <van-tabbar-item @click="goRouter('/home', 0)">
                <span>首页</span>
                <template #icon="props">
                    <img v-if="props.active" src="~@/assets/foot/11.png" />
                    <img v-else src="~@/assets/foot/1.png" />
                </template>
            </van-tabbar-item>
            <van-tabbar-item @click="goRouter('/trading-list', 1)">
                <span>行情</span>
                <template #icon="props">
                    <img v-if="props.active" src="~@/assets/foot/22.png" />
                    <img v-else src="~@/assets/foot/2.png" />
                </template>
            </van-tabbar-item>
            <van-tabbar-item @click="goRouter('/jiaoyi', 2)">
                <span>交易</span>
                <template #icon="props">
                    <img v-if="props.active" src="~@/assets/foot/33.png" />
                    <img v-else src="~@/assets/foot/3.png" />
                </template>
            </van-tabbar-item>
            <van-tabbar-item @click="goRouter('/user', 3)">
                <span>个人中心</span>
                <template #icon="props">
                    <img v-if="props.active" src="~@/assets/foot/44.png" />
                    <img v-else src="~@/assets/foot/4.png" />
                </template>
            </van-tabbar-item>
        </van-tabbar>
    </div>
</template>

<script>
export default {
    data() {
        return {
            active: 0,
            routeMap: {
                '/home': 0,
                '/trading-list': 1,
                '/jiaoyi': 2,
                '/user': 3
            }
        }
    },
    methods: {
        goRouter(url, index) {
            this.active = index
            this.$router.push({
                path: url
            })
        },
        updateActive() {
            // 获取当前路径
            const currentPath = this.$route.path

            // 检查当前路径是否在映射表中
            if (this.routeMap.hasOwnProperty(currentPath)) {
                this.active = this.routeMap[currentPath]
            } else {
                // 如果不在映射表中，检查是否是子路径
                if (currentPath.startsWith('/home')) {
                    this.active = 0
                } else if (currentPath.includes('list') || currentPath.includes('search')) {
                    this.active = 1
                } else if (currentPath.includes('jiaoyi') || currentPath.includes('buy') || currentPath.includes('chicang')) {
                    this.active = 2
                } else if (currentPath.includes('user') || currentPath.includes('wallet') ||
                    currentPath.includes('card') || currentPath.includes('auth')) {
                    this.active = 3
                }
            }
        }
    },
    created() {
        // 组件创建时更新active状态
        this.updateActive()
    },
    mounted() {
        // 组件挂载时更新active状态
        this.updateActive()
    },
    watch: {
        // 监听路由变化，更新active状态
        '$route'() {
            this.updateActive()
        }
    }
}
</script>

<style lang="less" scoped>
.foot {
    height: 1.3488rem;
    width: 100%;
    padding: 0;
    padding-bottom: env(safe-area-inset-bottom, 0px);
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #fff;
    z-index: 1000;
    /* 添加边框来区分内容区域 */
    border-top: 1px solid #f0f0f0;

    .van-tabbar {
        height: 1.3488rem;
        /* 确保tabbar适应安全区域 */
        padding-bottom: env(safe-area-inset-bottom, 0px);
    }
}

.van-tabbar-item--active {
    color: rgba(238, 0, 17, 1);
}
</style>
