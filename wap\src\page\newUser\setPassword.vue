<template>
    <div class="set-password-page">
        <div class="header">
            <van-nav-bar :title="userInfo.hadWithdrawPwd ? '修改资金密码' : '设置资金密码'" left-arrow fixed
                @click-left="handleGoBack" />
        </div>

        <div class="password-form">
            <!-- 表单说明 -->
            <div class="form-intro">
                <div class="intro-icon">🔐</div>
                <div class="intro-text">
                    <div class="intro-title">{{ userInfo.hadWithdrawPwd ? '修改资金密码' : '设置资金密码' }}</div>
                    <div class="intro-desc">资金密码用于提现和重要操作验证，请妥善保管</div>
                </div>
            </div>

            <!-- 旧密码输入 -->
            <div class="form-group" v-if="userInfo.hadWithdrawPwd">
                <div class="form-label">
                    <span class="label-text">当前密码</span>
                    <span class="label-required">*</span>
                </div>
                <div class="input-wrapper" :class="{ 'error': errors.oldWithPwd, 'focused': focusStates.oldWithPwd }">
                    <div class="input-icon">
                        <svg class="icon" viewBox="0 0 1024 1024">
                            <path
                                d="M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32z">
                            </path>
                        </svg>
                    </div>
                    <input :type="passwordVisibility.oldWithPwd ? 'text' : 'password'" placeholder="请输入6位数字当前密码"
                        v-model="formData.oldWithPwd" maxlength="6" @focus="handleFocus('oldWithPwd')"
                        @blur="handleBlur('oldWithPwd')" @input="clearError('oldWithPwd')" :disabled="isSubmitting" />
                    <div class="password-toggle" @click="togglePasswordVisibility('oldWithPwd')"
                        v-if="formData.oldWithPwd">
                        <van-icon :name="passwordVisibility.oldWithPwd ? 'eye-o' : 'eye'" />
                    </div>
                </div>
                <div class="error-message" v-if="errors.oldWithPwd">{{ errors.oldWithPwd }}</div>
            </div>

            <!-- 新密码输入 -->
            <div class="form-group">
                <div class="form-label">
                    <span class="label-text">新密码</span>
                    <span class="label-required">*</span>
                </div>
                <div class="input-wrapper" :class="{ 'error': errors.newWithPwd, 'focused': focusStates.newWithPwd }">
                    <div class="input-icon">
                        <svg class="icon" viewBox="0 0 1024 1024">
                            <path
                                d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 384c-88.4 0-160-71.6-160-160s71.6-160 160-160 160 71.6 160 160-71.6 160-160 160z">
                            </path>
                            <path
                                d="M623.6 316.7C593.6 290.4 554.9 276 512 276s-81.6 14.4-111.6 40.7C369.2 344 351.9 382.7 351.9 424c0 41.3 17.3 80 48.5 107.3C430.4 557.6 469.1 572 512 572s81.6-14.4 111.6-40.7C654.8 504 672.1 465.3 672.1 424c0-41.3-17.3-80-48.5-107.3z">
                            </path>
                        </svg>
                    </div>
                    <input :type="passwordVisibility.newWithPwd ? 'text' : 'password'" placeholder="请设置6位数字密码"
                        v-model="formData.newWithPwd" maxlength="6" @focus="handleFocus('newWithPwd')"
                        @blur="handleBlur('newWithPwd')" @input="handleNewPasswordInput" :disabled="isSubmitting" />
                    <div class="password-toggle" @click="togglePasswordVisibility('newWithPwd')"
                        v-if="formData.newWithPwd">
                        <van-icon :name="passwordVisibility.newWithPwd ? 'eye-o' : 'eye'" />
                    </div>
                </div>
                <div class="error-message" v-if="errors.newWithPwd">{{ errors.newWithPwd }}</div>

                <!-- 密码强度指示器 -->
                <div class="password-strength" v-if="formData.newWithPwd">
                    <div class="strength-label">密码强度：</div>
                    <div class="strength-bars">
                        <div class="strength-bar"
                            :class="{ active: passwordStrength >= 1, weak: passwordStrength === 1 }"></div>
                        <div class="strength-bar"
                            :class="{ active: passwordStrength >= 2, strong: passwordStrength === 2 }"></div>
                    </div>
                    <div class="strength-text" :class="strengthClass">{{ strengthText }}</div>
                </div>
            </div>

            <!-- 确认密码输入 -->
            <div class="form-group">
                <div class="form-label">
                    <span class="label-text">确认新密码</span>
                    <span class="label-required">*</span>
                </div>
                <div class="input-wrapper"
                    :class="{ 'error': errors.cirNewPassword, 'focused': focusStates.cirNewPassword }">
                    <div class="input-icon">
                        <svg class="icon" viewBox="0 0 1024 1024">
                            <path
                                d="M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0 0 51.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z">
                            </path>
                            <path
                                d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z">
                            </path>
                        </svg>
                    </div>
                    <input :type="passwordVisibility.cirNewPassword ? 'text' : 'password'" placeholder="请再次输入新密码"
                        v-model="formData.cirNewPassword" maxlength="6" @focus="handleFocus('cirNewPassword')"
                        @blur="handleBlur('cirNewPassword')" @input="handleConfirmPasswordInput"
                        :disabled="isSubmitting" />
                    <div class="password-toggle" @click="togglePasswordVisibility('cirNewPassword')"
                        v-if="formData.cirNewPassword">
                        <van-icon :name="passwordVisibility.cirNewPassword ? 'eye-o' : 'eye'" />
                    </div>
                </div>
                <div class="error-message" v-if="errors.cirNewPassword">{{ errors.cirNewPassword }}</div>
                <div class="success-message" v-if="isPasswordMatched && formData.cirNewPassword">密码匹配</div>
            </div>

            <!-- 提交按钮 -->
            <div class="submit-button" @click="handleSubmit"
                :class="{ 'loading': isSubmitting, 'disabled': !isFormValid }">
                <van-loading v-if="isSubmitting" size="20px" color="#fff" />
                <span v-else>{{ isSubmitting ? '提交中...' : '确认设置' }}</span>
            </div>

            <!-- 密码规则提示 -->
            <div class="password-rules">
                <div class="rules-title">密码规则：</div>
                <div class="rules-list">
                    <div class="rule-item" :class="{ 'valid': passwordRules.length }">
                        <span class="rule-icon">{{ passwordRules.length ? '✓' : '○' }}</span>
                        <span class="rule-text">密码长度为6位数字</span>
                    </div>
                    <div class="rule-item" :class="{ 'valid': passwordRules.digital }">
                        <span class="rule-icon">{{ passwordRules.digital ? '✓' : '○' }}</span>
                        <span class="rule-text">仅包含数字0-9</span>
                    </div>
                </div>
            </div>

            <!-- 安全提示 -->
            <div class="security-tips">
                <div class="tips-title">安全提示：</div>
                <div class="tips-list">
                    <div class="tip-item">
                        <span class="tip-icon">🔒</span>
                        <span class="tip-text">资金密码用于提现和重要资金操作</span>
                    </div>
                    <div class="tip-item">
                        <span class="tip-icon">🚫</span>
                        <span class="tip-text">请勿将密码告知他人，定期修改密码</span>
                    </div>
                    <div class="tip-item">
                        <span class="tip-icon">📞</span>
                        <span class="tip-text">如忘记密码，请联系客服处理</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import * as api from "@/axios/api";
import { isNull } from "@/utils/utils";
import { Toast, Dialog } from "vant";

export default {
    name: "SetPassword",
    data() {
        return {
            userInfo: {},
            isSubmitting: false,
            formData: {
                oldWithPwd: "",
                newWithPwd: "",
                cirNewPassword: "",
            },
            passwordVisibility: {
                oldWithPwd: false,
                newWithPwd: false,
                cirNewPassword: false,
            },
            focusStates: {
                oldWithPwd: false,
                newWithPwd: false,
                cirNewPassword: false,
            },
            errors: {},
        };
    },

    computed: {
        // 密码强度计算
        passwordStrength() {
            const password = this.formData.newWithPwd;
            if (!password) return 0;

            let strength = 0;

            // 长度检查
            if (password.length === 6) strength++;

            // 数字检查
            if (/^\d+$/.test(password)) strength++;

            return Math.min(strength, 2);
        },

        // 强度等级类名
        strengthClass() {
            switch (this.passwordStrength) {
                case 1: return 'weak';
                case 2: return 'strong';
                default: return '';
            }
        },

        // 强度文本
        strengthText() {
            switch (this.passwordStrength) {
                case 1: return '弱';
                case 2: return '强';
                default: return '';
            }
        },

        // 密码规则验证
        passwordRules() {
            const password = this.formData.newWithPwd;
            return {
                length: password.length === 6,
                digital: /^\d+$/.test(password),
            };
        },

        // 密码是否匹配
        isPasswordMatched() {
            return this.formData.newWithPwd &&
                this.formData.cirNewPassword &&
                this.formData.newWithPwd === this.formData.cirNewPassword;
        },

        // 表单是否有效
        isFormValid() {
            const { oldWithPwd, newWithPwd, cirNewPassword } = this.formData;

            // 如果需要旧密码且为空，返回false
            if (this.userInfo.hadWithdrawPwd && !oldWithPwd) return false;

            // 新密码为空或不符合规则
            if (!newWithPwd || this.passwordStrength < 2) return false;

            // 确认密码为空或不一致
            if (!cirNewPassword || newWithPwd !== cirNewPassword) return false;

            // 检查是否有错误
            return Object.keys(this.errors).length === 0;
        }
    },

    async created() {
        await this.getUserInfo();
    },

    methods: {
        // 获取用户信息
        async getUserInfo() {
            const toastLoading = Toast.loading({
                message: "加载中...",
                duration: 0,
                forbidClick: true,
            });

            try {
                const data = await api.getUserInfo();
                if (data.status === 0) {
                    this.userInfo = data.data;
                } else {
                    Toast.fail(data.msg || "获取用户信息失败");
                    this.$router.push("/login");
                }
            } catch (error) {
                console.error("获取用户信息失败:", error);
                Toast.fail("网络错误，请重试");
            } finally {
                toastLoading.clear();
            }
        },

        // 返回上一页
        handleGoBack() {
            if (this.hasFormData()) {
                Dialog.confirm({
                    title: '确认离开',
                    message: '您有未保存的修改，确定要离开吗？',
                })
                    .then(() => {
                        this.$router.go(-1);
                    })
                    .catch(() => {
                        // 用户取消
                    });
            } else {
                this.$router.go(-1);
            }
        },

        // 检查是否有表单数据
        hasFormData() {
            const { oldWithPwd, newWithPwd, cirNewPassword } = this.formData;
            return oldWithPwd || newWithPwd || cirNewPassword;
        },

        // 处理焦点状态
        handleFocus(field) {
            this.focusStates[field] = true;
        },

        handleBlur(field) {
            this.focusStates[field] = false;
            this.validateField(field);
        },

        // 切换密码可见性
        togglePasswordVisibility(field) {
            this.passwordVisibility[field] = !this.passwordVisibility[field];
        },

        // 清除错误
        clearError(field) {
            if (this.errors[field]) {
                this.$delete(this.errors, field);
            }
        },

        // 处理新密码输入
        handleNewPasswordInput() {
            this.clearError('newWithPwd');
            if (this.formData.cirNewPassword) {
                this.validateField('cirNewPassword');
            }
        },

        // 处理确认密码输入
        handleConfirmPasswordInput() {
            this.clearError('cirNewPassword');
        },

        // 字段验证
        validateField(field) {
            switch (field) {
                case 'oldWithPwd':
                    if (!this.formData.oldWithPwd.trim()) {
                        this.$set(this.errors, field, '请输入当前密码');
                    } else if (!/^\d{6}$/.test(this.formData.oldWithPwd)) {
                        this.$set(this.errors, field, '当前密码格式不正确');
                    }
                    break;

                case 'newWithPwd':
                    if (!this.formData.newWithPwd.trim()) {
                        this.$set(this.errors, field, '请输入新密码');
                    } else if (!/^\d{6}$/.test(this.formData.newWithPwd)) {
                        this.$set(this.errors, field, '密码必须为6位数字');
                    } else if (this.userInfo.hadWithdrawPwd && this.formData.newWithPwd === this.formData.oldWithPwd) {
                        this.$set(this.errors, field, '新密码不能与当前密码相同');
                    }
                    break;

                case 'cirNewPassword':
                    if (!this.formData.cirNewPassword.trim()) {
                        this.$set(this.errors, field, '请再次输入新密码');
                    } else if (this.formData.cirNewPassword !== this.formData.newWithPwd) {
                        this.$set(this.errors, field, '两次输入的密码不一致');
                    }
                    break;
            }
        },

        // 表单验证
        validateForm() {
            this.errors = {};

            // 验证所有字段
            const fieldsToValidate = ['newWithPwd', 'cirNewPassword'];
            if (this.userInfo.hadWithdrawPwd) {
                fieldsToValidate.unshift('oldWithPwd');
            }

            fieldsToValidate.forEach(field => {
                this.validateField(field);
            });

            return Object.keys(this.errors).length === 0;
        },

        // 处理提交
        async handleSubmit() {
            if (!this.isFormValid || this.isSubmitting) {
                return;
            }

            if (!this.validateForm()) {
                return;
            }

            this.isSubmitting = true;

            try {
                await this.executePasswordChange();
            } catch (error) {
                this.handleSubmitError(error);
            } finally {
                this.isSubmitting = false;
                this.triggerVibration();
            }
        },

        // 执行密码修改
        async executePasswordChange() {
            const params = {
                newWithPwd: this.formData.newWithPwd,
            };

            // 如果是修改密码，添加旧密码
            if (this.userInfo.hadWithdrawPwd) {
                params.oldWithPwd = this.formData.oldWithPwd;
            }

            const response = await api.insertWithPwd(params);
            this.handlePasswordChangeResponse(response);
        },

        // 处理密码修改响应
        handlePasswordChangeResponse(response) {
            if (!response) {
                throw new Error('服务器无响应');
            }

            if (response.status === 0) {
                Toast.success(response.msg || '密码设置成功');
                setTimeout(() => {
                    this.navigateToUser();
                }, 1500);
            } else {
                const errorMsg = response.msg || '操作失败';
                Toast.fail(errorMsg);

                // 根据错误类型设置具体错误
                if (errorMsg.includes('当前密码') || errorMsg.includes('旧密码')) {
                    this.$set(this.errors, 'oldWithPwd', errorMsg);
                }
            }
        },

        // 处理提交错误
        handleSubmitError(error) {
            console.error('密码设置失败:', error);

            if (error.response) {
                switch (error.response.status) {
                    case 401:
                        Toast.fail('登录已过期，请重新登录');
                        break;
                    case 403:
                        Toast.fail('没有操作权限');
                        break;
                    case 500:
                        Toast.fail('服务器异常，请稍后重试');
                        break;
                    default:
                        Toast.fail('网络请求失败');
                }
            } else {
                Toast.fail(error.message || '操作失败，请重试');
            }
        },

        // 导航到用户页面
        navigateToUser() {
            try {
                this.$router.push({ path: "/user" });
            } catch (error) {
                console.error('导航失败:', error);
                this.$router.go(-1);
            }
        },

        // 触发震动反馈
        triggerVibration() {
            if (navigator.vibrate) {
                navigator.vibrate([55]);
            }
        },
    },
};
</script>

<style lang="less" scoped>
.set-password-page {
    background: #f5f5f5;
    min-height: 100vh;
    font-size: 0.3256rem;

    .header {
        width: 100%;
        height: 1.07rem;
    }

    .password-form {
        padding: 0.4651rem 0.2326rem;

        .form-intro {
            background: #fff;
            border-radius: 0.1860rem;
            padding: 0.4651rem;
            margin-bottom: 0.4651rem;
            display: flex;
            align-items: center;
            gap: 0.3488rem;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

            .intro-icon {
                font-size: 1.1627rem;
                background: linear-gradient(135deg, #EA001B, #FF6B3D);
                width: 1.3953rem;
                height: 1.3953rem;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 0 4px 12px rgba(234, 0, 27, 0.3);
            }

            .intro-text {
                flex: 1;

                .intro-title {
                    font-size: 0.4186rem;
                    font-weight: 600;
                    color: #2c3e50;
                    margin-bottom: 0.1162rem;
                }

                .intro-desc {
                    font-size: 0.3256rem;
                    color: #7f8c8d;
                    line-height: 1.4;
                }
            }
        }

        .form-group {
            background: #fff;
            border-radius: 0.1860rem;
            padding: 0.4651rem;
            margin-bottom: 0.2326rem;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

            .form-label {
                display: flex;
                align-items: center;
                margin-bottom: 0.2326rem;

                .label-text {
                    font-size: 0.3721rem;
                    font-weight: 600;
                    color: #2c3e50;
                }

                .label-required {
                    color: #EA001B;
                    margin-left: 0.0930rem;
                    font-size: 0.3721rem;
                }
            }

            .input-wrapper {
                position: relative;
                display: flex;
                align-items: center;
                background: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 0.1860rem;
                transition: all 0.3s ease;

                &.focused {
                    border-color: #EA001B;
                    background: #fff;
                    box-shadow: 0 0 0 0.0697rem rgba(234, 0, 27, 0.1);
                }

                &.error {
                    border-color: #dc3545;
                    background: #fff5f5;
                }

                .input-icon {
                    padding: 0 0.2326rem;

                    .icon {
                        width: 0.4651rem;
                        height: 0.4651rem;
                        fill: #95a5a6;
                        transition: all 0.3s ease;
                    }
                }

                input {
                    flex: 1;
                    height: 1.1627rem;
                    padding: 0 0.2326rem;
                    border: none;
                    background: transparent;
                    font-size: 0.3488rem;
                    color: #2c3e50;

                    &::placeholder {
                        color: #bdc3c7;
                    }

                    &:disabled {
                        color: #95a5a6;
                        cursor: not-allowed;
                    }
                }

                .password-toggle {
                    padding: 0 0.2326rem;
                    cursor: pointer;

                    .van-icon {
                        font-size: 0.4651rem;
                        color: #95a5a6;
                        transition: all 0.3s ease;

                        &:hover {
                            color: #EA001B;
                        }
                    }
                }

                &.focused .input-icon .icon {
                    fill: #EA001B;
                }
            }

            .error-message {
                color: #dc3545;
                font-size: 0.3023rem;
                margin-top: 0.1162rem;
                display: flex;
                align-items: center;
                gap: 0.0930rem;

                &::before {
                    content: '⚠';
                    color: #dc3545;
                }
            }

            .success-message {
                color: #28a745;
                font-size: 0.3023rem;
                margin-top: 0.1162rem;
                display: flex;
                align-items: center;
                gap: 0.0930rem;

                &::before {
                    content: '✓';
                    color: #28a745;
                }
            }

            .password-strength {
                margin-top: 0.2326rem;
                display: flex;
                align-items: center;
                gap: 0.2326rem;

                .strength-label {
                    font-size: 0.3023rem;
                    color: #7f8c8d;
                }

                .strength-bars {
                    display: flex;
                    gap: 0.0697rem;

                    .strength-bar {
                        width: 0.4651rem;
                        height: 0.0930rem;
                        background: #e9ecef;
                        border-radius: 0.0465rem;
                        transition: all 0.3s ease;

                        &.active.weak {
                            background: #dc3545;
                        }

                        &.active.strong {
                            background: #28a745;
                        }
                    }
                }

                .strength-text {
                    font-size: 0.3023rem;
                    font-weight: 600;

                    &.weak {
                        color: #dc3545;
                    }

                    &.strong {
                        color: #28a745;
                    }
                }
            }
        }

        .submit-button {
            background: linear-gradient(135deg, #EA001B, #FF6B3D);
            color: #fff;
            border-radius: 0.1860rem;
            height: 1.1627rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.3721rem;
            font-weight: 600;
            margin: 0.4651rem 0;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(234, 0, 27, 0.3);

            &:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(234, 0, 27, 0.4);
            }

            &:active {
                transform: translateY(0);
            }

            &.disabled {
                background: #bdc3c7;
                box-shadow: none;
                cursor: not-allowed;

                &:hover {
                    transform: none;
                    box-shadow: none;
                }
            }

            &.loading {
                cursor: not-allowed;

                span {
                    margin-left: 0.2326rem;
                }
            }
        }

        .password-rules {
            background: #fff;
            border-radius: 0.1860rem;
            padding: 0.4651rem;
            margin-bottom: 0.2326rem;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

            .rules-title {
                font-size: 0.3488rem;
                font-weight: 600;
                color: #2c3e50;
                margin-bottom: 0.2326rem;
            }

            .rules-list {
                .rule-item {
                    display: flex;
                    align-items: center;
                    gap: 0.1860rem;
                    margin-bottom: 0.1162rem;
                    transition: all 0.3s ease;

                    &:last-child {
                        margin-bottom: 0;
                    }

                    .rule-icon {
                        width: 0.4651rem;
                        text-align: center;
                        font-size: 0.3256rem;
                        color: #bdc3c7;
                        transition: all 0.3s ease;
                    }

                    .rule-text {
                        font-size: 0.3256rem;
                        color: #7f8c8d;
                        transition: all 0.3s ease;
                    }

                    &.valid {
                        .rule-icon {
                            color: #28a745;
                        }

                        .rule-text {
                            color: #2c3e50;
                        }
                    }
                }
            }
        }

        .security-tips {
            background: #fff;
            border-radius: 0.1860rem;
            padding: 0.4651rem;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

            .tips-title {
                font-size: 0.3488rem;
                font-weight: 600;
                color: #2c3e50;
                margin-bottom: 0.2326rem;
            }

            .tips-list {
                .tip-item {
                    display: flex;
                    align-items: flex-start;
                    gap: 0.1860rem;
                    margin-bottom: 0.1162rem;
                    transition: all 0.3s ease;

                    &:last-child {
                        margin-bottom: 0;
                    }

                    .tip-icon {
                        font-size: 0.3256rem;
                        margin-top: 0.0465rem;
                    }

                    .tip-text {
                        font-size: 0.3256rem;
                        color: #7f8c8d;
                        line-height: 1.4;
                    }
                }
            }
        }
    }
}

/* 响应式适配 */
@media (max-width: 375px) {
    .set-password-page {
        .password-form {
            padding: 0.2326rem 0.1162rem;

            .form-intro {
                padding: 0.3488rem;

                .intro-icon {
                    width: 1.1627rem;
                    height: 1.1627rem;
                    font-size: 0.9302rem;
                }
            }

            .form-group {
                padding: 0.3488rem;
            }
        }
    }
}
</style>
