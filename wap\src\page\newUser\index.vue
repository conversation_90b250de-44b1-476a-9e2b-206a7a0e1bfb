<template>
    <div class="container">
        <!-- <div class="header">
            <van-icon name="setting" color="#1e2433" @click="$router.push({ path: '/setting' })" />
        </div> -->
        <div class="top">
            <div class="flex-between">
                <div class="avatar">
                    <img src="~@/assets/images/qiquan26/17336181294013AA00582.png" />
                </div>
                <div class="info">
                    <div class="text_1">
                        <div>{{ maskPhoneNumber(userInfo.phone ? userInfo.phone : userInfo.phone) }}</div>
                        <div :class="`text_1_status ${userInfo.isActive == 2 ? 'success' : ''}`">{{ userInfo.isActive ==
                            2 ?
                            '已实名' :
                            '未实名' }}</div>
                    </div>
                    <div class="text_2">{{ maskRealName(userInfo.realName) }}</div>
                </div>
            </div>
            <div>
                <van-icon name="setting-o" color="#1e2433" @click="$router.push({ path: '/setting' })" />
            </div>
        </div>
        <div class="layout">
            <div class="wallet">
                <div class="real"></div>
                <div class="wallet_container">
                    <div class="title">
                        <div>
                            <div class="text">
                                总市值（元）
                                <span class="eye-toggle" @click="toggleMoneyDisplay">
                                    <van-icon v-if="isShowMoney" name="eye-o" class="eye-icon" />
                                    <van-icon v-else name="closed-eye" class="eye-icon" />
                                </span>
                            </div>
                            <div class="item">{{ isShowMoney ? useFormatMoney(($store.state.userInfo.userAmt || 0)) :
                                '***' }}</div>
                        </div>
                        <div class="action">
                            <div class="abtn" @click="rechargeEvent()">银转证</div>
                            <div class="abtn" @click="$router.push('/withdraw')">证转银</div>
                        </div>
                    </div>
                    <div class="radioa">
                        <div class="item">
                            <p>可用余额</p>
                            <span>{{ isShowMoney ? useFormatMoney($store.state.userInfo.enableAmt || 0) : '******'
                                }}</span>
                        </div>
                        <div class="item">
                            <p>新股申购</p>
                            <span>{{ isShowMoney ? useFormatMoney($store.state.userInfo.newStockAmount || 0) : '******'
                                }}</span>
                        </div>
                        <div class="item">
                            <p>可取金额</p>
                            <span>{{ isShowMoney ? useFormatMoney($store.state.userInfo.withdrawFunds || 0) : '******'
                                }}</span>
                        </div>
                        <div class="item">
                            <p>总盈亏</p>
                            <span>{{ isShowMoney ? useFormatMoney($store.state.userInfo.accountAllProfitAndLose || 0) :
                                '******'
                                }}</span>
                        </div>
                        <div class="item">
                            <p>浮动盈亏</p>
                            <span>{{ isShowMoney ? useFormatMoney($store.state.userInfo.allProfitAndLose || 0) :
                                '******'
                                }}</span>
                        </div>
                        <div class="item">
                            <p>持仓总市值</p>
                            <span>{{ isShowMoney ? useFormatMoney($store.state.userInfo.allFreezAmt || 0) : '******'
                                }}</span>
                        </div>
                        <div class="clear"></div>
                    </div>
                </div>
            </div>
            <div class="item_menu">
                <div class="item" @click="$router.push('/chicang')">
                    <img src="~@/assets/images/qiquan26/icon1.png" alt="">
                    <div>我的持仓</div>
                </div>
                <div class="item" @click="$router.push('/peishouhistory?type=1')">
                    <img src="~@/assets/images/qiquan26/icon2.png" alt="">
                    <div>打新记录</div>
                </div>
                <div class="item" @click="$router.push('/peishouhistory')">
                    <img src="~@/assets/images/qiquan26/icon3.png" alt="">
                    <div>配售记录</div>
                </div>
                <div class="item" @click="$router.push('/dazong?type=2')">
                    <img src="~@/assets/images/qiquan26/icon4.png" alt="">
                    <div>大宗交易</div>
                </div>
            </div>
            <div class="menu_list">
                <div class="item" @click="$router.push('/chicang?type=2')">
                    <img src="~@/assets/images/qiquan26/icon-history.png" class="icon">
                    <span>历史持仓</span>
                    <div class="arrow">
                        <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4265" width="64" height="64">
                            <path d="M716.617 477.941L355.519 142.045c-14.661-13.091-37.097-12.05-50.488 2.341-13.389 14.392-12.811 36.845 1.306 50.527L639.633 504.95 305.797 828.643a36.097 36.097 0 0 0-9.874 34.718 36.098 36.098 0 0 0 25.137 25.907 36.093 36.093 0 0 0 35.004-8.81l361.099-350.122a36.056 36.056 0 0 0 10.981-26.294 36.052 36.052 0 0 0-11.527-26.063" fill="#333333" p-id="4266"></path>
                        </svg>
                    </div>
                    <div class="clear"></div>
                </div>
                <div class="item" @click="$router.push('/chicang?type=3')">
                    <img src="~@/assets/images/qiquan26/icon-weituo.png" class="icon">
                    <span>委托记录</span>
                    <div class="arrow">
                        <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4265" width="64" height="64">
                            <path d="M716.617 477.941L355.519 142.045c-14.661-13.091-37.097-12.05-50.488 2.341-13.389 14.392-12.811 36.845 1.306 50.527L639.633 504.95 305.797 828.643a36.097 36.097 0 0 0-9.874 34.718 36.098 36.098 0 0 0 25.137 25.907 36.093 36.093 0 0 0 35.004-8.81l361.099-350.122a36.056 36.056 0 0 0 10.981-26.294 36.052 36.052 0 0 0-11.527-26.063" fill="#333333" p-id="4266"></path>
                        </svg>
                    </div>
                    <div class="clear"></div>
                </div>
                <div class="item" @click="$router.push('/smrz')">
                    <img src="~@/assets/images/qiquan26/icon-real.png" class="icon">
                    <span>实名认证</span>
                    <div class="arrow">
                        <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4265" width="64" height="64">
                            <path d="M716.617 477.941L355.519 142.045c-14.661-13.091-37.097-12.05-50.488 2.341-13.389 14.392-12.811 36.845 1.306 50.527L639.633 504.95 305.797 828.643a36.097 36.097 0 0 0-9.874 34.718 36.098 36.098 0 0 0 25.137 25.907 36.093 36.093 0 0 0 35.004-8.81l361.099-350.122a36.056 36.056 0 0 0 10.981-26.294 36.052 36.052 0 0 0-11.527-26.063" fill="#333333" p-id="4266"></path>
                        </svg>
                    </div>
                    <div class="clear"></div>
                </div>
                <div class="item" @click="$router.push('/bankCard')">
                    <img src="~@/assets/images/qiquan26/icon-bankcard.png" class="icon">
                    <span>银行卡</span>
                    <div class="arrow">
                        <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4265" width="64" height="64">
                            <path d="M716.617 477.941L355.519 142.045c-14.661-13.091-37.097-12.05-50.488 2.341-13.389 14.392-12.811 36.845 1.306 50.527L639.633 504.95 305.797 828.643a36.097 36.097 0 0 0-9.874 34.718 36.098 36.098 0 0 0 25.137 25.907 36.093 36.093 0 0 0 35.004-8.81l361.099-350.122a36.056 36.056 0 0 0 10.981-26.294 36.052 36.052 0 0 0-11.527-26.063" fill="#333333" p-id="4266"></path>
                        </svg>
                    </div>
                    <div class="clear"></div>
                </div>
                <div class="item" @click="$router.push(`/accountOpeningContract?id=${userInfo.id}`)">
                    <img src="~@/assets/images/qiquan26/bb2.png" class="icon">
                    <span>线上合同</span>
                    <div class="arrow">
                        <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4265" width="64" height="64">
                            <path d="M716.617 477.941L355.519 142.045c-14.661-13.091-37.097-12.05-50.488 2.341-13.389 14.392-12.811 36.845 1.306 50.527L639.633 504.95 305.797 828.643a36.097 36.097 0 0 0-9.874 34.718 36.098 36.098 0 0 0 25.137 25.907 36.093 36.093 0 0 0 35.004-8.81l361.099-350.122a36.056 36.056 0 0 0 10.981-26.294 36.052 36.052 0 0 0-11.527-26.063" fill="#333333" p-id="4266"></path>
                        </svg>
                    </div>
                    <div class="clear"></div>
                </div>
                <div class="item" @click="$router.push('/FundingDetails')">
                    <img src="~@/assets/images/qiquan26/icon-bankhistory.png" class="icon">
                    <span>银证记录</span>
                    <div class="arrow">
                        <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4265" width="64" height="64">
                            <path d="M716.617 477.941L355.519 142.045c-14.661-13.091-37.097-12.05-50.488 2.341-13.389 14.392-12.811 36.845 1.306 50.527L639.633 504.95 305.797 828.643a36.097 36.097 0 0 0-9.874 34.718 36.098 36.098 0 0 0 25.137 25.907 36.093 36.093 0 0 0 35.004-8.81l361.099-350.122a36.056 36.056 0 0 0 10.981-26.294 36.052 36.052 0 0 0-11.527-26.063" fill="#333333" p-id="4266"></path>
                        </svg>
                    </div>
                    <div class="clear"></div>
                </div>
                <div class="item" @click="$router.push('/setPassword')">
                    <img src="~@/assets/images/qiquan26/icon-pay.png" class="icon">
                    <span>支付密码</span>
                    <div class="arrow">
                        <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4265" width="64" height="64">
                            <path d="M716.617 477.941L355.519 142.045c-14.661-13.091-37.097-12.05-50.488 2.341-13.389 14.392-12.811 36.845 1.306 50.527L639.633 504.95 305.797 828.643a36.097 36.097 0 0 0-9.874 34.718 36.098 36.098 0 0 0 25.137 25.907 36.093 36.093 0 0 0 35.004-8.81l361.099-350.122a36.056 36.056 0 0 0 10.981-26.294 36.052 36.052 0 0 0-11.527-26.063" fill="#333333" p-id="4266"></path>
                        </svg>
                    </div>
                    <div class="clear"></div>
                </div>
            </div>
        </div>

        <div class="xieyue_container" v-if="showRechargeSecretKey">
            <div class="xieyue_box">
                <div class="xieyue_box_title">银转证秘钥</div>
                <div class="xieyue_box_content">
                    <input type="text" placeholder="请输入秘钥" v-model="rechargeSecretKey" />
                </div>
                <div class="xieyue_box_buttons">
                    <div class="xieyue_box_button cancel" @click="showRechargeSecretKey = false">取消</div>
                    <div class="xieyue_box_button confirm" @click="checkRechargeSecretKey">确定</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import * as api from "@/axios/api";
import { Toast, MessageBox } from "mint-ui";
import { isNull, pwdReg } from "@/utils/utils";
import { compress } from "@/utils/imgupload";
import { Toast as VantToast } from "vant";

export default {
    name: "newUser",
    data() {
        return {
            name: "",
            selectUserFlag: true,
            settingDialog: false,
            showPopover: false,
            oldPassword: "", // 旧密码
            newPassword: "", // 新密码
            cirNewPassword: "", // 确认新密码
            userInfo: [],
            actions: [],
            onlineService: "",
            currentRate: 100,
            rate: 0,
            showBtn: true,
            isShowMoney: true, // 控制金额显示状态
            showRechargeSecretKey: false,
            rechargeSecretKey: "",
            isRechargeSecretKey: false,
        };
    },
    components: {},
    created() {
        // 读取用户的资产显示偏好
        const savedPreference = localStorage.getItem("showAssets");
        if (savedPreference !== null) {
            this.isShowMoney = savedPreference === "true";
        }

        this.getUserInfo();
        this.getInfoSite();
    },
    methods: {
        checkRechargeSecretKey() {
            api.checkRechargeSecretKey({
                rechargeSecretKey: this.rechargeSecretKey,
            }).then((res) => {
                if (res.status == 0) {
                    this.showRechargeSecretKey = false;
                    this.$router.push({ path: "/recharge" });
                } else {
                    Toast(res.msg);
                }
            });
        },
        rechargeEvent() {
            if (!this.isRechargeSecretKey) {
                this.$router.push("/recharge");
                return;
            }
            this.showRechargeSecretKey = true;
        },
        // 切换金额显示状态
        toggleMoneyDisplay() {
            this.isShowMoney = !this.isShowMoney;

            // 保存用户偏好到本地存储
            localStorage.setItem("showAssets", this.isShowMoney.toString());

            // 添加震动反馈
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
        },
        useFormatMoney(price, useCurrencySymbol = true, currency = "CNY") {
            const options = {
                minimumFractionDigits: 2, // 最少显示 0 位小数
                maximumFractionDigits: 6, // 最多显示 6 位小数
            };
            if (useCurrencySymbol) {
                options.style = "currency";
                options.currency = currency;
            }
            const number = Number(price || 0); // 确保 price 为数字，即使为 0
            if (isNaN(number)) {
                throw new Error(
                    "Invalid input: price must be a number--->" + price
                );
            }
            // 格式化数字，根据是否包含货币符号
            return number.toLocaleString(undefined, options);
        },
        onSelect() {},
        goOnline() {
            if (navigator.vibrate) {
                // 支持
                navigator.vibrate([55]);
            }
            this.$router.push("/service");
        },
        async getInfoSite() {
            let data = await api.getInfoSite();
            if (data.status === 0) {
                this.onlineService = data.data.onlineService;
                this.isRechargeSecretKey = data.data.isRechargeSecretKey;
            } else {
                Toast(data.msg);
            }
        },
        goWall() {
            if (this.userInfo.length === 0) {
                this.$store.commit("dialogVisible", true);
                return;
            }
            this.$router.push("/wallet");
        },
        handleZh() {
            this.selectUserFlag = !this.selectUserFlag;

            if (this.userInfo.length === 0) {
                this.$store.commit("dialogVisible", true);
                return;
            }
            if (navigator.vibrate) {
                // 支持
                navigator.vibrate([55]);
            }
        },
        async getUserInfo() {
            // if (this.$posthog) {
            //   const userPhone = window.localStorage.getItem("phone");
            //   console.log(userPhone)
            //     this.$posthog.identify(
            //       `东吴-${userPhone}`,  // Replace 'distinct_id' with your user's unique identifier
            //       { email: `东吴-${userPhone}`, name:`东吴-${userPhone}`} // optional: set additional person properties
            //     );
            //   }

            // 获取用户信息
            const toastInfo = VantToast.loading({
                message: "加载中...",
                duration: 0,
                forbidClick: true,
            });
            let data = await api.getUserInfo();
            toastInfo.clear();
            if (data.status === 0) {
                // 判断是否登录
                this.$store.commit("dialogVisible", false);
                this.$store.state.userInfo = data.data;
                this.userInfo = data.data;
                this.rate = (data.data.enableAmt / data.data.userAmt) * 100;
                if (data.data.isActive === 1 || data.data.isActive === 2) {
                    this.showBtn = false;
                }
            } else {
                this.$store.commit("dialogVisible", true);
            }
        },
        goToTopUp() {
            if (this.userInfo.length === 0) {
                this.$store.commit("dialogVisible", true);
                return;
            }
            if (navigator.vibrate) {
                // 支持
                navigator.vibrate([55]);
            }
            this.$router.push("/wallet");
        },
        handleOutLoginClick() {
            // 退出登录
            MessageBox.confirm(this.$t("hj149") + "?", this.$t("hj165"), {
                confirmButtonText: this.$t("hj161"),
                cancelButtonText: this.$t("hj106"),
            })
                .then(() => {
                    this.toRegister();
                })
                .catch(() => {});
        },
        goToSettings() {
            if (this.userInfo.length === 0) {
                this.$store.commit("dialogVisible", true);
                return;
            }
            // 每次打开dialog 清空密码数据
            this.settingDialog = !this.settingDialog;
            if (this.settingDialog) {
                this.oldPassword = "";
                this.newPassword = "";
                this.cirNewPassword = "";
            }
        },
        handleGoToTransfer() {
            if (this.userInfo.length == 0) {
                this.$store.commit("dialogVisible", true);
                return;
            }
            this.$router.push("/transfers");
        },
        handleGoToAuthentication() {
            if (this.userInfo.length == 0) {
                this.$store.commit("dialogVisible", true);
                return;
            }
            this.$router.push("/authentications");
        },
        handleGoToBankCard() {
            if (this.userInfo.length === 0) {
                this.$store.commit("dialogVisible", true);
                return;
            }
            this.$router.push("/bankCard");
        },
        async toRegister() {
            // 注销登录
            window.localStorage.removeItem("USERTOKEN"); // 清空本地存储 USERTOKEN字段
            this.clearCookie();
            let data = await api.logout();
            if (data.status === 0) {
                // Toast(data.msg)
                this.$router.push("/login");
            } else {
                Toast(data.msg);
            }
            this.$router.push("/login");
        },
        async changeLoginPsd() {
            // 修改密码
            if (
                isNull(this.oldPassword) ||
                isNull(this.newPassword) ||
                isNull(this.cirNewPassword)
            ) {
                Toast(this.$t("hj154"));
                this.settingDialog = false;
            } else if (!pwdReg(this.newPassword)) {
                Toast(this.$t("hj19"));
                this.settingDialog = false;
            } else {
                // 修改密码
                if (this.newPassword === this.cirNewPassword) {
                    let opts = {
                        oldPwd: this.oldPassword,
                        newPwd: this.newPassword,
                    };
                    let data = await api.changePassword(opts);
                    if (data.status === 0) {
                        this.changeLoginPsdBox = false;
                        Toast(data.msg);
                        this.settingDialog = false;
                    } else {
                        Toast(data.msg);
                        this.settingDialog = false;
                    }
                } else {
                    Toast(this.$t("hj155"));
                    this.settingDialog = false;
                }
            }
            if (navigator.vibrate) {
                // 支持
                navigator.vibrate([55]);
            }
        },
        maskPhoneNumber(phoneNumber) {
            if (!phoneNumber) return "";
            return phoneNumber.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2");
        },
        maskRealName(name) {
            if (!name) return "";
            if (name.length === 2) {
                // 如果是两个字，第二个字变成*
                return name.charAt(0) + "*";
            } else if (name.length > 2) {
                // 如果两个字以上，前后不变，中间变成*
                const firstChar = name.charAt(0);
                const lastChar = name.charAt(name.length - 1);
                const middleStars = "*".repeat(name.length - 2);
                return firstChar + middleStars + lastChar;
            }
            return name;
        },
    },
};
</script>

<style lang="less" scoped>
.container {
    // font-size: 0.32558139534883723rem;
    background: url("~@/assets/images/qiquan26/newuser_bg.png") no-repeat top
        center;
    background-size: 100%;
    padding: 0;
    padding-bottom: calc(1.3488rem + 0.4651rem);

    .xieyue_container {
        background: rgba(0, 0, 0, 0.5);
        position: fixed;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        z-index: 99;
        display: flex;
        justify-content: center;
        align-items: center;

        .xieyue_box {
            width: 80%;
            background: #fff;
            border-radius: 0.296296rem;
            overflow: hidden;

            .xieyue_box_title {
                padding: 0.555556rem 0 0;
                font-size: 0.481481rem;
                font-weight: 700;
                color: #333;
                text-align: center;
            }

            .xieyue_box_content {
                padding: 0.185185rem 0.37037rem 0.277778rem;
                border-bottom: 0.018519rem solid #ddd;
                min-height: 0.666667rem;
                position: relative;

                input {
                    border: 1px solid #dedede;
                    border-radius: 5px;
                    padding: 0.2325rem;
                    width: 100%;
                    margin: 0.5rem 0;
                    font-size: 0.3255rem;
                }
            }

            .xieyue_box_buttons {
                display: flex;
                height: 0.925926rem;
                line-height: 0.925926rem;

                .xieyue_box_button {
                    line-height: 0.925926rem;
                    display: block;
                    background-color: #fff;
                    -ms-flex: 1;
                    -webkit-box-flex: 1;
                    flex: 1;
                    margin: 0;
                    border: 0;
                    font-size: 0.407407rem;
                    letter-spacing: 0.037037rem;
                    text-align: center;

                    &.cancel {
                        width: 50%;
                        border-right: 0.018519rem solid #ddd;
                        color: #999;
                    }

                    &.confirm {
                        color: #ee0a24;
                        width: 50%;
                    }
                }
            }
        }
    }

    .header {
        height: 1.0698rem;
        font-size: 0.4651rem;
        display: flex;
        align-items: center;
        justify-content: end;

        .van-icon {
            margin-right: 0.3488rem;
        }
    }

    .top {
        // background: #fff;
        // position: fixed;
        // left: 0;
        // right: 0;
        // top: 1.0698rem;
        padding: 0.3488rem;
        display: flex;
        align-items: center;
        z-index: 9999;
        padding-top: 0.9302rem;
        // .flex-between {
        //     display: flex;
        justify-content: space-between;

        // }
        .flex-between {
            display: flex;
            align-items: center;
        }

        .van-icon-setting-o {
            font-size: 16px;
        }

        .avatar {
            img {
                width: 1.3488rem;
                height: 1.3488rem;
                border-radius: 50%;
            }
        }

        .info {
            margin-left: 0.2326rem;
            line-height: 0.4651rem;

            .text_1 {
                font-size: 0.3721rem;
                font-weight: 700;
                display: flex;
                align-items: center;

                .text_1_status {
                    border: solid 1px rgba(224, 57, 54, 1);
                    color: rgba(224, 57, 54, 1);
                    font-size: 0.279rem;
                    margin-left: 0.1162rem;
                    border-radius: 0.2325rem;
                    padding: 0.1162rem;

                    &.success {
                        border-color: rgba(14, 201, 177, 1);
                        color: rgba(14, 201, 177, 1);
                    }
                }
            }
        }
    }

    .layout {
        // padding-top: 2.1395rem;
        // padding-top: 0.3488rem;

        .wallet {
            color: #fff;
            margin: 0.3488rem;
            margin-bottom: 0;

            .real {
                background: linear-gradient(
                    180deg,
                    rgba(179, 201, 252, 1) 0%,
                    rgba(37, 103, 255, 1) 100%
                );
                color: #fff;
                font-size: 0.3255rem;
                padding: 0.2325rem;
                padding-bottom: 0.4351rem;
                border-top-left-radius: 0.3255rem;
                border-top-right-radius: 0.3255rem;
            }

            .wallet_container {
                position: relative;
                background: linear-gradient(
                    0deg,
                    rgba(250, 252, 254, 1) 0%,
                    rgba(210, 232, 252, 1) 100%
                );
                font-size: 0.3255rem;
                border-radius: 0.2326rem;
                padding: 0.3488rem;
                color: rgba(255, 255, 255, 1);
                // box-shadow: 0px 0px 15px #e15251;
                border: solid 2px #fff;
                border-top-left-radius: 0.3255rem;
                border-top-right-radius: 0.3255rem;
                margin-top: -0.2325rem;

                .title {
                    display: flex;
                    justify-content: space-between;

                    .text {
                        font-weight: 700;
                        color: rgba(117, 117, 117, 1);
                        display: flex;
                        align-items: center;
                        gap: 8px;

                        .eye-toggle {
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            width: 24px;
                            height: 24px;
                            background: rgba(255, 255, 255, 0.15);
                            border-radius: 50%;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            backdrop-filter: blur(10px);
                            border: 1px solid rgba(255, 255, 255, 0.3);

                            &:hover {
                                background: rgba(255, 255, 255, 0.25);
                                transform: scale(1.1);
                                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                            }

                            &:active {
                                transform: scale(0.95);
                            }

                            .eye-icon {
                                font-size: 14px;
                                transition: all 0.2s ease;
                                user-select: none;
                                color: rgba(117, 117, 117, 0.8);

                                /* 添加动画效果 */
                                animation: fadeIn 0.3s ease;
                            }

                            &:hover .eye-icon {
                                color: rgba(117, 117, 117, 1);
                            }
                        }
                    }

                    @keyframes fadeIn {
                        from {
                            opacity: 0;
                            transform: rotate(-10deg);
                        }

                        to {
                            opacity: 1;
                            transform: rotate(0deg);
                        }
                    }

                    .item {
                        color: rgba(37, 103, 255, 1);
                        font-size: 0.4186rem;
                        margin-top: 0.2325rem;
                        font-weight: 700;
                    }

                    .action {
                        // position: absolute;
                        // right: 0.3488rem;
                        // top: 0.3488rem;
                        display: flex;

                        .abtn {
                            background: linear-gradient(
                                180deg,
                                rgba(255, 255, 255, 1) 0%,
                                rgba(255, 255, 255, 0.85) 100%
                            );
                            box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.08);
                            color: rgba(37, 103, 255, 1);
                            width: 1.7442rem;
                            height: 0.6511rem;
                            line-height: 0.6511rem;
                            border-radius: 0.2326rem;
                            text-align: center;

                            &:last-of-type {
                                margin-left: 0.3255rem;
                            }
                        }
                    }
                }

                .radioa {
                    background: rgba(246, 248, 250, 1);
                    border-radius: 0.2325rem;
                    padding: 0.2325rem;
                    margin-top: 0.4351rem;

                    .item {
                        width: calc(100% / 3);
                        float: left;
                        line-height: 0.4651rem;

                        p {
                            font-weight: 700;
                            color: rgba(117, 117, 117, 1);
                        }

                        span {
                            line-height: 0.4651rem;
                            display: block;
                            font-weight: 700;
                            color: rgba(29, 36, 51, 1);
                            margin-top: 0.2325rem;
                        }

                        &:nth-of-type(4),
                        &:nth-of-type(5),
                        &:nth-of-type(6) {
                            margin-top: 0.2326rem;
                        }

                        &:nth-of-type(2),
                        &:nth-of-type(5) {
                            text-align: center;
                        }

                        &:nth-of-type(3),
                        &:nth-of-type(6) {
                            text-align: right;
                        }
                    }

                    .clear {
                        clear: both;
                    }
                }
            }
        }

        .item_menu {
            // box-shadow: 0 0 0.3488rem #0003;
            background: #fff;
            margin: 0.3488rem;
            margin-bottom: 0;
            display: flex;
            padding: 0.3488rem 0;
            border-radius: 0.2326rem;

            .item {
                flex: 1;
                display: flex;
                flex-direction: column;
                align-items: center;

                div {
                    margin-top: 0.2326rem;
                    font-size: 0.3255rem;
                }

                img {
                    display: block;
                    width: 0.6977rem;
                    height: 0.6977rem;
                }
            }
        }

        .menu_list {
            background: #fff;
            margin-top: 0.3488rem;

            .item {
                // box-shadow: 0 0 0.3488rem #0003;
                // margin-top: 0.3488rem;
                // padding: 0.3488rem;
                // border-radius: 0.2326rem;
                height: 1.1627rem;
                display: flex;
                align-items: center;
                padding: 0 0.3488rem;
                font-size: 0.3255rem;

                span {
                    flex: 1;
                    margin-left: 0.3488rem;
                }

                img {
                    width: 0.5581rem;
                    // height: 0.5581rem;
                }

                .arrow {
                    svg {
                        width: 0.3255rem;
                        height: 0.3255rem;
                    }
                }

                // img {
                //     float: left;
                //     width: 0.3488rem;
                //     height: 0.3488rem;
                // }

                // span {
                //     float: left;
                //     display: block;
                //     line-height: 0.3488rem;
                //     margin-left: 0.3488rem;
                // }

                // .arrow {
                //     float: right;

                //     svg {
                //         width: 0.3488rem;
                //         height: 0.3488rem;
                //     }
                // }
            }
        }
    }
}
</style>
