<template>
    <div class="funding-details-page">
        <div class="header">
            <van-nav-bar title="银证记录" left-arrow fixed @click-left="handleGoBack" />
        </div>

        <div class="page-content">
            <!-- 页面介绍 -->
            <div class="page-intro">
                <div class="intro-icon">💳</div>
                <div class="intro-text">
                    <div class="intro-title">银证记录</div>
                    <div class="intro-desc">查看银行与证券账户间的资金转入转出详情</div>
                </div>
                <div class="intro-stats">
                    <div class="stat-item">
                        <div class="stat-value">{{ totalRecords }}</div>
                        <div class="stat-label">总记录</div>
                    </div>
                </div>
            </div>

            <!-- Tab 切换 - 参考peishouhistory.vue设计 -->
            <div class="tab-menu">
                <div :class="`tab-item ${activeTab === 0 ? 'active' : ''}`" @click="switchTab(0)">
                    <div class="tab-content">
                        <span>银证转入</span>
                        <span class="tab-indicator"></span>
                    </div>
                </div>
                <div :class="`tab-item ${activeTab === 1 ? 'active' : ''}`" @click="switchTab(1)">
                    <div class="tab-content">
                        <span>银证转出</span>
                        <span class="tab-indicator"></span>
                    </div>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="record-content">
                <div v-if="loading" class="loading-state">
                    <van-loading size="24px" vertical>加载中...</van-loading>
                </div>

                <div v-else-if="!hasData" class="empty-state">
                    <div class="empty-icon">{{ activeTab === 0 ? '📥' : '📤' }}</div>
                    <div class="empty-text">暂无{{ currentTabName }}记录</div>
                    <div class="empty-desc">您还没有进行过{{ currentTabName }}操作</div>
                </div>

                <div v-else class="content-wrapper">
                    <!-- 银证转入列表 -->
                    <div v-if="activeTab === 0" class="list-wrapper" v-infinite-scroll="loadMore"
                        infinite-scroll-disabled="loading" infinite-scroll-distance="10">
                        <div class="record-card" v-for="(item, index) in list" :key="item.key || index">
                            <!-- 卡片头部 -->
                            <div class="card-header">
                                <div class="header-left">
                                    <div class="transaction-type">银证转入</div>
                                    <div class="order-number">{{ item.orderSn }}</div>
                                </div>
                                <div class="status-wrapper">
                                    <div class="status success" v-if="item.orderStatus == 1">
                                        <i class="status-icon">✓</i>成功
                                    </div>
                                    <div class="status failed" v-if="item.orderStatus == 2">
                                        <i class="status-icon">✗</i>失败
                                    </div>
                                    <div class="status cancelled" v-if="item.orderStatus == 3">
                                        <i class="status-icon">⊝</i>已取消
                                    </div>
                                    <div class="status pending" v-if="item.orderStatus == 4">
                                        <i class="status-icon">⏳</i>审核中
                                    </div>
                                </div>
                            </div>

                            <!-- 金额区域 -->
                            <div class="amount-section">
                                <div class="amount-wrapper">
                                    <div class="amount-label">转入金额</div>
                                    <div class="amount-value positive">¥{{ formatAmount(item.payAmt) }}</div>
                                </div>
                            </div>

                            <!-- 详情区域 -->
                            <div class="details-section">
                                <div class="detail-row">
                                    <div class="detail-item">
                                        <span class="label">支付方式</span>
                                        <span class="value">{{ getPaymentChannelName(item.payChannel) }}</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="label">转入时间</span>
                                        <span class="value">{{ formatTime(item.addTime) }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 银证转出列表 -->
                    <div v-if="activeTab === 1" class="list-wrapper" v-infinite-scroll="loadMore"
                        infinite-scroll-disabled="loading" infinite-scroll-distance="10">
                        <div class="record-card" v-for="(item, index) in list" :key="item.key || index">
                            <!-- 卡片头部 -->
                            <div class="card-header">
                                <div class="header-left">
                                    <div class="transaction-type">银证转出</div>
                                    <div class="order-number">{{ item.orderNo }}</div>
                                </div>
                                <div class="status-wrapper">
                                    <div class="status pending" v-if="item.withStatus == 0">
                                        <i class="status-icon">⏳</i>审核中
                                    </div>
                                    <div class="status success" v-if="item.withStatus == 1">
                                        <i class="status-icon">✓</i>成功
                                    </div>
                                    <div class="status failed" v-if="item.withStatus == 2">
                                        <i class="status-icon">✗</i>失败
                                    </div>
                                    <div class="status cancelled" v-if="item.withStatus == 3">
                                        <i class="status-icon">⊝</i>已取消
                                    </div>
                                </div>
                            </div>

                            <!-- 金额区域 -->
                            <div class="amount-section">
                                <div class="amount-wrapper">
                                    <div class="amount-label">转出金额</div>
                                    <div class="amount-value negative">¥{{ formatAmount(item.withAmt) }}</div>
                                </div>
                            </div>

                            <!-- 银行卡信息 -->
                            <div class="bank-section" v-if="item.bankNo">
                                <div class="bank-card">
                                    <div class="bank-icon">🏦</div>
                                    <div class="bank-info">
                                        <div class="bank-label">银行卡</div>
                                        <div class="bank-number">{{ formatBankNumber(item.bankNo) }}</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 失败原因 -->
                            <div class="error-section" v-if="item.withStatus == 2 && item.withMsg">
                                <div class="error-card">
                                    <div class="error-icon">⚠️</div>
                                    <div class="error-content">
                                        <div class="error-title">失败原因</div>
                                        <div class="error-message">{{ item.withMsg }}</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 详情区域 -->
                            <div class="details-section">
                                <div class="detail-row">
                                    <div class="detail-item">
                                        <span class="label">申请时间</span>
                                        <span class="value">{{ formatTime(item.applyTime) }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 加载更多指示器 -->
                    <div v-if="loading && list.length > 0" class="load-more-indicator">
                        <van-loading size="16px">加载更多...</van-loading>
                    </div>

                    <!-- 没有更多数据提示 -->
                    <div v-if="!hasMore && list.length > 0" class="no-more-data">
                        <span>已显示全部记录</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import * as api from "@/axios/api";
import { Dialog, Toast } from "vant";
import dayjs from "dayjs";

export default {
    name: "FundingDetails",
    data() {
        return {
            activeTab: 0, // 当前激活的tab：0-银证转入，1-银证转出
            loading: false,
            list: [],
            pageNum: 1,
            pageSize: 15,
            total: 0,
            hasMore: true,
        };
    },

    computed: {
        // 当前标签页名称
        currentTabName() {
            const tabNames = ['银证转入', '银证转出'];
            return tabNames[this.activeTab] || '未知';
        },

        // 是否有数据
        hasData() {
            return this.list && this.list.length > 0;
        },

        // 总记录数
        totalRecords() {
            return this.list ? this.list.length : 0;
        }
    },

    mounted() {
        this.initializePage();
    },

    methods: {
        // 初始化页面
        async initializePage() {
            console.log('银证记录页面初始化');
            await this.loadTabData();
            this.triggerVibration();
        },

        // 返回上一页
        handleGoBack() {
            if (this.hasUnsavedChanges()) {
                Dialog.confirm({
                    title: '确认离开',
                    message: '确定要离开当前页面吗？',
                })
                    .then(() => {
                        this.navigateBack();
                    })
                    .catch(() => {
                        // 用户取消
                    });
            } else {
                this.navigateBack();
            }
        },

        // 检查是否有未保存的更改
        hasUnsavedChanges() {
            return false;
        },

        // 执行返回导航
        navigateBack() {
            try {
                this.$router.go(-1);
            } catch (error) {
                console.error('导航失败:', error);
                this.$router.push('/user');
            }
        },

        // 切换tab
        async switchTab(tabIndex) {
            if (this.activeTab === tabIndex) {
                return;
            }

            console.log('切换Tab到:', this.currentTabName, '->', tabIndex === 0 ? '银证转入' : '银证转出');

            this.activeTab = tabIndex;

            // 重新加载数据
            await this.loadTabData();

            console.log('Tab切换到:', this.currentTabName);
            this.triggerVibration();
        },

        // 加载tab数据
        async loadTabData() {
            this.resetState();

            if (this.activeTab === 0) {
                console.log('加载银证转入数据');
                await this.loadRechargeList();
            } else {
                console.log('加载银证转出数据');
                await this.loadCashList();
            }
        },

        // 重置状态
        resetState() {
            this.list = [];
            this.pageNum = 1;
            this.total = 0;
            this.hasMore = true;
            this.loading = false;
            console.log('页面状态已重置');
        },

        // 加载银证转入列表
        async loadRechargeList() {
            if (this.loading) {
                console.log('银证转入正在加载中，跳过重复请求');
                return;
            }

            console.log('开始调用银证转入接口，pageNum:', this.pageNum);
            this.loading = true;

            try {
                const params = {
                    payChannel: "",
                    orderStatus: "",
                    pageNum: this.pageNum,
                    pageSize: this.pageSize,
                };

                console.log('银证转入接口请求参数:', params);
                const response = await api.rechargeList(params);
                console.log('银证转入接口响应:', response);

                this.handleRechargeResponse(response);

            } catch (error) {
                this.handleError(error, '银证转入');
            } finally {
                this.loading = false;
            }
        },

        // 加载银证转出列表
        async loadCashList() {
            if (this.loading) {
                console.log('银证转出正在加载中，跳过重复请求');
                return;
            }

            console.log('开始调用银证转出接口，pageNum:', this.pageNum);
            this.loading = true;

            try {
                const params = {
                    withStatus: "",
                    pageNum: this.pageNum,
                    pageSize: this.pageSize,
                };

                console.log('银证转出接口请求参数:', params);
                const response = await api.withdrawList(params);
                console.log('银证转出接口响应:', response);

                this.handleCashResponse(response);

            } catch (error) {
                this.handleError(error, '银证转出');
            } finally {
                this.loading = false;
            }
        },

        // 处理银证转入响应
        handleRechargeResponse(response) {
            if (!response) {
                throw new Error('服务器无响应');
            }

            if (response.status === 0 && response.data && Array.isArray(response.data.list)) {
                if (this.pageNum === 1) {
                    this.list = [];
                }

                response.data.list.forEach((element) => {
                    this.list.push(element);
                });
                this.total = response.data.total || 0;
                this.hasMore = this.list.length < this.total;

                console.log(`银证转入记录加载成功，当前${this.list.length}条，总计${this.total}条`);
            } else {
                throw new Error(response.msg || '数据格式异常');
            }
        },

        // 处理银证转出响应
        handleCashResponse(response) {
            if (!response) {
                throw new Error('服务器无响应');
            }

            if (response.status === 0 && response.data && Array.isArray(response.data.list)) {
                if (this.pageNum === 1) {
                    this.list = [];
                }

                response.data.list.forEach((element) => {
                    this.list.push(element);
                });

                const total = response.data.total;
                if (total !== undefined) {
                    this.total = total;
                    this.hasMore = this.list.length < total;
                } else {
                    this.hasMore = response.data.list.length >= this.pageSize;
                }

                console.log(`银证转出记录加载成功，当前${this.list.length}条`);
            } else {
                throw new Error(response.msg || '数据格式异常');
            }
        },

        // 处理错误
        handleError(error, type) {
            console.error(`获取${type}记录失败:`, error);

            if (error.message) {
                Toast(error.message);
            } else {
                Toast('网络错误，请重试');
            }
        },

        // 加载更多数据
        async loadMore() {
            if (this.loading || !this.hasMore || this.list.length < 10) {
                return;
            }

            if (this.total > 0 && this.list.length >= this.total) {
                this.hasMore = false;
                return;
            }

            try {
                this.pageNum++;
                if (this.activeTab === 0) {
                    await this.loadRechargeList();
                } else {
                    await this.loadCashList();
                }
            } catch (error) {
                this.pageNum--;
                console.error('加载更多数据失败:', error);
            }
        },

        // 处理取消操作
        async handleCancel(item) {
            if (!item || !item.id) {
                Toast('参数错误');
                return;
            }

            try {
                await Dialog.confirm({
                    title: '确认取消',
                    message: '确定要取消这笔转出申请吗？',
                    confirmButtonText: '确定取消',
                    cancelButtonText: '我再想想',
                });

                await this.executeCancel(item.id);
            } catch (error) {
                if (error !== 'cancel') {
                    console.error('取消转出失败:', error);
                }
            }
        },

        // 执行取消操作
        async executeCancel(withId) {
            try {
                const params = { withId };
                const response = await api.canceloutMoney(params);

                if (response.status === 0) {
                    Toast(response.msg || '取消成功');
                    await this.loadTabData();
                } else {
                    throw new Error(response.msg || '取消失败');
                }
            } catch (error) {
                Toast(error.message || '取消失败，请重试');
            }
        },

        // 获取支付渠道名称
        getPaymentChannelName(channel) {
            const channelMap = {
                0: '支付宝',
                1: '对公转账',
            };
            return channelMap[channel] || '其他支付方式';
        },

        // 格式化金额
        formatAmount(amount) {
            if (!amount && amount !== 0) return '0.00';
            return parseFloat(amount).toLocaleString('zh-CN', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        },

        // 格式化银行卡号
        formatBankNumber(bankNo) {
            if (!bankNo) return '';
            if (bankNo.length > 8) {
                return bankNo.slice(0, 4) + '*'.repeat(bankNo.length - 8) + bankNo.slice(-4);
            }
            return bankNo;
        },

        // 格式化时间
        formatTime(time) {
            if (!time) return '--';
            return dayjs(time).format('MM-DD HH:mm');
        },

        // 触发震动反馈
        triggerVibration() {
            if (navigator.vibrate) {
                navigator.vibrate([30]);
            }
        },

        // 兼容原有方法
        onConfirm(value) {
            this.switchTab(value);
        },

        xuanze(index) {
            this.switchTab(index);
        },
    },
};
</script>

<style lang="less" scoped>
.funding-details-page {
    background: #f5f5f5;
    min-height: 100vh;
    font-size: 0.3256rem;

    .header {
        width: 100%;
        height: 1.07rem;
    }

    .page-content {
        padding: 0.4651rem 0.2326rem 0;

        .page-intro {
            background: #fff;
            border-radius: 0.1860rem;
            padding: 0.4651rem;
            margin-bottom: 0.4651rem;
            display: flex;
            align-items: center;
            gap: 0.3488rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            animation: slideInUp 0.3s ease-out;

            .intro-icon {
                font-size: 1.1627rem;
                background: linear-gradient(135deg, #EA001B, #FF6B3D);
                width: 1.3953rem;
                height: 1.3953rem;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 0 4px 12px rgba(234, 0, 27, 0.3);
            }

            .intro-text {
                flex: 1;

                .intro-title {
                    font-size: 0.4186rem;
                    font-weight: 600;
                    color: #2c3e50;
                    margin-bottom: 0.1162rem;
                }

                .intro-desc {
                    font-size: 0.3023rem;
                    color: #7f8c8d;
                    line-height: 1.4;
                }
            }

            .intro-stats {
                .stat-item {
                    text-align: center;

                    .stat-value {
                        font-size: 0.5116rem;
                        font-weight: 700;
                        color: #EA001B;
                        line-height: 1;
                        font-family: 'Arial', 'Helvetica', monospace;
                    }

                    .stat-label {
                        font-size: 0.2791rem;
                        color: #95a5a6;
                        margin-top: 0.0930rem;
                    }
                }
            }
        }

        .tab-menu {
            display: flex;
            background: #fff;
            margin-bottom: 0.4651rem;
            border-radius: 0.1860rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            animation: slideInUp 0.3s ease-out;
            animation-delay: 0.1s;
            animation-fill-mode: both;

            .tab-item {
                flex: 1;
                display: flex;
                justify-content: center;

                .tab-content {
                    position: relative;
                    padding: 0.4651rem 0.6977rem;
                    color: #7f8c8d;
                    font-weight: 500;
                    font-size: 0.3488rem;
                    cursor: pointer;
                    text-align: center;
                    transition: all 0.3s ease;

                    .tab-indicator {
                        position: absolute;
                        left: 50%;
                        bottom: 0.1162rem;
                        transform: translateX(-50%);
                        width: 0.4651rem;
                        height: 0.0697rem;
                        background: #EA001B;
                        border-radius: 0.0349rem;
                        display: none;
                    }

                    &:hover {
                        color: #EA001B;
                        border-radius: 0.1860rem;
                    }
                }

                &.active {
                    .tab-content {
                        color: #EA001B;
                        font-weight: 600;

                        .tab-indicator {
                            display: block;
                        }
                    }
                }
            }
        }

        .record-content {
            background: #fff;
            border-radius: 0.1860rem;
            min-height: 11.627rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            animation: slideInUp 0.3s ease-out;
            animation-delay: 0.2s;
            animation-fill-mode: both;

            .loading-state,
            .empty-state {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                padding: 2.3256rem 0;
            }

            .empty-state {
                .empty-icon {
                    font-size: 1.6279rem;
                    margin-bottom: 0.4651rem;
                    opacity: 0.3;
                }

                .empty-text {
                    color: #2c3e50;
                    font-size: 0.3721rem;
                    font-weight: 500;
                    margin-bottom: 0.1162rem;
                }

                .empty-desc {
                    color: #95a5a6;
                    font-size: 0.3023rem;
                }
            }

            .content-wrapper {
                padding: 0;
                overflow: hidden;
                border-radius: 0.1860rem;

                .list-wrapper {
                    .record-card {
                        background: #fff;
                        margin-top: 0.3488rem;
                        font-size: 0.3255rem;
                        border-radius: 0.2326rem;
                        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
                        border: 1px solid rgba(240, 240, 240, 0.8);
                        transition: all 0.3s ease;
                        overflow: hidden;

                        &:hover {
                            transform: translateY(-2px);
                            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
                            border-color: rgba(234, 0, 27, 0.2);
                        }

                        .card-header {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            padding: 0.4651rem;
                            border-bottom: 1px solid rgba(245, 247, 250, 1);
                            background: linear-gradient(135deg, #ffffff, #f8f9fa);

                            .header-left {
                                .transaction-type {
                                    font-size: 0.3721rem;
                                    font-weight: 600;
                                    color: #2c3e50;
                                    margin-bottom: 0.1162rem;
                                }

                                .order-number {
                                    font-size: 0.3023rem;
                                    color: #95a5a6;
                                    font-family: 'Courier New', monospace;
                                }
                            }

                            .status-wrapper {
                                .status {
                                    display: inline-flex;
                                    align-items: center;
                                    gap: 0.1162rem;
                                    padding: 0.1860rem 0.3488rem;
                                    border-radius: 0.4651rem;
                                    font-size: 0.2791rem;
                                    font-weight: 600;

                                    .status-icon {
                                        font-size: 0.3023rem;
                                    }

                                    &.success {
                                        background: linear-gradient(135deg, rgba(46, 204, 113, 0.1), rgba(39, 174, 96, 0.1));
                                        color: #27ae60;
                                        border: 1px solid rgba(39, 174, 96, 0.2);
                                    }

                                    &.failed {
                                        background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(192, 57, 43, 0.1));
                                        color: #e74c3c;
                                        border: 1px solid rgba(231, 76, 60, 0.2);
                                    }

                                    &.cancelled {
                                        background: linear-gradient(135deg, rgba(149, 165, 166, 0.1), rgba(127, 140, 141, 0.1));
                                        color: #95a5a6;
                                        border: 1px solid rgba(149, 165, 166, 0.2);
                                    }

                                    &.pending {
                                        background: linear-gradient(135deg, rgba(243, 156, 18, 0.1), rgba(230, 126, 34, 0.1));
                                        color: #f39c12;
                                        border: 1px solid rgba(243, 156, 18, 0.2);
                                    }
                                }
                            }
                        }

                        .amount-section {
                            padding: 0.6977rem 0.4651rem;
                            text-align: center;
                            background: linear-gradient(135deg, #f8f9fa, #ffffff);

                            .amount-wrapper {
                                .amount-label {
                                    font-size: 0.3023rem;
                                    color: #7f8c8d;
                                    margin-bottom: 0.2326rem;
                                    font-weight: 500;
                                }

                                .amount-value {
                                    font-size: 0.6977rem;
                                    font-weight: 700;
                                    font-family: 'Arial', 'Helvetica', monospace;
                                    letter-spacing: 0.0465rem;

                                    &.positive {
                                        color: #27ae60;
                                        text-shadow: 0 1px 2px rgba(39, 174, 96, 0.2);
                                    }

                                    &.negative {
                                        color: #e74c3c;
                                        text-shadow: 0 1px 2px rgba(231, 76, 60, 0.2);
                                    }
                                }
                            }
                        }

                        .bank-section {
                            padding: 0.4651rem;

                            .bank-card {
                                background: linear-gradient(135deg, #ecf0f1, #bdc3c7);
                                border-radius: 0.1860rem;
                                padding: 0.3488rem;
                                display: flex;
                                align-items: center;
                                gap: 0.2326rem;

                                .bank-icon {
                                    font-size: 0.4651rem;
                                    opacity: 0.8;
                                }

                                .bank-info {
                                    flex: 1;

                                    .bank-label {
                                        font-size: 0.2791rem;
                                        color: #7f8c8d;
                                        margin-bottom: 0.0465rem;
                                        font-weight: 500;
                                    }

                                    .bank-number {
                                        font-size: 0.3488rem;
                                        font-weight: 600;
                                        color: #2c3e50;
                                        font-family: 'Courier New', monospace;
                                        letter-spacing: 0.0233rem;
                                    }
                                }
                            }
                        }

                        .error-section {
                            padding: 0.4651rem;

                            .error-card {
                                background: linear-gradient(135deg, rgba(231, 76, 60, 0.05), rgba(192, 57, 43, 0.05));
                                border: 1px solid rgba(231, 76, 60, 0.2);
                                border-radius: 0.1860rem;
                                padding: 0.3488rem;
                                display: flex;
                                align-items: flex-start;
                                gap: 0.2326rem;

                                .error-icon {
                                    font-size: 0.4186rem;
                                    margin-top: 0.0233rem;
                                }

                                .error-content {
                                    flex: 1;

                                    .error-title {
                                        font-size: 0.3023rem;
                                        color: #e74c3c;
                                        font-weight: 600;
                                        margin-bottom: 0.1162rem;
                                    }

                                    .error-message {
                                        font-size: 0.3256rem;
                                        color: #c0392b;
                                        line-height: 1.4;
                                    }
                                }
                            }
                        }

                        .details-section {
                            padding: 0.4651rem;
                            background: #fafbfc;
                            border-top: 1px solid #ecf0f1;

                            .detail-row {
                                display: flex;
                                justify-content: space-between;
                                align-items: center;

                                .detail-item {
                                    display: flex;
                                    flex-direction: column;
                                    align-items: flex-start;

                                    .label {
                                        font-size: 0.2791rem;
                                        color: #95a5a6;
                                        font-weight: 500;
                                        margin-bottom: 0.0465rem;
                                    }

                                    .value {
                                        font-size: 0.3256rem;
                                        color: #2c3e50;
                                        font-weight: 600;
                                        font-family: 'Arial', 'Helvetica', monospace;
                                    }
                                }
                            }
                        }
                    }
                }

                .load-more-indicator {
                    display: flex;
                    justify-content: center;
                    padding: 0.4651rem 0;

                    :deep(.van-loading__text) {
                        color: #95a5a6;
                        font-size: 0.3023rem;
                    }
                }

                .no-more-data {
                    text-align: center;
                    padding: 0.4651rem 0;
                    color: #95a5a6;
                    font-size: 0.3023rem;
                    margin-top: 0.2326rem;
                    background: #f8f9fa;

                    span {
                        padding: 0 0.4651rem;
                    }
                }
            }
        }
    }
}

/* 加载状态样式 */
:deep(.van-loading) {
    .van-loading__text {
        color: #95a5a6;
        font-size: 0.3256rem;
    }
}

/* 动画效果 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(0.4651rem);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式适配 */
@media (max-width: 375px) {
    .funding-details-page {
        .page-content {
            padding: 0.2326rem 0.1162rem 0;

            .page-intro {
                padding: 0.3488rem;

                .intro-icon {
                    width: 1.1627rem;
                    height: 1.1627rem;
                    font-size: 0.9302rem;
                }

                .intro-stats .stat-item .stat-value {
                    font-size: 0.4186rem;
                }
            }

            .tab-menu .tab-item .tab-content {
                padding: 0.3488rem 0.4651rem;
                font-size: 0.3256rem;
            }
        }
    }
}
</style>
