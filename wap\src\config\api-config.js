/**
 * API配置文件
 * 存储所有可能的API服务器地址和健康检查配置
 */

// 服务器地址列表
const API_URLS = [
    'https://hw1s0mm.kavdm.com',
    'https://hw1b3pp.oocmw.com',
    'https://tx1n0qe.kavdm.com',
    'https://tx1lmbb.oocmw.com',
    'https://cwig2o.w6o8xd.com',
    'https://1u1qdg.w6o8xd.com'
]

// 远程连接池URL，用于动态获取更多服务器地址
const CONNECTION_POOL_URL = 'https://poolses.fztfsj.com/zxjt03.json'

// 健康检查路径
const HEALTH_CHECK_PATH = '/health-check'

// 健康检查响应验证值
const HEALTH_CHECK_RESPONSE = 'zhaoshang04'

export default {
    API_URLS,
    CONNECTION_POOL_URL,
    HEALTH_CHECK_PATH,
    HEALTH_CHECK_RESPONSE
} 