<template>
    <div class="c_container">
        <div class="header">
            <van-nav-bar title="支付" left-arrow fixed @click-left="$router.go(-1)" />
        </div>
        <div class="main">
            <iframe v-if="url" :src="url" />
        </div>
    </div>
</template>

<script>
import * as api from "@/axios/api";
import { Toast, MessageBox } from "mint-ui";

export default {
    data() {
        return {
            price: "",
            url: "",
        };
    },
    mounted() {
        this.price = this.$route.query.price;
        if (!this.price) {
            Toast("请输入金额");
            this.$router.go(-1);
            return;
        }
        this.inMoneyHandle();
    },
    methods: {
        inMoneyHandle() {
            const _this = this;
            api.inMoney({
                amt: this.price,
            }).then((res) => {
                if (res.status == 0) {
                    _this.url = res.data;
                } else {
                    Toast(res.msg);
                    this.$router.go(-1);
                }
            });
        },
    },
};
</script>


<style lang="less" scoped>
.c_container {
    .header {
        width: 100%;
        height: 1.07rem;
    }

    .head {
        width: 100%;
        background: linear-gradient(-55deg, rgb(223, 48, 28), rgb(223, 48, 28));
        text-align: center;

        h2 {
            text-align: center;
            height: 1.2549rem;
            width: 100%;
            position: relative;
            line-height: 1.2549rem;
            font-size: 0.4806rem;
            color: #fff;
            background: transparent;
            font-weight: 500;
            z-index: 3;

            .hbnh {
                position: absolute;
                left: 0.4005rem;
                font-size: 0.4272rem;
                font-weight: 500;

                .fan {
                    width: 0.2403rem;
                    height: 0.4272rem;
                    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAgCAYAAAAffCjxAAAAAXNSR0IArs4c6QAAAVdJREFUSEut1usqBVEYxvH/c1MuRJJDkhwTEpIkJUlyCEk++CJJckpycw49Gs3Wa+y9Z82ePd/Xr5n38KwRNR7bE0APMK1OHdtTwAWQGZcdQbZngPMc+QImK0O2Z4GzgIxLuqoE2Z4DTgMyJuk6K08yZHseOAnIqKSbRo2TINsLwHFARiTdxkaVQrYXgaOADEu6K3a7LWR7CTjMD2XdaYq0rZHtFWA/Rz6BIUn3reau6RvZXgX28kMfOfLQbnj/QbbXgN380HuOPJZtwB/I9jqwE5ABSc9lyJ8a2d4AtgPSL+klBfmFbG8CWwHpk/SaivxAtjMgg7Inq0mvpLcqSHehTOvKpzU+oSvFDlj99gesOJCDkp7Kip+yIknT3XL7C0tbum9lMbIMHKQkQEqwJWVSKZTPWTElq0dt6GYxt6uHf8DqX0cBq39BBqz+lR2w+j8RAfv9rfkGqF24CUdT9E4AAAAASUVORK5CYII=) no-repeat 50%;
                    background-size: 100%;
                    display: inline-block;
                    margin-right: 0.1335rem;
                    vertical-align: middle;
                    margin-top: -0.0534rem;
                }
            }
        }
    }

    .main {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        top: 1.2549rem;
        overflow-y: auto;

        iframe {
            width: 100%;
            height: 100%;
        }
    }
}
</style>