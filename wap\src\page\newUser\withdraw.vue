<template>
    <div class="container">
        <div class="header">
            <van-nav-bar title="提现" left-arrow @click-left="$router.go(-1)" fixed></van-nav-bar>
        </div>
        <div class="layout">
            <div>当前可用余额</div>
            <div class="price">¥ {{$store.state.userInfo.withdrawFunds}}</div>
        </div>
        <div class="form">
            <div class="title">提现金额</div>
            <div class="input">
                <input type="text" v-model="money" placeholder="请输入提现金额">
                <div class="all_btn" @click="withdrawalAll">全部</div>
            </div>
            <div class="title" style="margin-top: 0.3488rem;">资金密码</div>
            <div class="pwd">
                <input type="password" v-model="password" placeholder="请输入资金密码">
            </div>
            <div class="subbtn" @click="Recharge">确认提现</div>
            <div class="info">
                <p>1、银证转出请先通过实名认证和绑定银行卡。</p>
                <p>2、银证转出时间交易日9：30~11：30，13：00~15：00之间。</p>
                <p>3、每笔银证转出金额最小100元。</p>
                <p><span>4、银证转出时段内银证转出一般1小时到账，银证转出时间受银行间清算时间影响，各家银行到账时间不同，最迟T+1次日24点前到账</span></p>
            </div>
        </div>
    </div>
</template>

<script>
import * as api from "@/axios/api";
import { mapState } from "vuex";
export default {
    name: "recharge",
    data() {
        return {
            loading: false,
            money: "",
            password: "",
            userInfo: {},
            settingInfo: {},
        };
    },
    computed: {
        ...mapState(["bankInfo"]),
    },
    mounted() {
        this.getUserInfo();
        this.getSettingInfo();
    },
    methods: {
        Recharge() {
            if (
                this.money == "" ||
                this.money == null ||
                this.money == undefined
            ) {
                this.$toast("请输入转入金额");
                return;
            }
            if (
                this.password == "" ||
                this.password == null ||
                this.password == undefined
            ) {
                this.$toast("请输入通道密码");
                return;
            }
            this.handleToSure();
        },
        withdrawalAll() {
            // 点击全部银证转出
            this.money = this.userInfo.withdrawFunds;
            if (navigator.vibrate) {
                // 支持
                navigator.vibrate([55]);
            }
        },
        async handleToSure() {
            // 点击确定银证转出
            // if (!this.userInfo.idCard) {
            //     this.$toast(this.$t('hj178'));
            //     return;
            // }
            // if (!this.bankInfo.bankNo) {
            //     this.$toast(this.$t('hj179'));
            //     return;
            // } else {
            // }
            if (!this.money || this.money <= 0) {
                this.$toast(this.$t("hj180"));
            } else if (this.money - this.settingInfo.withMinAmt < 0) {
                this.$toast(this.$t("hj181") + this.settingInfo.withMinAmt);
            } else {
                let opts = {
                    amt: this.money,
                    withPwd: this.password,
                };
                this.loading = true;
                let data = await api.outMoney(opts);
                if (data.status === 0) {
                    // 成功
                    this.$toast(this.$t("hj182"));
                    this.$router.push("/user");
                } else {
                    this.$toast(data.msg ? data.msg : this.$t("hj183"));
                }
                this.loading = false;
            }
            if (navigator.vibrate) {
                // 支持
                navigator.vibrate([55]);
            }
        },
        async getUserInfo() {
            // 获取用户信息
            let data = await api.getUserInfo();
            if (data.status === 0) {
                // 判断是否登录
                this.$store.commit("dialogVisible", false);
                this.$store.state.userInfo = data.data;
                this.userInfo = data.data;
            } else {
                this.$store.commit("dialogVisible", true);
            }
        },
        async getSettingInfo() {
            let data = await api.getSetting();
            if (data.status === 0) {
                // 成功
                this.settingInfo = data.data;
                console.log(this.settingInfo, "settingInfo");
            } else {
                Toast(data.msg);
            }
        },
    },
};
</script>

<style scoped lang="less">
.container {
    font-size: 0.3256rem;
    padding: 0;

    .header {
        width: 100%;
        height: 1.07rem;
        .van-nav-bar {
            background: rgba(224, 57, 54, 1);
            /deep/ .van-nav-bar__title {
                color: #fff;
            }
            /deep/ .van-icon {
                color: #fff;
            }
            &::after {
                border: none;
            }
        }
    }
    .layout {
        background: rgba(224, 57, 54, 1);
        color: #fff;
        padding: 0.3488rem;
        font-size: 0.3255rem;
        // height: calc(100vh * 0.3);
        padding-bottom: 1.8604rem;
        .price {
            font-size: 0.6046rem;
            margin-top: 0.2325rem;
        }
    }
    .form {
        background: #fff;
        margin: 0.2325rem;
        margin-top: -1.3953rem;
        border-radius: 0.2325rem;
        padding: 0.3488rem;
        .title {
            font-size: 0.372rem;
        }
        .pwd {
            background: rgba(245, 247, 250, 1);
            border-radius: 0.2325rem;
            margin-top: 0.3488rem;
            display: flex;
            input {
                height: 1.1162rem;
                padding: 0 0.2325rem;
                font-size: 0.372rem;
                border: none;
                flex: 1;
                background: none;
                // width: 100%;
            }
        }
        .input {
            position: relative;
            display: flex;
            margin-top: 0.3488rem;
            input {
                border: none;
                border-bottom: solid 1px rgba(201, 201, 201, 1);
                flex: 1;
                height: 1.1162rem;
                font-size: 0.372rem;
            }
            .all_btn {
                position: absolute;
                right: 0;
                top: 0;
                line-height: 1.1162rem;
                color: rgba(217, 22, 1, 1);
                font-size: 0.3255rem;
            }
        }
        .subbtn {
            border-radius: 8px;
            background: rgba(217, 22, 1, 1);
            box-shadow: 0px 2px 4px rgba(224, 57, 54, 0.49);
            color: #fff;
            text-align: center;
            line-height: 1.1162rem;
            margin-top: 0.3488rem;
            font-size: 0.372rem;
        }
        .info {
            background: rgba(245, 247, 250, 1);
            border-radius: 0.2325rem;
            padding: 0.3488rem;
            margin-top: 0.9302rem;
            line-height: 200%;
            font-size: 0.3255rem;
            p {
                span {
                    color: rgba(217, 22, 1, 1);
                }
            }
        }
    }
}
</style>