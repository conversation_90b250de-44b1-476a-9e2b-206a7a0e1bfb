<template>
    <div class="position-detail-page">
        <div class="header">
            <van-nav-bar title="持仓详情" left-arrow @click-left="$router.go(-1)" fixed></van-nav-bar>
        </div>

        <!-- 股票信息 -->
        <div class="stock-card">
            <div class="stock-info">
                <div class="stock-name">{{ currentItem.stockName }}</div>
                <stock-tag-info :stock-code="currentItem.stockGid" show-code />
            </div>
            <div class="profit-info" :class="profitStatusClass">
                <div class="profit-amount">{{ formatNumber(currentItem.profitAndLose) }}</div>
                <div class="profit-ratio">{{ formatNumber(currentItem.profitAndLossRatio) }}%</div>
            </div>
        </div>

        <!-- 详细信息 -->
        <div class="detail-list">
            <div class="detail-item">
                <span class="label">股票代码</span>
                <span class="value">{{ currentItem.stockCode }}</span>
            </div>
            <div class="detail-item">
                <span class="label">持股数</span>
                <span class="value">{{ currentItem.buyNum }} 股</span>
            </div>
            <div class="detail-item">
                <span class="label">买入价格</span>
                <span class="value">¥{{ formatNumber(currentItem.buyOrderPrice) }}</span>
            </div>
            <div class="detail-item" v-if="currentItem.sellOrderId">
                <span class="label">卖出价格</span>
                <span class="value">¥{{ formatNumber(currentItem.sellOrderPrice) }}</span>
            </div>
            <div class="detail-item">
                <span class="label">买入市值</span>
                <span class="value">¥{{ formatNumber(currentItem.buyPrice) }}</span>
            </div>
            <div class="detail-item">
                <span class="label">手续费</span>
                <span class="value">¥{{ formatNumber(currentItem.orderFee) }}</span>
            </div>
            <div class="detail-item"  v-if="currentItem.sellOrderId">
                <span class="label">印花税</span>
                <span class="value">¥{{ formatNumber(currentItem.orderSpread) }}</span>
            </div>
            <div class="detail-item">
                <span class="label">买入时间</span>
                <span class="value">{{ formatTime(currentItem.buyOrderTime) }}</span>
            </div>
            <div class="detail-item" v-if="currentItem.sellOrderId">
                <span class="label">卖出时间</span>
                <span class="value">{{ formatTime(currentItem.sellOrderTime) }}</span>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-btn" v-if="currentItem.sellOrderId" @click="goBack">
            返回
        </div>
        <div class="action-btn sell-btn" v-else @click="confirmSell">
            我要平仓
        </div>


    </div>
</template>

<script>
import * as api from "@/axios/api";
import { MessageBox, Toast } from "mint-ui";
import StockTagInfo from "@/components/stock-tag-info.vue";
import dayjs from "dayjs";

export default {
    name: "PositionDetailPage",
    components: {
        StockTagInfo
    },
    data() {
        return {
            currentItem: {},
            loading: false,
        };
    },
    computed: {
        // 盈亏状态类名
        profitStatusClass() {
            if (!this.currentItem.profitAndLossRatio) return '';
            return this.currentItem.profitAndLossRatio > 0 ? 'profit' :
                this.currentItem.profitAndLossRatio < 0 ? 'loss' : 'neutral';
        },

        // 是否已卖出
        isSold() {
            return !!this.currentItem.sellOrderId;
        }
    },
    mounted() {
        this.initializeData();
    },
    methods: {
        // 初始化数据
        initializeData() {
            try {
                const itemQuery = this.$route.query.item;
                if (!itemQuery) {
                    throw new Error('缺少持仓数据');
                }

                this.currentItem = JSON.parse(decodeURIComponent(itemQuery));
                console.log('持仓详情数据:', this.currentItem);

                // 数据验证
                if (!this.currentItem.stockCode) {
                    throw new Error('持仓数据不完整');
                }

            } catch (error) {
                console.error('数据初始化失败:', error);
                this.$toast('数据加载失败');
                this.goBack();
            }
        },

        // 格式化数字
        formatNumber(number) {
            if (!number && number !== 0) return '0.00';
            return parseFloat(number).toFixed(2);
        },

        // 格式化时间
        formatTime(time) {
            if (!time) return '--';
            return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
        },

        // 返回上一页
        goBack() {
            this.$router.go(-1);
        },

        // 确认卖出
        confirmSell() {
            if (this.isSold) {
                this.$toast('该持仓已卖出');
                return;
            }

            if (this.loading) {
                return;
            }

            MessageBox.confirm(
                this.$t("hj139") + "?",
                this.$t("hj165"),
                {
                    confirmButtonText: this.$t("hj161"),
                    cancelButtonText: this.$t("hj106"),
                }
            ).then(() => {
                this.executeSell();
            }).catch((error) => {
                console.log('用户取消卖出:', error);
            });
        },

        // 执行卖出操作
        async executeSell() {
            this.loading = true;

            try {
                const params = {
                    positionSn: this.currentItem.positionSn,
                };

                if (!params.positionSn) {
                    throw new Error('持仓编号不存在');
                }

                const response = await api.sell(params);
                this.handleSellResponse(response);

            } catch (error) {
                this.handleSellError(error);
            } finally {
                this.loading = false;
            }
        },

        // 处理卖出响应
        handleSellResponse(response) {
            if (!response) {
                throw new Error('服务器无响应');
            }

            if (response.status === 0) {
                Toast(response.msg || '卖出成功');
                setTimeout(() => {
                    this.goBack();
                }, 1000);
            } else {
                this.handleSellBusinessError(response);
            }
        },

        // 处理业务错误
        handleSellBusinessError(response) {
            const errorMsg = response.msg || '卖出失败';

            if (errorMsg.indexOf("不在交易时段内") > -1) {
                Toast(this.$t("hj140"));
            } else {
                Toast(errorMsg);
            }
        },

        // 处理卖出错误
        handleSellError(error) {
            console.error('卖出操作失败:', error);

            if (error.response) {
                switch (error.response.status) {
                    case 401:
                        Toast('登录已过期，请重新登录');
                        break;
                    case 403:
                        Toast('没有操作权限');
                        break;
                    case 500:
                        Toast('服务器异常，请稍后重试');
                        break;
                    default:
                        Toast('网络请求失败');
                }
            } else {
                Toast(error.message || '操作失败，请重试');
            }
        }
    },
};
</script>

<style lang="less" scoped>
.position-detail-page {
    background: #f5f5f5;
    min-height: 100vh;
    font-size: 0.3256rem;

    .header {
        width: 100%;
        height: 1.07rem;
    }

    .stock-card {
        background: #fff;
        margin: 0.2326rem;
        padding: 0.4651rem;
        border-radius: 0.1860rem;

        .stock-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.4651rem;

            .stock-name {
                font-size: 0.4186rem;
                font-weight: 600;
                color: #333;
            }
        }

        .profit-info {
            text-align: center;
            padding: 0.2326rem;

            .profit-amount {
                font-size: 0.5116rem;
                font-weight: 600;
                margin-bottom: 0.0930rem;
            }

            .profit-ratio {
                font-size: 0.3488rem;
            }

            &.profit {

                .profit-amount,
                .profit-ratio {
                    color: #EA001B;
                }
            }

            &.loss {

                .profit-amount,
                .profit-ratio {
                    color: #00C851;
                }
            }

            &.neutral {

                .profit-amount,
                .profit-ratio {
                    color: #999;
                }
            }
        }
    }

    .detail-list {
        background: #fff;
        margin: 0.2326rem;
        border-radius: 0.1860rem;

        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.3488rem 0.4651rem;
            border-bottom: 1px solid #f0f0f0;

            &:last-child {
                border-bottom: none;
            }

            .label {
                color: #666;
                font-size: 0.3488rem;
            }

            .value {
                color: #333;
                font-weight: 500;
                font-size: 0.3488rem;
            }
        }
    }

    .action-btn {
        margin: 0.4651rem 0.2326rem;
        padding: 0.3488rem;
        background: #999;
        color: #fff;
        text-align: center;
        border-radius: 0.1860rem;
        font-size: 0.3721rem;
        font-weight: 500;

        &.sell-btn {
            background: #EA001B;
        }
    }
}
</style>