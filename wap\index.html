<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta name="viewport"
    content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no,viewport-fit=cover" />
  <title>招商证券</title>
  <link rel="shortcut icon" href="static/logo.png" type=image/x-icon>
  <!-- <link href="static/css/public1.css" rel="stylesheet" /> -->
  <!-- 完全阻止索引和跟踪 -->
  <meta name="robots" content="noindex, nofollow, noarchive, nosnippet">
  <meta name="googlebot" content="noindex, nofollow">

  <!-- 阻止缓存 -->
  <meta name="googlebot" content="noarchive">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  <meta name="theme-color" content="#ffffff">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "PingFang SC", "Microsoft YaHei", sans-serif;
      margin: 0;
      padding: 0;
      min-height: 100vh;
      width: 100vw;
      /* 禁用横向滚动 */
      overflow-x: hidden;
      /* 防止橡皮筋效果 */
      overscroll-behavior-x: none;
      /* 添加iOS安全区域支持 */
      padding-top: env(safe-area-inset-top, 0px);
      padding-bottom: env(safe-area-inset-bottom, 0px);
    }

    .splash-page-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100vh;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      z-index: 9999;
      transition: opacity 0.8s ease, visibility 0.8s ease;
      padding: 60px 20px 40px;
      background: #3691D8 url('static/3.jpg') no-repeat center top;
      background-size: 100% auto;
    }

    /* 顶部区域：Logo + 特色功能 */
    .splash-top-section {
      text-align: center;
      margin-top: 40px;
    }

    .splash-logo-area {
      margin-bottom: 24px;
    }

    .splash-main-logo {
      width: 74px;
      height: 74px;
      border-radius: 19.52px;

      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto;
      box-shadow: 0 8px 32px rgba(220, 38, 38, 0.3);
    }

    .splash-main-logo img {
      width: 74px;
      height: 74px;
      border-radius: 19.52px;
    }

    .splash-company-name {
      font-size: 20px;
      font-weight: bold;
      color: #333;
      margin-bottom: 8px;
    }

    /* 特色功能展示 */
    .splash-features-wrapper {
      width: 100%;
      max-width: 320px;
    }

    .splash-features-list {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 16px;
    }

    .splash-feature-item {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 14px;
      color: #8B5A3C;
      font-weight: 500;
    }

    .splash-feature-icon {
      width: 16px;
      height: 16px;
      background: url('static/dd.png') no-repeat center center;
      background-size: contain;
    }

    .splash-feature-separator {
      width: 1px;
      height: 16px;
      background: #D4B896;
    }

    /* 中心3D图像区域 */
    .splash-center-illustration {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
    }

    .splash-main-illustration {
      width: 264px;
      height: 264px;
      background: url('static/2.png') no-repeat center center;
      background-size: contain;
      animation: splashFloatAnimation 3s ease-in-out infinite;
    }

    /* 动画定义 */
    @keyframes splashFloatAnimation {

      0%,
      100% {
        transform: translateY(0);
      }

      50% {
        transform: translateY(-10px);
      }
    }

    /* 淡出动画 */
    .splash-page-fade-out {
      opacity: 0;
      visibility: hidden;
    }

    /* 响应式设计 */
    @media (max-width: 480px) {
      .splash-page-container {
        padding: 40px 16px 30px;
      }

      .splash-main-illustration {
        width: 220px;
        height: 220px;
      }

      .splash-features-list {
        gap: 12px;
      }

      .splash-feature-item {
        font-size: 12px;
      }

      .splash-feature-icon {
        width: 14px;
        height: 14px;
      }
    }

    @media (max-width: 360px) {
      .splash-main-illustration {
        width: 180px;
        height: 180px;
      }

      .splash-features-list {
        flex-direction: column;
        gap: 8px;
      }

      .splash-feature-separator {
        display: none;
      }
    }
  </style>

  <script>
    // 禁用缩放和双击
    function preventZoom() {
      document.documentElement.addEventListener("touchstart", function (event) {
        if (event.touches.length > 1) {
          event.preventDefault();
        }
      }, { passive: false });

      var lastTouchEnd = 0;
      document.documentElement.addEventListener("touchend", function (event) {
        var now = Date.now();
        if (now - lastTouchEnd <= 300) {
          event.preventDefault();
        }
        lastTouchEnd = now;
      }, { passive: false });
    }

    // 应用启动逻辑
    document.addEventListener("DOMContentLoaded", function () {
      preventZoom();

      const splashContainer = document.getElementById('splash-container');

      // 全局回调，供Vue应用调用
      window.hideLoadingScreen = function () {
        window.appIsReady = true;
      };

      // 简单的启动逻辑
      setTimeout(function () {
        window.appIsReady = true;

        // 延迟后隐藏加载界面
        setTimeout(function () {
          if (splashContainer) {
            splashContainer.classList.add('splash-page-fade-out');
            setTimeout(function () {
              splashContainer.style.display = 'none';
            }, 800);
          }
        }, 600);
      }, 3000); // 3秒后自动隐藏

      // 初始化标记
      window.appIsReady = false;
    });
  </script>
</head>

<body>
  <div id="app">
    <div id="splash-container" class="splash-page-container">
      <!-- 顶部区域：Logo + 特色功能 -->
      <div class="splash-top-section">
        <!-- Logo区域 -->
        <div class="splash-logo-area">
          <div class="splash-main-logo">
            <img src="static/logo.png" alt="招商证券">
          </div>
        </div>

        <!-- 特色功能展示 -->
        <div class="splash-features-wrapper">
          <div class="splash-features-list">
            <div class="splash-feature-item">
              <div class="splash-feature-icon"></div>
              <span>零佣金开户</span>
            </div>
            <div class="splash-feature-separator"></div>
            <div class="splash-feature-item">
              <div class="splash-feature-icon"></div>
              <span>极速交易</span>
            </div>
            <div class="splash-feature-separator"></div>
            <div class="splash-feature-item">
              <div class="splash-feature-icon"></div>
              <span>安全可靠</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 中心3D图像 -->
      <!-- <div class="splash-center-illustration">
        <div class="splash-main-illustration"></div>
      </div> -->
    </div>
  </div>
  <!-- built files will be auto injected -->
</body>

</html>