/* auto-build-1753669979406-rdxg2j */
webpackJsonp([16],{GS4Z:function(t,e){},IkwI:function(t,e,s){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=s("Gu7T"),a=s.n(i),n=s("mvHQ"),r=s.n(n),c=s("Xxa5"),o=s.n(c),d=s("exGp"),l=s.n(d),v=s("c2Ch"),u=s("ICZX"),_=s("oqQY"),m=s.n(_),h={name:"DazongTradingPage",components:{StockTagInfo:u.a},data:function(){return{list:[],finished:!1,loading:!0,itemIndex:0,pageNum:1,currentRequestId:0}},computed:{currentTabTitle:function(){return["我的持仓","交易记录","我的委托"][this.itemIndex]||"未知"},hasData:function(){return this.list.length>0}},mounted:function(){this.initializePage()},beforeDestroy:function(){this.currentRequestId=0},methods:{initializePage:function(){var t=this;return l()(o.a.mark(function e(){return o.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,t.changeItemIndex(0);case 3:e.next=9;break;case 5:e.prev=5,e.t0=e.catch(0),console.error("页面初始化失败:",e.t0),t.$toast("页面加载失败，请重试");case 9:case"end":return e.stop()}},e,t,[[0,5]])}))()},parseNumber:function(t){return t||0===t?parseFloat(t).toFixed(2):"0.00"},changeItemIndex:function(t){var e=this;return l()(o.a.mark(function s(){return o.a.wrap(function(s){for(;;)switch(s.prev=s.next){case 0:return s.prev=0,e.resetPageData(),e.itemIndex=t,e.currentRequestId=Date.now(),s.next=6,e.getOrderList(t);case 6:s.next=12;break;case 8:s.prev=8,s.t0=s.catch(0),console.error("切换到标签页"+t+"失败:",s.t0),e.$toast("切换标签页失败");case 12:case"end":return s.stop()}},s,e,[[0,8]])}))()},resetPageData:function(){this.pageNum=1,this.list=[],this.finished=!1},chicangDetail:function(t){t&&t.id?this.$router.push({path:"/chicangDetail?type=dazong&item="+r()(t)}):this.$toast("数据异常，无法查看详情")},weituoDetail:function(t){t&&t.id?this.$router.push({path:"/weituoDetail?item="+r()(t)}):this.$toast("数据异常，无法查看详情")},getOrderList:function(t){var e=this;return l()(o.a.mark(function s(){var i,a,n;return o.a.wrap(function(s){for(;;)switch(s.prev=s.next){case 0:return e.loading=!0,i=e.currentRequestId,s.prev=2,a={state:t,type:3,stockCode:"",stockSpell:"",pageNum:e.pageNum,pageSize:15},s.next=6,v.U(a);case 6:if(n=s.sent,i===e.currentRequestId){s.next=9;break}return s.abrupt("return");case 9:e.handleOrderListResponse(n),s.next=17;break;case 12:if(s.prev=12,s.t0=s.catch(2),i===e.currentRequestId){s.next=16;break}return s.abrupt("return");case 16:e.handleOrderListError(s.t0);case 17:return s.prev=17,e.loading=!1,s.finish(17);case 20:case"end":return s.stop()}},s,e,[[2,12,17,20]])}))()},handleOrderListResponse:function(t){if(!t||!t.data||!Array.isArray(t.data.list))throw new Error("数据格式异常");var e=t.data.list;e.length<15&&(this.finished=!0),this.list=[].concat(a()(this.list),a()(e)),this.pageNum++,console.log("加载"+this.currentTabTitle+"数据成功，本次加载"+e.length+"条")},handleOrderListError:function(t){if(console.error("获取订单列表失败:",t),t.response)switch(t.response.status){case 401:this.$toast("登录已过期，请重新登录");break;case 403:this.$toast("没有访问权限");break;case 500:this.$toast("服务器异常，请稍后重试");break;default:this.$toast("网络请求失败，请检查网络连接")}else t.request?this.$toast("网络连接失败，请检查网络"):this.$toast("数据加载失败，请重试")},formatTime:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"YYYY-MM-DD HH:mm:ss";return t?m()(t).format(e):"--"},getProfitStatus:function(t){return t?t>0?"profit":t<0?"loss":"neutral":"neutral"}}},f={render:function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"container"},[s("div",{staticClass:"header"},[s("van-nav-bar",{attrs:{title:"大宗交易记录","left-arrow":"",fixed:""},on:{"click-left":function(e){return t.$router.go(-1)}}})],1),t._v(" "),s("div",{staticClass:"trading-page__tabs"},[s("div",{class:["tab-item",{"tab-item--active":0===t.itemIndex}],on:{click:function(e){return t.changeItemIndex(0)}}},[t._m(0)]),t._v(" "),s("div",{class:["tab-item",{"tab-item--active":1===t.itemIndex}],on:{click:function(e){return t.changeItemIndex(1)}}},[t._m(1)]),t._v(" "),s("div",{class:["tab-item",{"tab-item--active":2===t.itemIndex}],on:{click:function(e){return t.changeItemIndex(2)}}},[t._m(2)])]),t._v(" "),0==t.itemIndex?s("div",{staticClass:"list"},[t._m(3),t._v(" "),s("div",{staticClass:"list_container"},[s("van-list",{attrs:{finished:t.finished,"immediate-check":!1,"finished-text":t.$t("hj43")},on:{load:function(e){return t.getOrderList(1)}},model:{value:t.loading,callback:function(e){t.loading=e},expression:"loading"}},t._l(t.list,function(e){return s("div",{key:e.id,staticClass:"item",on:{click:function(s){return t.chicangDetail(e)}}},[s("div",{staticClass:"ebox",staticStyle:{"justify-content":"left"}},[s("div",{staticClass:"stock"},[s("div",{staticClass:"name"},[t._v(t._s(e.stockName))]),t._v(" "),s("div",{staticClass:"child"},[s("stock-tag-info",{attrs:{"stock-code":e.stockGid}})],1)])]),t._v(" "),s("div",{staticClass:"cbox"},[s("span",[t._v(t._s(t.parseNumber(e.buyNum)))]),t._v(" "),s("span",[t._v(t._s(t.parseNumber(e.buyPrice)))])]),t._v(" "),s("div",{staticClass:"cbox"},[s("span",[t._v(t._s(t.parseNumber(e.now_price)))]),t._v(" "),s("span",[t._v(t._s(t.parseNumber(e.buyOrderPrice)))])]),t._v(" "),s("div",{staticClass:"cbox"},[s("span",{class:e.profitAndLose>0?"red":"green"},[t._v(t._s(e.profitAndLose))]),t._v(" "),s("span",{class:e.profitAndLossRatio>0?"red":"green"},[t._v(t._s(e.profitAndLossRatio)+"%")])])])}),0)],1)]):t._e(),t._v(" "),1==t.itemIndex?s("div",{staticClass:"list"},[t._m(4),t._v(" "),s("div",{staticClass:"list_container"},[s("van-list",{attrs:{finished:t.finished,"immediate-check":!1,"finished-text":t.$t("hj43")},on:{load:function(e){return t.getOrderList(1)}},model:{value:t.loading,callback:function(e){t.loading=e},expression:"loading"}},t._l(t.list,function(e){return s("div",{key:e.id,staticStyle:{"border-bottom":"solid 1px rgba(223, 223, 223, 1)","padding-bottom":"0.3488rem"}},[s("div",{staticClass:"item",staticStyle:{border:"none"}},[s("div",{staticClass:"ebox",staticStyle:{"justify-content":"left"}},[s("div",{staticClass:"stock"},[s("div",{staticClass:"name"},[t._v(t._s(e.stockName))]),t._v(" "),s("div",{staticClass:"child"},[s("stock-tag-info",{attrs:{"stock-code":e.stockGid}})],1)])]),t._v(" "),s("div",{staticClass:"cbox"},[s("span",[t._v(t._s(t.parseNumber(e.buyPrice)))]),t._v(" "),s("span",[t._v(t._s(t.parseNumber(e.buyNum)))])]),t._v(" "),s("div",{staticClass:"cbox"},[s("span",[t._v(t._s(t.parseNumber(e.buyOrderPrice)))]),t._v(" "),s("span",[t._v(t._s(t.parseNumber(e.sellOrderPrice)))])]),t._v(" "),s("div",{class:"cbox "+(e.profitAndLossRatio>0?"red":"green")},[s("span",[t._v(t._s(e.profitAndLose))]),t._v(" "),s("span",[t._v(t._s(e.profitAndLossRatio)+"%")])])]),t._v(" "),s("div",{staticClass:"time"},[s("div",[t._v(t._s(t.formatTime(e.buyOrderTime)))]),t._v(" "),s("div",[t._v(t._s(t.formatTime(e.sellOrderTime)))])]),t._v(" "),s("div",{staticClass:"dbtn",on:{click:function(s){return t.chicangDetail(e)}}},[t._v("查看详情")])])}),0)],1)]):t._e(),t._v(" "),2==t.itemIndex?s("div",{staticClass:"weituo_list"},[s("van-list",{attrs:{finished:t.finished,"immediate-check":!1,"finished-text":t.$t("hj43")},on:{load:function(e){return t.getOrderList(2)}},model:{value:t.loading,callback:function(e){t.loading=e},expression:"loading"}},t._l(t.list,function(e){return s("div",{key:e.id,staticClass:"weituo_item",on:{click:function(s){return t.weituoDetail(e)}}},[s("div",{staticClass:"stock"},[s("div",{staticClass:"name"},[t._v(t._s(e.stockName))]),t._v(" "),s("div",{staticClass:"child"},[s("stock-tag-info",{attrs:{"stock-code":e.stockGid}})],1)]),t._v(" "),s("div",{staticClass:"info"},[s("div",{staticClass:"item"},[s("div",[t._v("买卖类别")]),t._v(" "),s("div",[t._v("证券买入")])]),t._v(" "),s("div",{staticClass:"item"},[s("div",[t._v("当前状态")]),t._v(" "),s("div",[t._v("挂单")])]),t._v(" "),s("div",{staticClass:"item"},[s("div",[t._v("委托手数")]),t._v(" "),s("div",[t._v(t._s(e.orderNum/100))])]),t._v(" "),s("div",{staticClass:"item"},[s("div",[t._v("委托价格")]),t._v(" "),s("div",[t._v(t._s(t.parseNumber(e.buyOrderPrice)))])])])])}),0)],1):t._e()])},staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"tab-item__content"},[e("span",{staticClass:"tab-item__text"},[this._v("我的持仓")]),this._v(" "),e("span",{staticClass:"tab-item__indicator"})])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"tab-item__content"},[e("span",{staticClass:"tab-item__text"},[this._v("交易记录")]),this._v(" "),e("span",{staticClass:"tab-item__indicator"})])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"tab-item__content"},[e("span",{staticClass:"tab-item__text"},[this._v("我的委托")]),this._v(" "),e("span",{staticClass:"tab-item__indicator"})])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"list_title"},[e("div",{staticClass:"item"},[this._v("名称")]),this._v(" "),e("div",{staticClass:"item"},[this._v("持仓 | 市值")]),this._v(" "),e("div",{staticClass:"item"},[this._v("现价 | 成本")]),this._v(" "),e("div",{staticClass:"item"},[this._v("盈亏 | 涨幅")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"list_title"},[e("div",{staticClass:"item"},[this._v("股票 | 代码")]),this._v(" "),e("div",{staticClass:"item"},[this._v("本金 | 数量")]),this._v(" "),e("div",{staticClass:"item"},[this._v("买入 | 卖出价")]),this._v(" "),e("div",{staticClass:"item"},[this._v("收益 | 涨幅")])])}]};var p=s("VU/8")(h,f,!1,function(t){s("GS4Z")},"data-v-6458b5de",null);e.default=p.exports}});
/* auto-build-1753669979406-rdxg2j */