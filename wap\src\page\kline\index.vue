<template>
    <div class="page-container">
        <div class="page-header">
            <van-nav-bar left-arrow fixed @click-left="$router.go(-1)"
                @click-right="$router.push({ path: '/Searchlist' })">
                <template #title>
                    板块详情
                </template>
            </van-nav-bar>
        </div>
        <div class="page-main">
            <div class="price-overview">
                <div class="nav-title" v-if="singDetails && singDetails.gid">
                    <stock-tag-info :stock-code="singDetails.gid" :show-code="false" />
                    <div class="nav-title__text">{{ singDetails.name + '[' + singDetails.gid + ']' }}</div>
                </div>
                <div :class="`price-overview__left ${singDetails.hcrate > 0 ? 'price-up' : 'price-down'}`">
                    <div class="price-section">
                        <div class="price-current">{{ singDetails.nowPrice }}</div>
                        <div class="price-details">
                            <div class="price-change">{{ Number(singDetails.nowPrice -
                                singDetails.preclose_px).toFixed(2) }}
                            </div>
                            <div class="price-rate">{{ singDetails.hcrate }}%</div>
                        </div>
                    </div>
                </div>
                <div class="price-overview__right">
                    <div class="price-info-item">最高：<span class="price-up">{{ singDetails.today_max }}</span></div>
                    <div class="price-info-item">最低：<span class="price-down">{{ singDetails.today_min }}</span></div>
                    <div class="price-info-item">金额：{{ (Number(singDetails.business_balance) / 100000000).toFixed(2) }}
                        亿</div>
                    <div class="price-info-item">今开：{{ singDetails.open_px }}</div>
                    <div class="price-info-item">昨收：{{ singDetails.preclose_px }}</div>
                    <div class="price-info-item">成交：<span class="price-up">{{ (Number(singDetails.business_amount) /
                        10000).toFixed(2) }} 万</span>
                    </div>
                </div>
            </div>
            <div class="page-divider"></div>
            <div class="chart-container">
                <Kline :type="singDetails.type" class="chart-kline" @changeGetSingDetails="getSingDetails" />
            </div>
            <div class="page-divider"></div>
            <div class="trading-section">
                <div class="tabs-container">
                    <div :class="`tabs-item ${tabIndex == 0 ? 'tabs-item--active' : ''}`" @click="tabIndex = 0">
                        <div class="tabs-content">
                            <div class="tabs-text">最新成交</div>
                            <span class="tabs-indicator"></span>
                        </div>
                    </div>
                    <div :class="`tabs-item ${tabIndex == 1 ? 'tabs-item--active' : ''}`"
                        @click="tabIndex = 1; sellList = []; getSellList();">
                        <div class="tabs-content">
                            <div class="tabs-text">当前成交</div>
                            <span class="tabs-indicator"></span>
                        </div>
                    </div>
                </div>

                <div class="orderbook-panel" v-if="tabIndex == 0">
                    <div class="orderbook-side">
                        <div class="orderbook-title">买盘</div>
                        <div class="orderbook-list">
                            <div class="orderbook-row">
                                <span class="orderbook-label">买1</span>
                                <span class="orderbook-volume">{{ singDetails.buy1_num }}</span>
                                <span class="orderbook-price">{{ singDetails.buy1 }}</span>
                            </div>
                            <div class="orderbook-row">
                                <span class="orderbook-label">买2</span>
                                <span class="orderbook-volume">{{ singDetails.buy2_num }}</span>
                                <span class="orderbook-price">{{ singDetails.buy2 }}</span>
                            </div>
                            <div class="orderbook-row">
                                <span class="orderbook-label">买3</span>
                                <span class="orderbook-volume">{{ singDetails.buy3_num }}</span>
                                <span class="orderbook-price">{{ singDetails.buy3 }}</span>
                            </div>
                            <div class="orderbook-row">
                                <span class="orderbook-label">买4</span>
                                <span class="orderbook-volume">{{ singDetails.buy4_num }}</span>
                                <span class="orderbook-price">{{ singDetails.buy4 }}</span>
                            </div>
                            <div class="orderbook-row">
                                <span class="orderbook-label">买5</span>
                                <span class="orderbook-volume">{{ singDetails.buy5_num }}</span>
                                <span class="orderbook-price">{{ singDetails.buy5 }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="orderbook-side">
                        <div class="orderbook-title">卖盘</div>
                        <div class="orderbook-list">
                            <div class="orderbook-row orderbook-row--sell">
                                <span class="orderbook-label">卖1</span>
                                <span class="orderbook-volume">{{ singDetails.sell1_num }}</span>
                                <span class="orderbook-price">{{ singDetails.sell1 }}</span>
                            </div>
                            <div class="orderbook-row orderbook-row--sell">
                                <span class="orderbook-label">卖2</span>
                                <span class="orderbook-volume">{{ singDetails.sell2_num }}</span>
                                <span class="orderbook-price">{{ singDetails.sell2 }}</span>
                            </div>
                            <div class="orderbook-row orderbook-row--sell">
                                <span class="orderbook-label">卖3</span>
                                <span class="orderbook-volume">{{ singDetails.sell3_num }}</span>
                                <span class="orderbook-price">{{ singDetails.sell3 }}</span>
                            </div>
                            <div class="orderbook-row orderbook-row--sell">
                                <span class="orderbook-label">卖4</span>
                                <span class="orderbook-volume">{{ singDetails.sell4_num }}</span>
                                <span class="orderbook-price">{{ singDetails.sell4 }}</span>
                            </div>
                            <div class="orderbook-row orderbook-row--sell">
                                <span class="orderbook-label">卖5</span>
                                <span class="orderbook-volume">{{ singDetails.sell5_num }}</span>
                                <span class="orderbook-price">{{ singDetails.sell5 }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="positions-panel" v-if="tabIndex == 1">
                    <van-list v-model="loading" :finished="finished" :finished-text="$t('hj43')" @load="getSellList"
                        :immediate-check="immediate">
                        <div class="positions-item" v-for="value in sellList" :key="value.id">
                            <div class="positions-row">
                                <div class="positions-stock">
                                    <stock-tag-info :stock-code="value.stockGid" :show-code="false" />
                                    <div class="positions-stock__name">{{ value.stockName }}({{ value.stockGid }})</div>
                                </div>
                                <div class="positions-price">最新价格：<span class="price-up">{{ value.now_price }}</span>
                                </div>
                            </div>
                            <div class="positions-row">
                                <div class="positions-data">买入价格：{{ value.buyOrderPrice }}</div>
                                <div class="positions-data">买入数量：{{ value.buyNum / 100 }}手</div>
                            </div>
                            <div class="positions-row">
                                <div class="positions-data">市值：{{ value.buyPrice }}</div>
                            </div>
                            <div class="positions-row">
                                <div class="positions-profit">浮动盈亏：{{ value.profitAndLose }}</div>
                                <div class="positions-profit">总盈亏：{{ value.allProfitAndLose }}</div>
                            </div>
                            <div class="positions-row">
                                <div class="positions-time-label">时间</div>
                                <div class="positions-time-value">{{ value.buyOrderTime | gettime }}</div>
                            </div>
                            <div class="positions-action" @click.stop="getpingcang(value.positionSn)">我要平仓</div>
                        </div>
                    </van-list>
                </div>
            </div>
            <div class="actions-panel">

                <div class="actions-buy" @click="goBuy(0)">买</div>
                <div class="actions-sell" @click="goBuy(1)">卖</div>
                <div :class="`actions-favorite ${isOptionOpt ? 'actions-favorite--active' : ''}`" @click="option()">
                    <icon-add v-if="!isOptionOpt" :size="24" />
                    <icon-check v-else :size="24" />
                    <span class="actions-text">{{ isOptionOpt == false ? '自选' : '已加入' }}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import Kline from "./components/kLine.vue";
import * as api from "@/axios/api";
import { Toast } from "mint-ui";
import { MessageBox } from "mint-ui";
import StockTagInfo from "@/components/stock-tag-info.vue";
import IconAdd from "@/components/icon-add.vue";
import IconCheck from "@/components/icon-check.vue";

export default {
    name: "kline",
    data() {
        return {
            tabIndex: 0,
            kLineDetails: {},
            singDetails: {},
            jianjie: "",
            optionBtn: false,
            isOptionOpt: false,
            timedata: [],
            newsdetailList: [],
            finished: false,
            immediate: false,
            sellList: [],
            loading: false,
            vibrationTimer: null
        };
    },

    computed: {
        // 股票代码
        stockCode() {
            return this.kLineDetails.code || '';
        },

        // 是否为美股
        isUsStock() {
            return this.kLineDetails.if_us === "1";
        },

        // 是否为港股指数
        isHkIndex() {
            return this.kLineDetails.if_zhishu !== "0" &&
                this.singDetails.gid &&
                this.singDetails.gid.includes("hk");
        },

        // 格式化股票信息
        stockDisplayInfo() {
            if (!this.singDetails.name || !this.singDetails.gid) return '';
            return `${this.singDetails.name}[${this.singDetails.gid}]`;
        },

        // 涨跌状态
        priceStatus() {
            const rate = this.singDetails.hcrate || 0;
            return rate > 0 ? 'price-up' : 'price-down';
        }
    },

    components: {
        Kline,
        StockTagInfo,
        IconAdd,
        IconCheck
    },

    async mounted() {
        try {
            this.initializeData();

            // 并行初始化，但使用兼容性更好的方式
            const initTasks = [
                this.initStockDetails().catch(error => {
                    console.error('股票详情初始化失败:', error);
                    return null;
                }),
                this.getOptionStatus().catch(error => {
                    console.error('自选状态初始化失败:', error);
                    return null;
                }),
                this.getUserInfo().catch(error => {
                    console.error('用户信息初始化失败:', error);
                    return null;
                })
            ];

            await Promise.all(initTasks);
        } catch (error) {
            console.error('页面初始化失败:', error);
            Toast('加载失败，请重试');
        }
    },

    beforeDestroy() {
        this.cleanup();
    },

    methods: {
        // 初始化数据
        initializeData() {
            const { query } = this.$route;
            this.kLineDetails = query;
        },

        // 初始化股票详情
        async initStockDetails() {
            if (this.isUsStock) {
                await this.getSingDetailUs();
            } else {
                await this.getSingDetails();
            }
        },

        // 清理资源
        cleanup() {
            if (this.vibrationTimer) {
                clearTimeout(this.vibrationTimer);
            }
        },

        // 震动反馈
        triggerVibration() {
            if (navigator.vibrate) {
                navigator.vibrate([55]);
            }
        },

        // 获取卖单列表
        async getSellList() {
            if (this.loading) return;

            this.loading = true;
            try {
                const params = {
                    state: 0,
                    stockCode: this.singDetails.code,
                    stockSpell: "",
                    pageNum: 1,
                    pageSize: 100,
                };

                const res = await api.getOrderList(params);

                if (res.data.list.length < 15) {
                    this.finished = true;
                }

                this.sellList.push(...res.data.list);
            } catch (error) {
                console.error('获取卖单列表失败:', error);
                Toast('加载失败，请重试');
            } finally {
                this.loading = false;
            }
        },

        // 平仓操作
        async getpingcang(positionSn) {
            try {
                await MessageBox.confirm(
                    this.$t("hj139") + "?",
                    this.$t("hj165"),
                    {
                        confirmButtonText: this.$t("hj161"),
                        cancelButtonText: this.$t("hj106"),
                    }
                );

                const data = await api.sell({ positionSn });

                if (data.status === 0) {
                    Toast(data.msg);
                    this.refreshPositionData();
                } else if (data.msg && data.msg.includes("不在交易时段内")) {
                    Toast(this.$t("hj140"));
                } else {
                    Toast(data.msg || '操作失败');
                }
            } catch (error) {
                if (error !== 'cancel') {
                    console.error('平仓操作失败:', error);
                    Toast('操作失败，请重试');
                }
            }
        },

        // 刷新持仓数据
        refreshPositionData() {
            this.finished = false;
            // 这些方法在当前代码中没有定义，可能需要根据实际情况调整
            if (typeof this.getListDetail === 'function') {
                this.getListDetail();
            }
            if (typeof this.init === 'function') {
                this.init();
            }
        },

        // 跳转到买卖页面
        goBuy(tab) {
            this.$router.push({
                path: "/buyStocks",
                query: {
                    name: this.singDetails.name,
                    code: this.singDetails.code,
                    m: this.singDetails.nowPrice,
                    id: this.singDetails.id,
                    tab,
                    symbol: this.singDetails.gid,
                },
            });
        },

        // 获取港股新闻
        async getHknews() {
            try {
                const res = await api.queryIndexNews();
                if (res.status === 0) {
                    this.newsdetailList = res.data.data;
                }
            } catch (error) {
                console.error('获取港股新闻失败:', error);
            }
        },

        // 获取非港股新闻
        async getNohknews() {
            try {
                const data = await api.queryNewsList(4);
                this.newsdetailList = data.data.list;
            } catch (error) {
                console.error('获取新闻失败:', error);
            }
        },

        // 自选股操作
        async option() {
            if (this.optionBtn) return;

            this.optionBtn = true;

            try {
                const apiMethod = this.isOptionOpt ? api.delOption : api.addOption;
                const data = await apiMethod({ code: this.stockCode });

                if (data.status === 0) {
                    await this.getOptionStatus();
                    const message = this.isOptionOpt ?this.$t("hj96") : this.$t("hj97") ;
                    Toast(message);
                } else {
                    Toast(data.msg || '操作失败');
                }

                this.triggerVibration();
            } catch (error) {
                console.error('自选股操作失败:', error);
                Toast('操作失败，请重试');
            } finally {
                this.optionBtn = false;
            }
        },

        // 获取用户信息
        async getUserInfo() {
            try {
                const data = await api.getUserInfo();
                if (data.status === 0) {
                    this.$store.state.userInfo = data.data;
                } else {
                    Toast(data.msg || '获取用户信息失败');
                }
            } catch (error) {
                console.error('获取用户信息失败:', error);
                Toast('获取用户信息失败');
            }
        },

        // 获取自选状态
        async getOptionStatus() {
            try {
                const data = await api.isOption({ code: this.stockCode });
                this.isOptionOpt = data.status !== 0;
            } catch (error) {
                console.error('获取自选状态失败:', error);
            }
        },

        // 获取股票详情
        async getSingDetails() {
            try {
                const params = {
                    code: this.stockCode,
                    stockType: this.kLineDetails.type,
                };

                const res = await api.getSingleStock(params);

                if (res.status === 0) {
                    this.singDetails = res.data.stock;
                    this.jianjie = res.data.introduction || res.data.indexintroduction || '';

                    // 获取相应新闻
                    if (this.isHkIndex) {
                        await this.getHknews();
                    } else {
                        await this.getNohknews();
                    }
                }
            } catch (error) {
                console.error('获取股票详情失败:', error);
                Toast('获取股票详情失败');
            }
        },

        // 获取美股详情
        async getSingDetailUs() {
            try {
                const params = {
                    code: this.stockCode,
                    stockType: this.kLineDetails.type,
                };

                const res = await api.getUsDetail(params);

                if (res.status === 0) {
                    this.singDetails = res.data.stock;
                    this.timedata = res.data.timedata;
                    this.jianjie = res.data.introduction || res.data.indexintroduction || '';

                    await this.getNohknews();
                }
            } catch (error) {
                console.error('获取美股详情失败:', error);
                Toast('获取美股详情失败');
            }
        },

        // 格式化时间
        formatTime(time) {
            if (!time) return "";

            const date = new Date(time);
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hour = String(date.getHours()).padStart(2, '0');
            const minute = String(date.getMinutes()).padStart(2, '0');
            const second = String(date.getSeconds()).padStart(2, '0');

            return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
        },

        // 截取名称
        truncateName(name) {
            return name && name.length > 15 ? name.substring(0, 14) : name;
        }
    },

    // 保留filters以兼容模板中的使用
    filters: {
        getName(name) {
            if (!name) return '';
            return name.length > 15 ? name.substring(0, 14) : name;
        },
        gettime(time) {
            if (!time) return "";

            const date = new Date(time);
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hour = String(date.getHours()).padStart(2, '0');
            const minute = String(date.getMinutes()).padStart(2, '0');
            const second = String(date.getSeconds()).padStart(2, '0');

            return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
        }
    }
};
</script>

<style lang="less" scoped>
/* ==========================================================================
   页面主体样式
   ========================================================================== */
.page-container {
    font-size: 0.3256rem;
    padding: 0;
    background: #fff;
    min-height: 100vh;
    padding-bottom: 1.8603rem;
}

.page-header {
    width: 100%;
    height: 1.07rem;
}

.page-main {
    background: #fff;
}

.page-divider {
    height: 0.3488rem;
    width: 100%;
    background: rgba(245, 247, 250, 1);
}

/* ==========================================================================
   导航栏模块样式
   ========================================================================== */
.nav-title {
    display: flex;
    align-items: center;
    padding-left: 10px;
    margin-bottom: 10px;
    padding-top: 10px;

    &__text {

        /** 文本1 */
        font-size: 16px;
        font-weight: 400;
        letter-spacing: 0px;
        line-height: 21.95px;
        color: rgba(29, 36, 51, 1);
        text-align: left;
        margin-left: 10px;

    }
}

/* ==========================================================================
   价格显示模块样式
   ========================================================================== */
.price-overview {
    display: flex;
    flex-direction: column;
    padding-bottom: 10px;
    background: #fff;

    &__left {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        gap: 13px;
    }

    &__right {
        font-size: 0.279rem;
        flex: 1;
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        margin-left: 0.2326rem;
        line-height: 0.8372rem;
    }
}

.price-section {
    padding-left: 10px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 13px;
}

.price-current {
    /** 文本1 */
    font-size: 32px;
    font-weight: bolder;
    letter-spacing: 0px;
    line-height: 39.07px;

}

.price-details {
    display: flex;
    justify-content: space-between;
}

.price-change,
.price-rate {

    /** 文本1 */
    font-size: 13px;
    font-weight: bolder;
    letter-spacing: 0px;
    margin-right: 10px;
}

.price-info-item {
    margin-bottom: 7px;

    /** 文本1 */
    font-size: 13px;
    font-weight: bolder;
    letter-spacing: 0px;
    line-height: 15.87px;
    color: rgba(117, 117, 117, 1);

}

/* ==========================================================================
   K线图模块样式
   ========================================================================== */
.chart-container {
    background: #fff;
}

.chart-kline {
    height: 8.1395rem;
}

/* ==========================================================================
   交易面板模块样式
   ========================================================================== */
.trading-section {
    background: #fff;
}

/* 标签页样式 */
.tabs-container {
    display: flex;
    border-bottom: solid 1px rgba(223, 223, 223, 1);
}

.tabs-item {
    flex: 1;
    display: flex;
    justify-content: center;
    padding: 0.3488rem 0;
    padding-bottom: 0;
    font-size: 0.3721rem;
    color: rgba(148, 149, 154, 1);

    &--active {
        color: #333;

        .tabs-indicator {
            display: block;
        }
    }
}

.tabs-content {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.tabs-text {
    margin-bottom: 0.2rem;
}

.tabs-indicator {
    margin-top: 0.3488rem;
    background: rgba(224, 57, 54, 1);
    height: 2px;
    display: none;
    width: 100%;
}

/* ==========================================================================
   买卖盘模块样式
   ========================================================================== */
.orderbook-panel {
    display: flex;
    padding-top: 0.3488rem;
}

.orderbook-side {
    flex: 1;
    padding: 0.3488rem;
    padding-top: 0;
}

.orderbook-title {
    font-weight: bold;
    margin-bottom: 0.3488rem;
}

.orderbook-list {
    display: flex;
    flex-direction: column;
}

.orderbook-row {
    display: flex;
    justify-content: space-between;
    margin-top: 0.3488rem;
    color: #f70202;

    &--sell {
        color: #01573d;
    }
}

.orderbook-label {
    color: rgba(181, 181, 181, 1);
    min-width: 1.5rem;
}

.orderbook-volume {
    flex: 1;
    text-align: center;
    margin-right: 0.2326rem;
}

.orderbook-price {
    color: #333;
    text-align: right;
}

/* ==========================================================================
   持仓列表模块样式
   ========================================================================== */
.positions-panel {
    padding: 0 0.3488rem;
}

.positions-item {
    border-bottom: solid 1px rgba(223, 223, 223, 1);
    font-size: 0.3256rem;
    line-height: 0.6976rem;
    padding: 0.3488rem 0;
}

.positions-row {
    display: flex;
    justify-content: space-between;
}

.positions-stock {
    display: flex;
    align-items: center;

    &__name {
        margin-left: 0.1162rem;
    }
}

.positions-price,
.positions-data,
.positions-profit {
    font-size: 0.3256rem;
    line-height: 0.6976rem;
}

.positions-time-label {
    color: rgba(125, 125, 125, 1);
}

.positions-time-value {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.positions-action {
    background: rgba(240, 240, 240, 1);
    height: 0.6976rem;
    line-height: 0.6976rem;
    border-radius: 0.3333rem;
    width: 2.3255rem;
    text-align: center;
    margin: auto;
    margin-top: 0.3488rem;
    cursor: pointer;

    &:hover {
        background: rgba(230, 230, 230, 1);
    }
}

/* ==========================================================================
   操作栏模块样式
   ========================================================================== */
.actions-panel {
    background: #fff;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    height: 44px;
    gap: 17px;
    margin: 0 10px;
    padding-bottom: 10px;
}

.actions-favorite {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid rgba(245, 247, 250, 1);

    &--active {
        color: rgba(14, 201, 177, 1);
    }
}

.actions-text {
    margin-left: 0.2325rem;
}

.actions-buy {
    flex: 1;
    font-size: 0.372rem;
    background: rgba(215, 12, 24, 1);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;

}

.actions-sell {
    flex: 1;
    font-size: 0.372rem;
    background: rgba(37, 103, 255, 1);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;

}

/* ==========================================================================
   通用样式类
   ========================================================================== */
.price-up {
    color: rgba(217, 22, 1, 1);

}

.price-down {
    color: rgba(0, 164, 68, 1);

}
</style>