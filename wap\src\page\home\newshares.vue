<template>
    <div class="new-shares-page">
        <div class="header">
            <van-nav-bar :title="type == 1 ? '新股申购' : '线下配售'" left-arrow @click-left="$router.go(-1)" fixed
                right-text="申购记录" @click-right="goToHistory">
            </van-nav-bar>
        </div>

        <div class="shares-list">
            <van-list v-model="loading" :finished="finished" :finished-text="$t('hj43')" @load="loadSharesList"
                :disabled="true" direction="down">

                <!-- 空状态 -->
                <div v-if="!loading && !hasData" class="empty-state">
                    <div class="empty-icon">📈</div>
                    <div class="empty-text">暂无可申购</div>
                    <div class="empty-hint">请关注最新发行信息</div>
                </div>

                <!-- 新股列表 -->
                <div v-else v-for="item in list" class="share-item" :key="item.id" @click="viewDetail(item)">
                    <!-- 股票信息头部 -->
                    <div class="stock-header">
                        <div class="stock-info">
                            <stock-tag-info :stock-code="getStockGid(item)" />
                            <span class="stock-name">{{ item.name }}</span>
                        </div>
                        <div class="arrow-icon">
                            <svg class="icon" viewBox="0 0 1024 1024">
                                <path d="M327.6 950.8l435.8-436.2-435.8-439-45.4 45.1 390.9 393.8-390.8 391.1z"></path>
                            </svg>
                        </div>
                    </div>

                    <!-- 详细信息 -->
                    <div class="detail-grid">
                        <div class="detail-item">
                            <div class="detail-label">发行价格</div>
                            <div class="detail-value price">¥{{ formatPrice(item.price) }}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">发行总数</div>
                            <div class="detail-value">{{ formatNumber(item.orderNumber) }}万股</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">市盈率</div>
                            <div class="detail-value">{{ formatPE(item.pe) }}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">申购时间</div>
                            <div class="detail-value">{{ formatTime(item.subscribeTime) }}</div>
                        </div>
                    </div>

                    <!-- 状态标识 -->
                    <div class="status-indicator">
                        <span class="status-text">点击申购</span>
                    </div>
                </div>
            </van-list>
        </div>
    </div>
</template>

<script>
import * as api from "@/axios/api";
import StockTagInfo from "@/components/stock-tag-info.vue";
import dayjs from "dayjs";

export default {
    name: "NewSharesPage",
    components: {
        StockTagInfo
    },
    data() {
        return {
            list: [],
            pageNum: 1,
            finished: false,
            loading: false,
            pageSize: 9,
            type: 1,
        };
    },
    computed: {
        // 是否有数据
        hasData() {
            return this.list && this.list.length > 0;
        }
    },
    mounted() {
        this.initializePage();
        this.type = parseInt(this.$route.query.type) || 1;
    },
    methods: {
        // 初始化页面
        async initializePage() {
            try {
                console.log('新股申购页面初始化');
                // 立即加载第一页数据
                await this.loadSharesList();
            } catch (error) {
                console.error('页面初始化失败:', error);
                this.$toast('页面加载失败，请重试');
            }
        },

        // 加载新股列表
        async loadSharesList() {
            if (this.loading || this.finished) {
                return;
            }

            this.loading = true;

            try {
                const params = {
                    pageNum: this.pageNum,
                    pageSize: this.pageSize,
                    type: this.type
                };

                const response = await api.getNewGu(params);
                this.handleListResponse(response);

            } catch (error) {
                this.handleListError(error);
            } finally {
                this.loading = false;
            }
        },

        // 处理列表响应
        handleListResponse(response) {
            if (!response || !response.data || !Array.isArray(response.data.list)) {
                throw new Error('数据格式异常');
            }

            const newItems = response.data.list;

            // 检查是否还有更多数据
            if (newItems.length < this.pageSize) {
                this.finished = true;
            }

            // 添加新数据到列表
            this.list.push(...newItems);
            this.pageNum++;

            console.log(`加载新股数据成功，当前页：${this.pageNum - 1}，新增：${newItems.length}条，总计：${this.list.length}条`);
        },

        // 处理列表错误
        handleListError(error) {
            console.error('获取新股列表失败:', error);

            if (error.response) {
                switch (error.response.status) {
                    case 401:
                        this.$toast('登录已过期，请重新登录');
                        break;
                    case 403:
                        this.$toast('没有访问权限');
                        break;
                    case 500:
                        this.$toast('服务器异常，请稍后重试');
                        break;
                    default:
                        this.$toast('网络请求失败，请检查网络连接');
                }
            } else {
                this.$toast('数据加载失败，请下拉重试');
            }

            // 发生错误时，允许重试
            this.finished = false;
        },

        // 获取股票GID（用于标签组件）
        getStockGid(item) {
            if (!item.stockType) return '';

            const typeMap = {
                '深': 'sz',
                '创': 'sz',
                '沪': 'sh',
                '科': 'sh',
                '北': 'bj'
            };

            const prefix = typeMap[item.stockType] || 'sz';
            return `${prefix}${item.code}`;
        },

        // 跳转到申购记录
        goToHistory() {
            this.$router.push({
                path: '/peishouhistory',
                query: { type: this.type }
            });
        },

        // 查看详情
        viewDetail(item) {

            try {
                this.$router.push({
                    path: "/newsharesdetail",
                    query: {
                        item: JSON.stringify(item),
                        type: this.type
                    }
                });
            } catch (error) {
                console.error('跳转详情页失败:', error);
                this.$toast('页面跳转失败');
            }
        },

        // 格式化价格
        formatPrice(price) {
            if (!price && price !== 0) return '--';
            return parseFloat(price).toFixed(2);
        },

        // 格式化数字
        formatNumber(number) {
            if (!number && number !== 0) return '--';
            return parseInt(number).toLocaleString();
        },

        // 格式化市盈率
        formatPE(pe) {
            if (!pe && pe !== 0) return '--';
            const peValue = parseFloat(pe);
            return isNaN(peValue) ? '--' : peValue.toFixed(2);
        },

        // 格式化时间
        formatTime(time) {
            if (!time) return '--';
            return dayjs(time).format('YYYY-MM-DD');
        }
    },
};
</script>

<style lang="less" scoped>
.new-shares-page {
    background: #f5f5f5;
    min-height: 100vh;
    font-size: 0.3256rem;

    .header {
        width: 100%;
        height: 1.07rem;
    }

    .shares-list {
        margin: 0.2326rem;

        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 1.1627rem 0;
            background: #fff;
            border-radius: 0.1860rem;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

            .empty-icon {
                font-size: 1.1627rem;
                margin-bottom: 0.2326rem;
                opacity: 0.5;
            }

            .empty-text {
                color: #2c3e50;
                font-size: 0.3721rem;
                font-weight: 600;
                margin-bottom: 0.1162rem;
            }

            .empty-hint {
                color: #95a5a6;
                font-size: 0.3256rem;
            }
        }

        .share-item {
            background: #fff;
            border-radius: 0.1860rem;
            margin-bottom: 0.2326rem;
            padding: 0.4651rem;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;

            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 0.0930rem;
                height: 100%;
                background: linear-gradient(135deg, #EA001B, #FF6B3D);
                opacity: 0;
                transition: all 0.3s ease;
            }

            // &:hover {
            //     transform: translateY(-2px);
            //     box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);

            //     &::before {
            //         opacity: 1;
            //     }

            //     .arrow-icon .icon {
            //         fill: #EA001B;
            //         transform: translateX(0.0697rem);
            //     }
            // }

            &:active {
                transform: translateY(0);
            }

            .stock-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 0.4651rem;
                padding-bottom: 0.3488rem;
                border-bottom: 1px solid #f0f0f0;

                .stock-info {
                    display: flex;
                    align-items: center;
                    gap: 0.2326rem;

                    .stock-name {
                        font-size: 0.3721rem;
                        font-weight: 600;
                        color: #2c3e50;
                    }
                }

                .arrow-icon {
                    .icon {
                        width: 0.3488rem;
                        height: 0.3488rem;
                        fill: #bdc3c7;
                        transition: all 0.3s ease;
                    }
                }
            }

            .detail-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 0.3488rem;
                margin-bottom: 0.3488rem;

                .detail-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .detail-label {
                        color: #7f8c8d;
                        font-size: 0.3256rem;
                        font-weight: 500;
                    }

                    .detail-value {
                        color: #2c3e50;
                        font-size: 0.3256rem;
                        font-weight: 600;
                        font-family: 'Arial', 'Helvetica', monospace;

                        &.price {
                            color: #EA001B;
                            font-weight: 700;
                        }
                    }
                }
            }

            .status-indicator {
                display: flex;
                justify-content: flex-end;
                align-items: center;
                padding-top: 0.2326rem;
                border-top: 1px solid #f0f0f0;

                .status-text {
                    color: #EA001B;
                    font-size: 0.3256rem;
                    font-weight: 600;
                    display: flex;
                    align-items: center;
                    gap: 0.1162rem;

                    &::after {
                        content: '→';
                        font-weight: bold;
                        transition: all 0.3s ease;
                    }
                }
            }
        }
    }
}

/* 加载状态样式 */
:deep(.van-list) {
    .van-list__finished-text {
        color: #95a5a6;
        font-size: 0.3256rem;
        padding: 0.4651rem 0;
    }

    .van-list__loading {
        padding: 0.4651rem 0;
    }
}

/* 动画效果 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(0.4651rem);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.share-item {
    animation: slideInUp 0.3s ease-out;
}

.share-item:nth-child(odd) {
    animation-delay: 0.05s;
}

.share-item:nth-child(even) {
    animation-delay: 0.1s;
}

.share-item:nth-child(3n) {
    animation-delay: 0.15s;
}

/* 响应式适配 */
@media (max-width: 375px) {
    .new-shares-page {
        .shares-list {
            margin: 0.1162rem;

            .share-item {
                padding: 0.3488rem;
                margin-bottom: 0.1162rem;
            }
        }
    }
}
</style>