<template>
    <div class="reset-password-page">
        <div class="header">
            <van-nav-bar title="修改登录密码" left-arrow fixed @click-left="$router.go(-1)" />
        </div>

        <div class="password-form">
            <!-- 表单说明 -->
            <div class="form-intro">
                <div class="intro-icon">🔐</div>
                <div class="intro-text">
                    <div class="intro-title">修改登录密码</div>
                    <div class="intro-desc">为了您的账户安全，请设置复杂度较高的密码</div>
                </div>
            </div>

            <!-- 旧密码输入 -->
            <div class="form-group">
                <div class="form-label">
                    <span class="label-text">旧密码</span>
                    <span class="label-required">*</span>
                </div>
                <div class="input-wrapper" :class="{ 'error': errors.oldPassword, 'focused': focusStates.oldPassword }">
                    <div class="input-icon">
                        <svg class="icon" viewBox="0 0 1024 1024">
                            <path
                                d="M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32z">
                            </path>
                        </svg>
                    </div>
                    <input type="password" placeholder="请输入当前密码" v-model="formData.oldPassword"
                        @focus="handleFocus('oldPassword')" @blur="handleBlur('oldPassword')"
                        @input="clearError('oldPassword')" :disabled="isSubmitting" />
                </div>
                <div class="error-message" v-if="errors.oldPassword">{{ errors.oldPassword }}</div>
            </div>

            <!-- 新密码输入 -->
            <div class="form-group">
                <div class="form-label">
                    <span class="label-text">新密码</span>
                    <span class="label-required">*</span>
                </div>
                <div class="input-wrapper" :class="{ 'error': errors.newPassword, 'focused': focusStates.newPassword }">
                    <div class="input-icon">
                        <svg class="icon" viewBox="0 0 1024 1024">
                            <path
                                d="M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32z">
                            </path>
                        </svg>
                    </div>
                    <input :type="passwordVisibility.newPassword ? 'text' : 'password'" placeholder="请输入新密码（6-20位）"
                        v-model="formData.newPassword" @focus="handleFocus('newPassword')"
                        @blur="handleBlur('newPassword')" @input="handleNewPasswordInput" :disabled="isSubmitting" />
                    <div class="password-toggle" @click="togglePasswordVisibility('newPassword')"
                        v-if="formData.newPassword">
                        <van-icon :name="passwordVisibility.newPassword ? 'eye-o' : 'eye'" />
                    </div>
                </div>
                <div class="error-message" v-if="errors.newPassword">{{ errors.newPassword }}</div>

                <!-- 密码强度指示器 -->
                <div class="password-strength" v-if="formData.newPassword">
                    <div class="strength-label">密码强度：</div>
                    <div class="strength-bars">
                        <div class="strength-bar"
                            :class="{ active: passwordStrength >= 1, weak: passwordStrength === 1 }"></div>
                        <div class="strength-bar"
                            :class="{ active: passwordStrength >= 2, medium: passwordStrength === 2 }"></div>
                        <div class="strength-bar"
                            :class="{ active: passwordStrength >= 3, strong: passwordStrength === 3 }"></div>
                    </div>
                    <div class="strength-text" :class="strengthClass">{{ strengthText }}</div>
                </div>
            </div>

            <!-- 确认密码输入 -->
            <div class="form-group">
                <div class="form-label">
                    <span class="label-text">确认新密码</span>
                    <span class="label-required">*</span>
                </div>
                <div class="input-wrapper"
                    :class="{ 'error': errors.confirmPassword, 'focused': focusStates.confirmPassword }">
                    <div class="input-icon">
                        <svg class="icon" viewBox="0 0 1024 1024">
                            <path
                                d="M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0 0 51.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z">
                            </path>
                            <path
                                d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z">
                            </path>
                        </svg>
                    </div>
                    <input :type="passwordVisibility.confirmPassword ? 'text' : 'password'" placeholder="请再次输入新密码"
                        v-model="formData.confirmPassword" @focus="handleFocus('confirmPassword')"
                        @blur="handleBlur('confirmPassword')" @input="handleConfirmPasswordInput"
                        :disabled="isSubmitting" />
                    <div class="password-toggle" @click="togglePasswordVisibility('confirmPassword')"
                        v-if="formData.confirmPassword">
                        <van-icon :name="passwordVisibility.confirmPassword ? 'eye-o' : 'eye'" />
                    </div>
                </div>
                <div class="error-message" v-if="errors.confirmPassword">{{ errors.confirmPassword }}</div>
                <div class="success-message" v-if="isPasswordMatched && formData.confirmPassword">密码匹配</div>
            </div>

            <!-- 提交按钮 -->
            <div class="submit-button" @click="handleSubmit"
                :class="{ 'loading': isSubmitting, 'disabled': !isFormValid }">
                <van-loading v-if="isSubmitting" size="20px" color="#fff" />
                <span v-else>{{ isSubmitting ? '提交中...' : '确认修改' }}</span>
            </div>

            <!-- 密码规则提示 -->
            <div class="password-rules">
                <div class="rules-title">密码规则：</div>
                <div class="rules-list">
                    <div class="rule-item" :class="{ 'valid': passwordRules.length }">
                        <span class="rule-icon">{{ passwordRules.length ? '✓' : '○' }}</span>
                        <span class="rule-text">长度为6-20位字符</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import * as api from "@/axios/api";
import { isNull, pwdReg } from "@/utils/utils";
import { Toast } from "mint-ui";

export default {
    name: "ResetPasswordPage",
    data() {
        return {
            formData: {
                oldPassword: "",
                newPassword: "",
                confirmPassword: "",
            },
            passwordVisibility: {
                oldPassword: false,
                newPassword: false,
                confirmPassword: false,
            },
            focusStates: {
                oldPassword: false,
                newPassword: false,
                confirmPassword: false,
            },
            errors: {},
            isSubmitting: false,
        };
    },
    computed: {
        // 密码强度计算
        passwordStrength() {
            const password = this.formData.newPassword;
            if (!password) return 0;

            let strength = 0;

            // 长度检查
            if (password.length >= 6) strength++;
            if (password.length >= 12) strength++;
            if (password.length >= 18) strength++;

            return Math.min(strength, 3);
        },

        // 强度等级类名
        strengthClass() {
            switch (this.passwordStrength) {
                case 1: return 'weak';
                case 2: return 'medium';
                case 3: return 'strong';
                default: return '';
            }
        },

        // 强度文本
        strengthText() {
            switch (this.passwordStrength) {
                case 1: return '短';
                case 2: return '中';
                case 3: return '长';
                default: return '';
            }
        },

        // 密码规则验证
        passwordRules() {
            const password = this.formData.newPassword;
            return {
                length: password.length >= 6 && password.length <= 20,
            };
        },

        // 密码是否匹配
        isPasswordMatched() {
            return this.formData.newPassword &&
                this.formData.confirmPassword &&
                this.formData.newPassword === this.formData.confirmPassword;
        },

        // 表单是否有效
        isFormValid() {
            return this.formData.oldPassword &&
                this.formData.newPassword &&
                this.formData.confirmPassword &&
                this.isPasswordMatched &&
                Object.values(this.passwordRules).every(rule => rule) &&
                Object.keys(this.errors).length === 0;
        }
    },
    mounted() {
        this.initializePage();
    },
    methods: {
        // 初始化页面
        initializePage() {
            console.log('重置密码页面初始化');
        },

        // 处理焦点状态
        handleFocus(field) {
            this.focusStates[field] = true;
        },

        handleBlur(field) {
            this.focusStates[field] = false;
            this.validateField(field);
        },

        // 切换密码可见性
        togglePasswordVisibility(field) {
            this.passwordVisibility[field] = !this.passwordVisibility[field];
        },

        // 清除错误
        clearError(field) {
            if (this.errors[field]) {
                this.$delete(this.errors, field);
            }
        },

        // 处理新密码输入
        handleNewPasswordInput() {
            this.clearError('newPassword');
            if (this.formData.confirmPassword) {
                this.validateField('confirmPassword');
            }
        },

        // 处理确认密码输入
        handleConfirmPasswordInput() {
            this.clearError('confirmPassword');
        },

        // 字段验证
        validateField(field) {
            switch (field) {
                case 'oldPassword':
                    if (!this.formData.oldPassword.trim()) {
                        this.$set(this.errors, field, '请输入旧密码');
                    }
                    break;

                case 'newPassword':
                    if (!this.formData.newPassword.trim()) {
                        this.$set(this.errors, field, '请输入新密码');
                    } else if (this.formData.newPassword.length < 6 || this.formData.newPassword.length > 20) {
                        this.$set(this.errors, field, '密码长度必须在6-20位之间');
                    } else if (this.formData.newPassword === this.formData.oldPassword) {
                        this.$set(this.errors, field, '新密码不能与旧密码相同');
                    }
                    break;

                case 'confirmPassword':
                    if (!this.formData.confirmPassword.trim()) {
                        this.$set(this.errors, field, '请再次输入新密码');
                    } else if (this.formData.confirmPassword !== this.formData.newPassword) {
                        this.$set(this.errors, field, '两次输入的密码不一致');
                    }
                    break;
            }
        },

        // 表单验证
        validateForm() {
            this.errors = {};

            // 验证所有字段
            ['oldPassword', 'newPassword', 'confirmPassword'].forEach(field => {
                this.validateField(field);
            });

            return Object.keys(this.errors).length === 0;
        },

        // 处理提交
        async handleSubmit() {
            if (!this.isFormValid || this.isSubmitting) {
                return;
            }

            if (!this.validateForm()) {
                return;
            }

            this.isSubmitting = true;

            try {
                await this.executePasswordChange();
            } catch (error) {
                this.handleSubmitError(error);
            } finally {
                this.isSubmitting = false;
                this.triggerVibration();
            }
        },

        // 执行密码修改
        async executePasswordChange() {
            const params = {
                oldPwd: this.formData.oldPassword,
                newPwd: this.formData.newPassword,
            };

            const response = await api.changePassword(params);
            this.handlePasswordChangeResponse(response);
        },

        // 处理密码修改响应
        handlePasswordChangeResponse(response) {
            if (!response) {
                throw new Error('服务器无响应');
            }

            if (response.status === 0) {
                Toast('密码修改成功');
                setTimeout(() => {
                    this.navigateToUser();
                }, 1500);
            } else {
                const errorMsg = response.msg || '密码修改失败';
                Toast(errorMsg);

                // 根据错误类型设置具体错误
                if (errorMsg.includes('旧密码')) {
                    this.$set(this.errors, 'oldPassword', errorMsg);
                }
            }
        },

        // 处理提交错误
        handleSubmitError(error) {
            console.error('密码修改失败:', error);

            if (error.response) {
                switch (error.response.status) {
                    case 401:
                        Toast('登录已过期，请重新登录');
                        break;
                    case 403:
                        Toast('没有修改权限');
                        break;
                    case 500:
                        Toast('服务器异常，请稍后重试');
                        break;
                    default:
                        Toast('网络请求失败');
                }
            } else {
                Toast(error.message || '密码修改失败，请重试');
            }
        },

        // 导航到用户页面
        navigateToUser() {
            try {
                this.$router.push({ path: "/user" });
            } catch (error) {
                console.error('导航失败:', error);
                this.$router.go(-1);
            }
        },

        // 触发震动反馈
        triggerVibration() {
            if (navigator.vibrate) {
                navigator.vibrate([55]);
            }
        },

        // 兼容原有方法名
        async changeLoginPsd() {
            await this.handleSubmit();
        }
    },
};
</script>

<style lang="less" scoped>
.reset-password-page {
    background: #f5f5f5;
    min-height: 100vh;
    font-size: 0.3256rem;

    .header {
        width: 100%;
        height: 1.07rem;
    }

    .password-form {
        padding: 0.4651rem 0.2326rem;

        .form-intro {
            background: #fff;
            border-radius: 0.1860rem;
            padding: 0.4651rem;
            margin-bottom: 0.4651rem;
            display: flex;
            align-items: center;
            gap: 0.3488rem;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

            .intro-icon {
                font-size: 1.1627rem;
                background: linear-gradient(135deg, #EA001B, #FF6B3D);
                width: 1.3953rem;
                height: 1.3953rem;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 0 4px 12px rgba(234, 0, 27, 0.3);
            }

            .intro-text {
                flex: 1;

                .intro-title {
                    font-size: 0.4186rem;
                    font-weight: 600;
                    color: #2c3e50;
                    margin-bottom: 0.1162rem;
                }

                .intro-desc {
                    font-size: 0.3256rem;
                    color: #7f8c8d;
                    line-height: 1.4;
                }
            }
        }

        .form-group {
            background: #fff;
            border-radius: 0.1860rem;
            padding: 0.4651rem;
            margin-bottom: 0.2326rem;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

            .form-label {
                display: flex;
                align-items: center;
                margin-bottom: 0.2326rem;

                .label-text {
                    font-size: 0.3721rem;
                    font-weight: 600;
                    color: #2c3e50;
                }

                .label-required {
                    color: #EA001B;
                    margin-left: 0.0930rem;
                    font-size: 0.3721rem;
                }
            }

            .input-wrapper {
                position: relative;
                display: flex;
                align-items: center;
                background: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 0.1860rem;
                transition: all 0.3s ease;

                &.focused {
                    border-color: #EA001B;
                    background: #fff;
                    box-shadow: 0 0 0 0.0697rem rgba(234, 0, 27, 0.1);
                }

                &.error {
                    border-color: #dc3545;
                    background: #fff5f5;
                }

                .input-icon {
                    padding: 0 0.2326rem;

                    .icon {
                        width: 0.4651rem;
                        height: 0.4651rem;
                        fill: #95a5a6;
                        transition: all 0.3s ease;
                    }
                }

                input {
                    flex: 1;
                    height: 1.1627rem;
                    padding: 0 0.2326rem;
                    border: none;
                    background: transparent;
                    font-size: 0.3488rem;
                    color: #2c3e50;

                    &::placeholder {
                        color: #bdc3c7;
                    }

                    &:disabled {
                        color: #95a5a6;
                        cursor: not-allowed;
                    }
                }

                .password-toggle {
                    padding: 0 0.2326rem;
                    cursor: pointer;

                    .van-icon {
                        font-size: 0.4651rem;
                        color: #95a5a6;
                        transition: all 0.3s ease;

                        &:hover {
                            color: #EA001B;
                        }
                    }
                }

                &.focused .input-icon .icon {
                    fill: #EA001B;
                }
            }

            .error-message {
                color: #dc3545;
                font-size: 0.3023rem;
                margin-top: 0.1162rem;
                display: flex;
                align-items: center;
                gap: 0.0930rem;

                &::before {
                    content: '⚠';
                    color: #dc3545;
                }
            }

            .success-message {
                color: #28a745;
                font-size: 0.3023rem;
                margin-top: 0.1162rem;
                display: flex;
                align-items: center;
                gap: 0.0930rem;

                &::before {
                    content: '✓';
                    color: #28a745;
                }
            }

            .password-strength {
                margin-top: 0.2326rem;
                display: flex;
                align-items: center;
                gap: 0.2326rem;

                .strength-label {
                    font-size: 0.3023rem;
                    color: #7f8c8d;
                }

                .strength-bars {
                    display: flex;
                    gap: 0.0697rem;

                    .strength-bar {
                        width: 0.4651rem;
                        height: 0.0930rem;
                        background: #e9ecef;
                        border-radius: 0.0465rem;
                        transition: all 0.3s ease;

                        &.active.weak {
                            background: #dc3545;
                        }

                        &.active.medium {
                            background: #ffc107;
                        }

                        &.active.strong {
                            background: #28a745;
                        }
                    }
                }

                .strength-text {
                    font-size: 0.3023rem;
                    font-weight: 600;

                    &.weak {
                        color: #dc3545;
                    }

                    &.medium {
                        color: #ffc107;
                    }

                    &.strong {
                        color: #28a745;
                    }
                }
            }
        }

        .submit-button {
            background: linear-gradient(135deg, #EA001B, #FF6B3D);
            color: #fff;
            border-radius: 0.1860rem;
            height: 1.1627rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.3721rem;
            font-weight: 600;
            margin: 0.4651rem 0;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(234, 0, 27, 0.3);

            &:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(234, 0, 27, 0.4);
            }

            &:active {
                transform: translateY(0);
            }

            &.disabled {
                background: #bdc3c7;
                box-shadow: none;
                cursor: not-allowed;

                &:hover {
                    transform: none;
                    box-shadow: none;
                }
            }

            &.loading {
                cursor: not-allowed;

                span {
                    margin-left: 0.2326rem;
                }
            }
        }

        .password-rules {
            background: #fff;
            border-radius: 0.1860rem;
            padding: 0.4651rem;
            margin-top: 0.2326rem;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

            .rules-title {
                font-size: 0.3488rem;
                font-weight: 600;
                color: #2c3e50;
                margin-bottom: 0.2326rem;
            }

            .rules-list {
                .rule-item {
                    display: flex;
                    align-items: center;
                    gap: 0.1860rem;
                    margin-bottom: 0.1162rem;
                    transition: all 0.3s ease;

                    &:last-child {
                        margin-bottom: 0;
                    }

                    .rule-icon {
                        width: 0.4651rem;
                        text-align: center;
                        font-size: 0.3256rem;
                        color: #bdc3c7;
                        transition: all 0.3s ease;
                    }

                    .rule-text {
                        font-size: 0.3256rem;
                        color: #7f8c8d;
                        transition: all 0.3s ease;
                    }

                    &.valid {
                        .rule-icon {
                            color: #28a745;
                        }

                        .rule-text {
                            color: #2c3e50;
                        }
                    }
                }
            }
        }
    }
}

/* 兼容原有样式类名 */
.container {
    &:extend(.reset-password-page);
}

.layout {
    background: #f5f5f5;
    min-height: calc(100vh - 1.4188rem);
    padding: 0.4651rem 0.2326rem;
}

.form {
    background: #fff;
    border-radius: 0.1860rem;
    padding: 0.4651rem;
    margin-bottom: 0.2326rem;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

    .clabel {
        font-size: 0.3721rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.2326rem;
    }

    .input {
        position: relative;
        display: flex;
        align-items: center;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 0.1860rem;
        transition: all 0.3s ease;

        input {
            flex: 1;
            height: 1.1627rem;
            padding: 0 0.2326rem;
            border: none;
            background: transparent;
            font-size: 0.3488rem;
            color: #2c3e50;

            &::placeholder {
                color: #bdc3c7;
            }
        }
    }
}

.ebtn {
    background: linear-gradient(135deg, #EA001B, #FF6B3D);
    color: #fff;
    border-radius: 0.1860rem;
    height: 1.1627rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.3721rem;
    font-weight: 600;
    margin: 0.4651rem 0;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(234, 0, 27, 0.3);

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(234, 0, 27, 0.4);
    }

    &:active {
        transform: translateY(0);
    }
}

/* 响应式适配 */
@media (max-width: 375px) {
    .reset-password-page {
        .password-form {
            padding: 0.2326rem 0.1162rem;

            .form-intro {
                padding: 0.3488rem;

                .intro-icon {
                    width: 1.1627rem;
                    height: 1.1627rem;
                    font-size: 0.9302rem;
                }
            }

            .form-group {
                padding: 0.3488rem;
            }
        }
    }
}
</style>
