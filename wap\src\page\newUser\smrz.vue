<template>
    <div class="auth-page">
        <div class="auth-page__header">
            <van-nav-bar title="实名认证" left-arrow @click-left="$router.go(-1)" fixed></van-nav-bar>
        </div>

        <div class="auth-page__content">
            <!-- 加载提示 -->
            <div class="loading-card" v-if="isLoading">
                <div class="loading-content">
                    <van-loading color="#EA001B" vertical>
                        <span class="loading-text">系统初始化中...</span>
                    </van-loading>
                </div>
            </div>

            <!-- 错误状态 -->
            <div v-else-if="loadError" class="error-container">
                <div class="error-content">
                    <div class="error-icon">⚠️</div>
                    <div class="error-message">{{ loadError }}</div>
                    <van-button type="primary" color="#EA001B" @click="retryLoadData" class="retry-button">
                        重新加载
                    </van-button>
                </div>
            </div>

            <!-- 主要内容：只有在成功初始化后才显示 -->
            <div v-else-if="isPageReady()">
                <!-- 认证状态卡片 -->
                <div class="status-card" v-if="userInfo.isActive !== undefined">
                    <div class="status-header">
                        <div class="status-icon-container">
                            <div class="status-icon">🛡️</div>
                        </div>
                        <div class="status-info">
                            <div class="status-title">实名认证状态</div>
                            <div class="status-tag" :class="getStatusClass(userInfo.isActive)">
                                {{ getStatusText(userInfo.isActive) }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 个人信息卡片 -->
                <div class="info-card">
                    <div class="card-title-simple">请完善您的个人信息</div>
                    <div class="card-content">
                        <div class="form-group">
                            <label class="form-label">姓名</label>
                            <div class="form-input">
                                <input v-if="showBtn" placeholder="请输入真实姓名" v-model="form.name" class="input-field" />
                                <input v-else placeholder="请输入真实姓名" v-model="form.name" readonly
                                    class="input-field input-field--readonly" />
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">身份证号</label>
                            <div class="form-input">
                                <input v-if="showBtn" placeholder="请输入18位身份证号码" v-model="form.idCard" class="input-field"
                                    maxlength="18" />
                                <input v-else placeholder="请输入18位身份证号码" v-model="form.idCard" readonly
                                    class="input-field input-field--readonly" />
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">地址</label>
                            <div class="form-input">
                                <input v-if="showBtn" placeholder="请输入详细地址" v-model="form.addr" class="input-field" />
                                <input v-else placeholder="请输入详细地址" v-model="form.addr" readonly
                                    class="input-field input-field--readonly" />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 身份证上传卡片 -->
                <div class="upload-card">
                    <div class="card-title-simple">请上传身份证的正反面</div>
                    <div class="card-content">
                        <!-- 身份证正面 -->
                        <div class="upload-section">
                            <div class="upload-info">
                                <div class="upload-title">身份证正面</div>
                                <div class="upload-desc">请确保证件信息清晰可见</div>
                            </div>
                            <div class="upload-area upload-area--front" @click="triggerFileInput('front')">
                                <input v-if="showBtn" ref="frontFileInput" type="file" accept="image/*"
                                    @change="handleFrontUpload" class="hidden-input" :disabled="!isPageReady()" />
                                <img v-if="form.img1key" :src="form.img1key" class="upload-image" alt="身份证正面" />
                                <div v-else class="upload-placeholder">
                                    <img src="~@/assets/newtemp/19.png" class="upload-icon" alt="上传图标" />
                                    <div class="upload-text" v-if="!imgStatus && isPageReady()">点击上传正面照片</div>
                                    <div class="upload-text" v-if="imgStatus">
                                        <div class="uploading-animation">
                                            <div class="spinner"></div>
                                            <span>上传中...</span>
                                        </div>
                                    </div>
                                    <div class="upload-text" v-if="!isPageReady()">初始化中...</div>
                                </div>
                            </div>
                        </div>

                        <!-- 身份证反面 -->
                        <div class="upload-section">
                            <div class="upload-info">
                                <div class="upload-title">身份证反面</div>
                                <div class="upload-desc">请确保有效期等信息清晰可见</div>
                            </div>
                            <div class="upload-area upload-area--back" @click="triggerFileInput('back')">
                                <input v-if="showBtn" ref="backFileInput" type="file" accept="image/*"
                                    @change="handleBackUpload" class="hidden-input" :disabled="!isPageReady()" />
                                <img v-if="form.img2key" :src="form.img2key" class="upload-image" alt="身份证反面" />
                                <div v-else class="upload-placeholder">
                                    <img src="~@/assets/newtemp/20.png" class="upload-icon" alt="上传图标" />
                                    <div class="upload-text" v-if="!imgStatus2 && isPageReady()">点击上传反面照片</div>
                                    <div class="upload-text" v-if="imgStatus2">
                                        <div class="uploading-animation">
                                            <div class="spinner"></div>
                                            <span>上传中...</span>
                                        </div>
                                    </div>
                                    <div class="upload-text" v-if="!isPageReady()">初始化中...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 提交按钮 -->
                <div class="submit-section" v-if="showBtn">
                    <button class="submit-button" @click="toSure">
                        <span class="submit-text">提交认证</span>
                        <span class="submit-icon">✓</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import APIUrl from "@/axios/api.url";
import * as api from "@/axios/api";
import { Toast } from "mint-ui";
import { isNull, idCardReg, isName } from "@/utils/utils";
import { compress } from "@/utils/imgupload";
import * as qiniu from "qiniu-js";
import heic2any from "heic2any";

export default {
    data() {
        return {
            imgUrl: "",
            imgUrl1: "",
            form: {
                phone: "",
                name: "",
                idCard: "",
                img1key: "",
                img2key: "",
                img3key: "",
                addr: "",
            },
            img1Key: "",
            img2Key: "",
            img3Key: "",
            showBtn: true,
            admin: APIUrl.baseURL,
            headers: {
                USERTOKEN: localStorage.getItem("USERTOKEN"),
            },
            imgStatus: false,
            imgStatus2: false,
            userInfo: {},
            messFlag: this.$store.state.userInfo.isActive == 3,
            qiniuDomain: "",
            isInitialized: false,
            initPromise: null,
            isLoading: true,
            loadError: null,
        };
    },
    async mounted() {
        this.admin = "https://" + window.location.hostname;
        console.log(this.admin);
        
        try {
            await this.initializePageData();
        } catch (error) {
            console.error('页面初始化失败:', error);
            this.loadError = '页面加载失败，请刷新重试';
        } finally {
            this.isLoading = false;
        }
    },
    methods: {
        // 初始化页面数据
        async initializePageData() {
            try {
                // 第一步：获取配置信息
                const configData = await this.getQiniuConfig();

                if (!configData || !configData.qiniuDomain) {
                    throw new Error('七牛云配置获取失败');
                }

                this.qiniuDomain = configData.qiniuDomain;
                this.isInitialized = true;
                console.log('七牛云域名获取成功:', this.qiniuDomain);

                // 第二步：获取用户信息（仅在配置获取成功后执行）
                await this.getUserInfo();

            } catch (error) {
                console.error('初始化页面数据失败:', error);
                throw error;
            }
        },

        // 获取七牛云配置
        async getQiniuConfig() {
            try {
                const response = await api.getInitConfig();

                if (!response || !response.data) {
                    throw new Error('配置接口返回数据异常');
                }

                const configData = response.data;

                // 验证必要的配置项
                if (!configData.qiniuDomain) {
                    throw new Error('七牛云域名配置缺失');
                }

                return configData;

            } catch (error) {
                console.error('获取七牛云配置失败:', error);
                Toast('获取配置信息失败，请稍后重试');
                throw error;
            }
        },

        // 保留原有的initializeConfig方法以兼容现有代码
        async initializeConfig() {
            this.isLoading = true;
            this.loadError = null;
            
            try {
                await this.initializePageData();
            } catch (error) {
                console.error('初始化配置失败:', error);
                this.loadError = '初始化失败，请刷新页面重试';
            } finally {
                this.isLoading = false;
            }
        },

        async ensureInitialized() {
            if (this.isInitialized && this.qiniuDomain) {
                return true;
            }

            if (this.initPromise) {
                try {
                    await this.initPromise;
                    return this.isInitialized && this.qiniuDomain;
                } catch (error) {
                    console.error('等待初始化失败:', error);
                    return false;
                }
            }

            await this.initializeConfig();
            return this.isInitialized && this.qiniuDomain;
        },

        async convertToJPEG(file) {
            if (file.type === "image/jpeg") {
                return file;
            }

            if (file.type === "image/heif" || file.type === "image/heic") {
                const jpegBlob = await heic2any({
                    blob: file,
                    toType: "image/jpeg",
                });
                return jpegBlob;
            }

            return new Promise((resolve, reject) => {
                const img = new Image();
                const url = URL.createObjectURL(file);

                img.onload = function () {
                    const canvas = document.createElement("canvas");
                    const ctx = canvas.getContext("2d");
                    canvas.width = img.width;
                    canvas.height = img.height;
                    ctx.drawImage(img, 0, 0);

                    canvas.toBlob(
                        (blob) => {
                            resolve(blob);
                            URL.revokeObjectURL(url);
                        },
                        "image/jpeg",
                        0.92
                    );
                };

                img.onerror = reject;
                img.src = url;
            });
        },

        async compressImageWithCanvas(file, maxWidth, maxHeight, quality) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                const url = URL.createObjectURL(file);

                img.onload = function () {
                    const canvas = document.createElement("canvas");
                    const ctx = canvas.getContext("2d");

                    let width = img.width;
                    let height = img.height;

                    if (width > height) {
                        if (width > maxWidth) {
                            height = Math.round((maxWidth / width) * height);
                            width = maxWidth;
                        }
                    } else {
                        if (height > maxHeight) {
                            width = Math.round((maxHeight / height) * width);
                            height = maxHeight;
                        }
                    }

                    canvas.width = width;
                    canvas.height = height;
                    ctx.drawImage(img, 0, 0, width, height);

                    canvas.toBlob(
                        (blob) => {
                            resolve(blob);
                            URL.revokeObjectURL(url);
                        },
                        "image/jpeg",
                        quality
                    );
                };

                img.onerror = reject;
                img.src = url;
            });
        },

        async upqiniu(file) {
            const initialized = await this.ensureInitialized();
            if (!initialized) {
                Toast("系统初始化中，请稍后重试");
                return;
            }

            const res = await api.getUploadToken();
            if (res.status === 0) {
                const uploadToken = res.data;

                if (!file) {
                    Toast("请选择文件");
                    return;
                }

                try {
                    const jpegFile = await this.convertToJPEG(file);

                    const compressedFile = await this.compressImageWithCanvas(
                        jpegFile,
                        1024,
                        1024,
                        0.7
                    );

                    const timestamp = Date.now();
                    const fileExtension = "jpg";
                    const fileName = `${timestamp}.${fileExtension}`;

                    const putExtra = {};
                    const config = {
                        region: qiniu.region.z2,
                    };

                    const observable = qiniu.upload(
                        compressedFile,
                        fileName,
                        uploadToken,
                        putExtra,
                        config
                    );
                    const domain = this.qiniuDomain;
                    return new Promise((resolve, reject) => {
                        observable.subscribe({
                            next(res) {
                                console.log(res.total);
                            },
                            error(err) {
                                console.error(err);
                                Toast("文件上传失败");
                                reject(err);
                            },
                            complete(res) {
                                const fileUrl = `${domain}/${res.key}`;
                                console.log(fileUrl);
                                resolve(fileUrl);
                            },
                        });
                    });
                } catch (error) {
                    Toast("图片压缩或转换失败");
                    console.error(error);
                }
            } else {
                Toast("获取上传Token失败");
            }
        },

        async setImg1key(file) {
            console.log(file);

            if (!this.isPageReady()) {
                Toast("系统初始化中，请稍等...");
                return;
            }

            this.imgStatus = true;
            try {
                const url = await this.upqiniu(file.file);
                if (url) {
                    this.form.img1key = url;
                }
            } catch (error) {
                console.error('上传失败:', error);
                Toast("上传失败，请重试");
            } finally {
                this.imgStatus = false;
            }
        },

        async setImg2key(file) {
            console.log(file);

            if (!this.isPageReady()) {
                Toast("系统初始化中，请稍等...");
                return;
            }

            this.imgStatus2 = true;
            try {
                const url = await this.upqiniu(file.file);
                if (url) {
                    this.form.img2key = url;
                }
            } catch (error) {
                console.error('上传失败:', error);
                Toast("上传失败，请重试");
            } finally {
                this.imgStatus2 = false;
            }
        },

        // 新增方法：触发文件选择
        triggerFileInput(type) {
            if (!this.isPageReady()) {
                Toast("系统初始化中，请稍等...");
                return;
            }

            if (type === 'front') {
                this.$refs.frontFileInput.click();
            } else if (type === 'back') {
                this.$refs.backFileInput.click();
            }
        },

        // 新增方法：处理正面上传
        async handleFrontUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            if (!this.isPageReady()) {
                Toast("系统初始化中，请稍等...");
                event.target.value = '';
                return;
            }

            if (!this.validateFile(file)) {
                event.target.value = '';
                return;
            }

            this.imgStatus = true;
            try {
                const url = await this.upqiniu(file);
                if (url) {
                    this.form.img1key = url;
                }
            } catch (error) {
                console.error('上传失败:', error);
                Toast("上传失败，请重试");
            } finally {
                this.imgStatus = false;
                event.target.value = '';
            }
        },

        // 新增方法：处理反面上传
        async handleBackUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            if (!this.isPageReady()) {
                Toast("系统初始化中，请稍等...");
                event.target.value = '';
                return;
            }

            if (!this.validateFile(file)) {
                event.target.value = '';
                return;
            }

            this.imgStatus2 = true;
            try {
                const url = await this.upqiniu(file);
                if (url) {
                    this.form.img2key = url;
                }
            } catch (error) {
                console.error('上传失败:', error);
                Toast("上传失败，请重试");
            } finally {
                this.imgStatus2 = false;
                event.target.value = '';
            }
        },

        // 新增方法：文件验证
        validateFile(file) {
            const isImage = file.type.startsWith('image/');
            if (!isImage) {
                Toast("只允许上传图片文件！");
                return false;
            }

            const isLt10M = file.size / 1024 / 1024 < 10;
            if (!isLt10M) {
                Toast("图片大小不能超过10MB！");
                return false;
            }

            return true;
        },

        handleAvatarSuccess(res, file) {
            this.imgStatus = false;

            if (res.data.url === "" || !res.data.url.startsWith("http")) {
                this.$message.error(
                    "上传失败请重新上传 返回url:" + res.data.url
                );
                return;
            }

            this.form.img1key = res.data.url;
        },
        beforeAvatarUpload(file) {
            this.imgStatus = true;
            const isJPGOrPNG =
                file.type === "image/jpeg" || file.type === "image/png";

            const isLt10M = file.size / 1024 / 1024 < 10;

            if (!isJPGOrPNG) {
                this.$message.error("只许上传 JPG 和 PNG 图片类型!");
                return false;
            }

            if (!isLt10M) {
                this.$message.error(this.$t("hj205"));
                return false;
            } else {
                this.form.img1key = URL.createObjectURL(file);
                compress(file, function (val) { });
            }
        },
        handleError(err, file, fileList) {
            this.imgStatus = false;

            this.$message.error(`文件上传失败: ${file.name}`);

            if (err && err.response) {
                console.log("响应状态:", err.response.status);
                console.log("响应数据:", err.response.data);
                this.$message.error(`上传失败内容: ${err.response.data}`);
            }
        },

        handleAvatarSuccess2(res, file) {
            this.imgStatus2 = false;
            if (res.data.url === "" || !res.data.url.startsWith("http")) {
                this.$message.error(
                    "上传失败请重新上传 返回url:" + res.data.url
                );
                return;
            }

            this.form.img2key = res.data.url;
        },
        beforeAvatarUpload2(file) {
            this.imgStatus2 = true;
            const isJPGOrPNG =
                file.type === "image/jpeg" || file.type === "image/png";

            if (!isJPGOrPNG) {
                this.$message.error("只许上传 JPG 和 PNG 图片类型!");
                return false;
            }

            const isLt10M = file.size / 1024 / 1024 < 10;
            if (!isLt10M) {
                this.$message.error(this.$t("hj205"));
                return false;
            } else {
                this.form.img2key = URL.createObjectURL(file);
                compress(file, function (val) { });
            }
        },
        handleError2() {
            this.imgStatus2 = false;
            this.$message.error(`文件上传失败: ${file.name}`);

            if (err && err.response) {
                console.log("响应状态:", err.response.status);
                console.log("响应数据:", err.response.data);
                this.$message.error(`上传失败内容: ${err.response.data}`);
            }
        },
        handleAvatarSuccess3(res, file) {
            this.form.img3key = res.data.url;
        },
        async getUserInfo() {
            let data = await api.getUserInfo();
            if (data.status === 0) {
                this.$store.commit("dialogVisible", false);
                this.$store.state.userInfo = data.data;
                this.userInfo = data.data;
                if (
                    this.$store.state.userInfo.isActive === 1 ||
                    this.$store.state.userInfo.isActive === 2
                ) {
                    this.form.idCard = this.$store.state.userInfo.idCard;
                    this.form.name = this.$store.state.userInfo.realName;
                    this.form.img1key = this.$store.state.userInfo.img1Key;
                    this.form.img2key = this.$store.state.userInfo.img2Key;
                    this.form.addr = this.$store.state.userInfo.addr;
                    this.showBtn = false;
                }
            } else {
                this.$router.push({ path: "/login" });
            }
        },
        beforeAvatarUpload3(file) { },
        handleFile: function (e) {
            let $target = e.target || e.srcElement;
            let file = $target.files[0];
            console.log(file, "file");
            let i = false;
            if (i) {
                Toast(this.$t("hj206"));
            } else {
                this.img1Key = file;
                var reader = new FileReader();
                reader.onload = (data) => {
                    let res = data.target || data.srcElement;
                    this.form.img1Key = res.result;
                };
                reader.readAsDataURL(file);
            }
        },
        toSure() {
            if (isNull(this.form.name) || !isName(this.form.name)) {
                Toast(this.$t("hj207"));
            } else if (
                isNull(this.form.idCard) ||
                !idCardReg(this.form.idCard)
            ) {
                Toast(this.$t("hj208"));
            } else if (isNull(this.form.img1key) || isNull(this.form.img2key)) {
                Toast(this.$t("hj209"));
            } else {
                this.toAuthentication();
            }
        },
        async toAuthentication() {
            let opts = {
                realName: this.form.name,
                idCard: this.form.idCard,
                img1key: this.form.img1key,
                img2key: this.form.img2key,
                img3key: this.form.img3key,
                addr: this.form.addr || '--',
            };
            let data = await api.userAuth(opts);
            if (data.status === 0) {
                Toast(this.$t("hj210"));
                this.goBack();
            } else {
                Toast(data.msg);
            }
        },
        goBack() {
            this.$router.back(-1);
        },
        getStatusText(status) {
            const statusMap = {
                0: "未实名认证",
                1: "实名认证审核中",
                2: "实名认证已通过",
                3: "实名认证未通过",
            };
            return statusMap[status] || "未知状态";
        },
        getStatusClass(status) {
            const classMap = {
                0: "status-pending",
                1: "status-processing",
                2: "status-success",
                3: "status-failed",
            };
            return classMap[status] || "status-pending";
        },
        isPageReady() {
            return this.isInitialized && this.qiniuDomain && !this.isLoading && !this.loadError;
        },
        retryLoadData() {
            this.initializeConfig();
            if (this.isInitialized) {
                Toast('重新加载成功');
            }
        },
    },
};
</script>

<style lang="less" scoped>
.auth-page {
    background: #F6F6F6;
    min-height: 100vh;
    font-size: 0.3256rem;

    &__header {
        width: 100%;
        height: 1.07rem;
    }

    &__content {
        padding: 0.2326rem;
        margin-top: 0.2326rem;
    }
}

// 加载卡片
.loading-card {
    background: #fff;
    border-radius: 0.1860rem;
    margin-bottom: 0.2326rem;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

    .loading-content {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 1.1628rem 0;

        .loading-text {
            margin-top: 0.4651rem;
            color: #EA001B;
            font-size: 0.3488rem;
            font-weight: 500;
        }
    }
}

// 状态卡片
.status-card {
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 0.3256rem;
    margin-bottom: 0.3721rem;
    box-shadow: 0 0.0930rem 0.4651rem rgba(0, 0, 0, 0.08);
    border-left: 0.0930rem solid #EA001B;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
        box-shadow: 0 0.1860rem 0.9302rem rgba(234, 0, 27, 0.15);
        transform: translateY(-0.0465rem);
    }

    .status-header {
        display: flex;
        align-items: center;
        padding: 0.6047rem 0.5581rem;
        gap: 0.4651rem;

        .status-icon-container {
            position: relative;
            width: 1.1628rem;
            height: 1.1628rem;
            background: linear-gradient(145deg, #EA001B, #ff4757);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 0.1395rem 0.4651rem rgba(234, 0, 27, 0.3);

            &::before {
                content: '';
                position: absolute;
                inset: -0.0465rem;
                background: linear-gradient(145deg, #EA001B, #ff4757);
                border-radius: 50%;
                z-index: -1;
                opacity: 0.3;
                animation: pulse 2s ease-in-out infinite;
            }

            .status-icon {
                font-size: 0.6047rem;
                filter: brightness(1.2) drop-shadow(0 0.0465rem 0.1395rem rgba(0, 0, 0, 0.2));
            }
        }

        .status-info {
            flex: 1;

            .status-title {
                font-size: 0.4186rem;
                font-weight: 700;
                color: #2c3e50;
                margin-bottom: 0.2326rem;
                letter-spacing: 0.0233rem;
            }

            .status-tag {
                display: inline-block;
                padding: 0.1860rem 0.3721rem;
                border-radius: 0.4651rem;
                font-size: 0.3023rem;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.0233rem;
                transition: all 0.3s ease;

                &.status-pending {
                    background: linear-gradient(145deg, #f8f9fa, #e9ecef);
                    color: #6c757d;
                    border: 1px solid #dee2e6;
                }

                &.status-processing {
                    background: linear-gradient(145deg, #e3f2fd, #bbdefb);
                    color: #1976d2;
                    border: 1px solid #90caf9;
                }

                &.status-success {
                    background: linear-gradient(145deg, #e8f5e8, #c8e6c9);
                    color: #388e3c;
                    border: 1px solid #a5d6a7;
                }

                &.status-failed {
                    background: linear-gradient(145deg, #ffebee, #ffcdd2);
                    color: #d32f2f;
                    border: 1px solid #ef9a9a;
                }
            }
        }
    }
}

// 信息卡片和上传卡片
.info-card,
.upload-card {
    background: #ffffff;
    border-radius: 0.3256rem;
    margin-bottom: 0.3721rem;
    box-shadow: 0 0.0930rem 0.4651rem rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
        box-shadow: 0 0.1860rem 0.9302rem rgba(0, 0, 0, 0.12);
        transform: translateY(-0.0465rem);
    }
}

// 简洁的卡片标题
.card-title-simple {
    background: #EA001B;
    color: #ffffff;
    font-size: 0.3721rem;
    font-weight: 600;
    padding: 0.4186rem 0.6047rem;
    border-radius: 0.3256rem 0.3256rem 0 0;
    letter-spacing: 0.0233rem;
}

// 卡片内容
.card-content {
    padding: 0.6977rem 0.5581rem;
    background: #ffffff;
}

// 表单组
.form-group {
    margin-bottom: 0.6047rem;

    &:last-child {
        margin-bottom: 0;
    }

    .form-label {
        display: block;
        font-size: 0.3721rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.3023rem;
        position: relative;
        padding-left: 0.4651rem;

        &:before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 0.0930rem;
            height: 0.4186rem;
            background: linear-gradient(145deg, #EA001B, #ff4757);
            border-radius: 0.0465rem;
            box-shadow: 0 0.0465rem 0.1395rem rgba(234, 0, 27, 0.3);
        }
    }

    .form-input {
        position: relative;

        .input-field {
            width: 100%;
            height: 1.2558rem;
            padding: 0 0.4651rem;
            border: 2px solid #e9ecef;
            border-radius: 0.2791rem;
            font-size: 0.3721rem;
            background: #ffffff;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-sizing: border-box;

            &:focus {
                outline: none;
                border-color: #EA001B;
                box-shadow: 0 0 0 0.1395rem rgba(234, 0, 27, 0.15);
                background: linear-gradient(145deg, #ffffff 0%, #fef8f8 100%);
                transform: translateY(-0.0233rem);
            }

            &::placeholder {
                color: #adb5bd;
                font-size: 0.3488rem;
            }

            &--readonly {
                background: linear-gradient(145deg, #f8f9fa 0%, #e9ecef 100%);
                border-color: #dee2e6;
                color: #6c757d;
                cursor: not-allowed;

                &:focus {
                    border-color: #dee2e6;
                    box-shadow: none;
                    background: linear-gradient(145deg, #f8f9fa 0%, #e9ecef 100%);
                    transform: none;
                }
            }
        }
    }
}

// 上传区域重新设计
.upload-section {
    display: flex;
    align-items: center;
    gap: 0.6047rem;
    padding: 0.5581rem;
    background: linear-gradient(145deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 0.2791rem;
    margin-bottom: 0.5116rem;
    border: 1px solid #e9ecef;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:last-child {
        margin-bottom: 0;
    }

    &:hover {
        background: linear-gradient(145deg, #ffffff 0%, #f1f3f4 100%);
        border-color: #d1d5db;
        transform: translateX(0.1163rem);
        box-shadow: 0 0.1395rem 0.4651rem rgba(0, 0, 0, 0.08);
    }

    .upload-info {
        flex: 1;

        .upload-title {
            font-size: 0.3721rem;
            font-weight: 600;
            color: #2c3e50;
            margin: 0 0 0.1860rem 0;
            display: flex;
            align-items: center;
            gap: 0.1860rem;

            &:before {
                content: '📋';
                font-size: 0.3256rem;
            }
        }

        .upload-desc {
            font-size: 0.3023rem;
            color: #6c757d;
            margin: 0;
            line-height: 1.4;
        }
    }

    .upload-area {
        width: 4.4186rem;
        height: 2.9767rem;
        border-radius: 0.2791rem;
        overflow: hidden;
        position: relative;
        background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
        border: 2px dashed #d1d5db;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;

        &--front {
            background-image: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1)), url(~@/assets/images/qiquan26/sfz_zheng.png);
            background-size: cover;
            background-position: center;
        }

        &--back {
            background-image: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1)), url(~@/assets/images/qiquan26/sfz_fan.png);
            background-size: cover;
            background-position: center;
        }

        &:hover {
            border-color: #adb5bd;
            background-blend-mode: multiply;
            background-color: rgba(108, 117, 125, 0.05);
            transform: scale(1.02);
            box-shadow: 0 0.2326rem 0.9302rem rgba(0, 0, 0, 0.1);
        }

        .upload-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 0.2326rem;
            box-shadow: 0 0.1860rem 0.5581rem rgba(0, 0, 0, 0.15);
        }

        .upload-placeholder {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(4px);
            color: #6c757d;
            transition: all 0.3s ease;

            .upload-icon {
                width: 1.3953rem;
                height: 1.3953rem;
                margin-bottom: 0.3721rem;
                opacity: 0.8;
                filter: drop-shadow(0 0.0930rem 0.2791rem rgba(108, 117, 125, 0.3));
                object-fit: contain;
                transition: all 0.3s ease;

                &:hover {
                    transform: scale(1.1);
                    opacity: 1;
                }
            }

            .upload-text {
                font-size: 0.3256rem;
                text-align: center;
                font-weight: 500;
                line-height: 1.3;
            }

            .uploading-animation {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 0.2791rem;

                .spinner {
                    width: 0.6977rem;
                    height: 0.6977rem;
                    border: 2px solid #f3f3f3;
                    border-top: 2px solid #EA001B;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                }

                span {
                    font-size: 0.3023rem;
                    color: #EA001B;
                    font-weight: 600;
                }
            }
        }

        .hidden-input {
            display: none;
        }
    }
}

// 动画效果
@keyframes pulse {
    0%,
    100% {
        opacity: 0.3;
        transform: scale(1);
    }

    50% {
        opacity: 0.6;
        transform: scale(1.05);
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

// 错误状态样式
.error-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 60vh;
    background: #F6F6F6;
    padding: 0.4651rem;

    .error-content {
        text-align: center;
        background: #fff;
        border-radius: 0.1860rem;
        padding: 0.6977rem 0.4651rem;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        max-width: 6.9767rem;

        .error-icon {
            font-size: 1.1628rem;
            margin-bottom: 0.4651rem;
        }

        .error-message {
            color: #e74c3c;
            font-size: 0.3721rem;
            margin-bottom: 0.6977rem;
            line-height: 1.5;
            font-weight: 500;
        }

        .retry-button {
            border-radius: 0.4651rem;
            height: 0.9302rem;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(234, 0, 27, 0.3);
            transition: all 0.3s ease;

            &:hover {
                transform: translateY(-0.0465rem);
                box-shadow: 0 4px 12px rgba(234, 0, 27, 0.4);
            }

            &:active {
                transform: translateY(0);
            }
        }
    }
}

// 提交区域
.submit-section {
    margin-top: 0.4651rem;
    padding: 0.2326rem;

    .submit-button {
        width: 100%;
        height: 1.3953rem;
        background: linear-gradient(135deg, #EA001B 0%, #FF6B3D 100%);
        border: none;
        border-radius: 0.6977rem;
        color: #fff;
        font-size: 0.4186rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.2326rem;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 16px rgba(234, 0, 27, 0.3);
        position: relative;
        overflow: hidden;

        &:before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transform: rotate(45deg);
            transition: all 0.6s ease;
            opacity: 0;
        }

        &:hover {
            transform: translateY(-0.0930rem);
            box-shadow: 0 8px 24px rgba(234, 0, 27, 0.4);
            background: linear-gradient(135deg, #d00019 0%, #e55a37 100%);

            &:before {
                opacity: 1;
                animation: shimmer 1.5s ease-in-out infinite;
            }
        }

        &:active {
            transform: translateY(-0.0465rem);
            box-shadow: 0 6px 20px rgba(234, 0, 27, 0.35);
        }

        .submit-text {
            font-size: 0.4186rem;
            letter-spacing: 0.0465rem;
        }

        .submit-icon {
            font-size: 0.4651rem;
            font-weight: bold;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 0.6977rem;
            height: 0.6977rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
}

// 元素隐藏
.avatar-uploader {
    overflow: hidden;

    :deep(.el-upload__input) {
        opacity: 0 !important;
    }

    &.upload-empty {
        opacity: 0;
    }

    &.uploaded {
        opacity: 1;
    }
}

// 动画效果
@keyframes shimmer {
    0% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
    }

    50% {
        transform: translateX(50%) translateY(50%) rotate(45deg);
    }

    100% {
        transform: translateX(200%) translateY(200%) rotate(45deg);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(0.4651rem);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

// 卡片入场动画
.status-card,
.info-card,
.upload-card {
    animation: slideInUp 0.6s ease-out;
    animation-fill-mode: both;
}

.info-card {
    animation-delay: 0.1s;
}

.upload-card {
    animation-delay: 0.2s;
}

.submit-section {
    animation: slideInUp 0.6s ease-out 0.3s both;
}

// 响应式设计
@media (max-width: 768px) {
    .auth-page {
        &__content {
            padding: 0.1163rem;
        }
    }

    .upload-section {
        flex-direction: column;
        text-align: center;
        gap: 0.2326rem;

        .upload-info {
            .upload-title {
                font-size: 0.3488rem;
            }

            .upload-desc {
                font-size: 0.2791rem;
            }
        }

        .upload-area {
            width: 100%;
            max-width: 4.6512rem;
            height: 2.7907rem;
        }
    }

    .submit-button {
        height: 1.1628rem;
        font-size: 0.3721rem;

        .submit-text {
            font-size: 0.3721rem;
        }

        .submit-icon {
            font-size: 0.4186rem;
            width: 0.5814rem;
            height: 0.5814rem;
        }
    }
}

// 滚动条美化
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #F6F6F6;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #EA001B, #FF6B3D);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #d00019, #e55a37);
}
</style>