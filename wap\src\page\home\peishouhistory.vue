<template>
    <div class="subscription-history-page">
        <div class="header">
            <van-nav-bar v-if="type == 1" title="打新记录" left-arrow @click-left="$router.go(-1)" fixed></van-nav-bar>
            <van-nav-bar v-else title="配售记录" left-arrow @click-left="$router.go(-1)" fixed></van-nav-bar>
        </div>

        <div class="tab-menu">
            <div :class="`tab-item ${itemIndex == 0 ? 'active' : ''}`" @click="changeItemIndex(0)">
                <div class="tab-content">
                    <span>申购中</span>
                    <span class="tab-indicator"></span>
                </div>
            </div>
            <div :class="`tab-item ${itemIndex == 1 ? 'active' : ''}`" @click="changeItemIndex(1)">
                <div class="tab-content">
                    <span>中签</span>
                    <span class="tab-indicator"></span>
                </div>
            </div>
            <div :class="`tab-item ${itemIndex == 2 ? 'active' : ''}`" @click="changeItemIndex(2)">
                <div class="tab-content">
                    <span>未中签</span>
                    <span class="tab-indicator"></span>
                </div>
            </div>
        </div>

        <div class="record-list">
            <div v-if="loading" class="loading-state">
                <van-loading size="24px" vertical>加载中...</van-loading>
            </div>

            <div v-else-if="!hasData" class="empty-state">
                <div class="empty-icon">📋</div>
                <div class="empty-text">暂无{{ currentTabName }}记录</div>
            </div>

            <div v-else v-for="item in list" class="record-item" :key="item.id">
                <!-- 股票信息头部 -->
                <div class="stock-header">
                    <div class="stock-info">
                        <stock-tag-info :stock-code="getStockGid(item)" />
                        <span class="stock-name">{{ item.newName }}</span>
                    </div>
                    <div class="status-badge">
                        <span v-if="itemIndex == 0" class="status-applying">申购中</span>
                        <span v-if="itemIndex == 1 && item.status == 4" class="status-paid">已认缴</span>
                        <span v-if="itemIndex == 1 && item.status == 3" class="status-pay-btn"
                            @click="confirmPayment(item.id)">
                            认缴
                        </span>
                        <span v-if="itemIndex == 2" class="status-failed">未中签</span>
                    </div>
                </div>

                <!-- 详细信息 -->
                <div class="detail-grid">
                    <div class="detail-item">
                        <div class="detail-label">发行价格</div>
                        <div class="detail-value">¥{{ formatPrice(item.buyPrice) }}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">申购代码</div>
                        <div class="detail-value">{{ item.newCode }}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">数量</div>
                        <div class="detail-value">{{ type == 2 ? item.applyNums : item.applyNumber || 0 }}</div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">{{ item.type == 1 ? '缴纳资金' : '占用资金' }}</div>
                        <div class="detail-value">{{ item.bond }} </div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">申购时间</div>
                        <div class="detail-value">{{ item.addTime }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import * as api from "@/axios/api";
import { MessageBox, Toast } from "mint-ui";
import StockTagInfo from "@/components/stock-tag-info.vue";
import dayjs from "dayjs";

export default {
    name: "SubscriptionHistoryPage",
    components: {
        StockTagInfo
    },
    data() {
        return {
            type: 2,
            itemIndex: 0,
            list: [],
            pageNum: 1,
            finished: false,
            loading: false,
            currentRequestId: 0, // 添加请求ID来追踪最新请求
        };
    },
    computed: {
        // 当前标签页名称
        currentTabName() {
            const tabNames = ['申购中', '中签', '未中签'];
            return tabNames[this.itemIndex] || '未知';
        },

        // 是否有数据
        hasData() {
            return this.list && this.list.length > 0;
        }
    },
    mounted() {
        this.initializePage();
    },
    beforeDestroy() {
        // 组件销毁时重置请求ID，确保不处理过时响应
        this.currentRequestId = 0;
    },
    methods: {
        // 初始化页面
        async initializePage() {
            try {
                this.type = parseInt(this.$route.query.type) || 2;
                // 如果URL中有tab参数，则跳转到对应的标签页，否则默认为0
                const tabIndex = parseInt(this.$route.query.tab) || 0;
                await this.changeItemIndex(tabIndex);
            } catch (error) {
                console.error('页面初始化失败:', error);
                this.$toast('页面加载失败，请重试');
            }
        },

        // 切换标签页
        async changeItemIndex(index) {
            try {
                this.resetPageData();
                this.itemIndex = index;
                
                // 生成新的请求ID
                this.currentRequestId = Date.now();

                if (!this.type) {
                    console.warn('类型参数缺失');
                    return;
                }

                const statusType = this.getStatusType(index);
                await this.loadSubscriptionRecords(statusType);

            } catch (error) {
                console.error(`切换到${this.currentTabName}标签页失败:`, error);
                this.$toast('切换标签页失败');
            }
        },

        // 重置页面数据
        resetPageData() {
            this.list = [];
            this.pageNum = 1;
            this.finished = false;
        },

        // 获取状态类型
        getStatusType(index) {
            const typeMap = {
                0: 1, // 申购中
                1: 3, // 中签
                2: 2  // 未中签
            };
            return typeMap[index] || 1;
        },

        // 获取股票GID（用于标签组件）
        getStockGid(item) {
            if (!item.newType) return '';

            const typeMap = {
                '深': 'sz',
                '创': 'sz',
                '沪': 'sh',
                '科': 'sh',
                '北': 'bj'
            };

            const prefix = typeMap[item.newType] || 'sz';
            return `${prefix}${item.newCode}`;
        },

        // 加载配售记录
        async loadSubscriptionRecords(statusType) {
            this.loading = true;
            
            // 记录当前请求ID
            const requestId = this.currentRequestId;

            try {
                const params = {
                    status: statusType,
                    type: this.type
                };

                const response = await api.getzqjl(params);
                
                // 检查是否为最新请求
                if (requestId !== this.currentRequestId) {
                    return; // 忽略过时的响应
                }

                this.handleRecordsResponse(response);

            } catch (error) {
                // 检查是否为最新请求
                if (requestId !== this.currentRequestId) {
                    return; // 忽略过时的响应
                }
                this.handleRecordsError(error);
            } finally {
                this.loading = false;
            }
        },

        // 处理记录响应
        handleRecordsResponse(response) {
            if (!response || !Array.isArray(response.data)) {
                throw new Error('数据格式异常');
            }

            this.list = response.data;
            console.log(`加载${this.currentTabName}记录成功，共${this.list.length}条`);
        },

        // 处理记录错误
        handleRecordsError(error) {
            console.error('获取配售记录失败:', error);

            if (error.response) {
                switch (error.response.status) {
                    case 401:
                        this.$toast('登录已过期，请重新登录');
                        break;
                    case 403:
                        this.$toast('没有访问权限');
                        break;
                    case 500:
                        this.$toast('服务器异常，请稍后重试');
                        break;
                    default:
                        this.$toast('网络请求失败，请检查网络连接');
                }
            } else {
                this.$toast('数据加载失败，请重试');
            }
        },

        // 确认认缴
        confirmPayment(id) {
            if (!id) {
                this.$toast('参数错误');
                return;
            }

            MessageBox.confirm(
                this.$t("hj251") + "?",
                this.$t("hj165"),
                {
                    confirmButtonText: this.$t("hj161"),
                    cancelButtonText: this.$t("hj106"),
                }
            ).then(() => {
                this.executePayment(id);
            }).catch(() => {
                console.log('用户取消认缴');
            });
        },

        // 执行认缴
        async executePayment(id) {
            try {
                const params = { id };
                const response = await api.submitSubscribe(params);

                Toast(response.msg || '认缴成功');

                // 刷新中签列表
                await this.changeItemIndex(1);

            } catch (error) {
                console.error('认缴失败:', error);
                const errorMsg = error.response && error.response.data && error.response.data.msg
                    ? error.response.data.msg
                    : '认缴失败，请重试';
                Toast(errorMsg);
            }
        },

        // 格式化价格
        formatPrice(price) {
            if (!price && price !== 0) return '0.00';
            return parseFloat(price).toFixed(2);
        },

        // 格式化数字
        formatNumber(number) {
            if (!number && number !== 0) return '0';
            return parseInt(number).toLocaleString();
        },

        // 格式化时间
        formatTime(time) {
            if (!time) return '--';
            return dayjs(time).format('YYYY-MM-DD');
        }
    },
};
</script>

<style lang="less" scoped>
.subscription-history-page {
    background: #f5f5f5;
    min-height: 100vh;
    font-size: 0.3256rem;

    .header {
        width: 100%;
        height: 1.07rem;
    }

    .tab-menu {
        display: flex;
        background: #fff;
        margin: 0.2326rem;
        border-radius: 0.1860rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

        .tab-item {
            flex: 1;
            display: flex;
            justify-content: center;

            .tab-content {
                position: relative;
                padding: 0.3488rem 0.6977rem;
                color: #7f8c8d;
                font-weight: 500;
                font-size: 0.3488rem;
                cursor: pointer;
                text-align: center;
                transition: all 0.3s ease;

                .tab-indicator {
                    position: absolute;
                    left: 50%;
                    bottom: 0.1162rem;
                    transform: translateX(-50%);
                    width: 0.4651rem;
                    height: 0.0697rem;
                    background: #EA001B;
                    border-radius: 0.0349rem;
                    display: none;
                }

                &:hover {
                    color: #EA001B;
                    // background: rgba(234, 0, 27, 0.05);
                    border-radius: 0.1860rem;
                }
            }

            &.active {
                .tab-content {
                    color: #EA001B;
                    font-weight: 600;

                    .tab-indicator {
                        display: block;
                    }
                }
            }
        }
    }

    .record-list {
        margin: 0.2326rem;

        .loading-state,
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 1.1627rem 0;
            background: #fff;
            border-radius: 0.1860rem;
        }

        .empty-state {
            .empty-icon {
                font-size: 1.1627rem;
                margin-bottom: 0.2326rem;
                opacity: 0.5;
            }

            .empty-text {
                color: #95a5a6;
                font-size: 0.3488rem;
            }
        }

        .record-item {
            background: #fff;
            border-radius: 0.1860rem;
            margin-bottom: 0.2326rem;
            padding: 0.4651rem;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;

            &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }

            .stock-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 0.4651rem;
                padding-bottom: 0.3488rem;
                border-bottom: 1px solid #f0f0f0;

                .stock-info {
                    display: flex;
                    align-items: center;
                    gap: 0.2326rem;

                    .stock-name {
                        font-size: 0.3721rem;
                        font-weight: 600;
                        color: #2c3e50;
                    }
                }

                .status-badge {
                    .status-applying {
                        color: #3498db;
                        font-size: 0.3256rem;
                        font-weight: 500;
                    }

                    .status-paid {
                        color: #f39c12;
                        font-size: 0.3256rem;
                        font-weight: 500;
                    }

                    .status-pay-btn {
                        background: #EA001B;
                        color: #fff;
                        padding: 0.1860rem 0.3488rem;
                        border-radius: 0.1395rem;
                        font-size: 0.3256rem;
                        font-weight: 500;
                        cursor: pointer;
                        transition: all 0.3s ease;

                        &:hover {
                            background: #C8001A;
                            transform: translateY(-1px);
                            box-shadow: 0 2px 6px rgba(234, 0, 27, 0.3);
                        }

                        &:active {
                            transform: translateY(0);
                        }
                    }

                    .status-failed {
                        color: #95a5a6;
                        font-size: 0.3256rem;
                        font-weight: 500;
                    }
                }
            }

            .detail-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 0.3488rem;

                .detail-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .detail-label {
                        color: #7f8c8d;
                        font-size: 0.3256rem;
                        font-weight: 500;
                        white-space: nowrap;
                        margin-right: 0.1860rem;
                    }

                    .detail-value {
                        color: #2c3e50;
                        font-size: 0.3256rem;
                        font-weight: 600;
                        font-family: 'Arial', 'Helvetica', monospace;
                        white-space: nowrap;
                    }
                }
            }
        }
    }
}

/* 加载状态样式 */
:deep(.van-loading) {
    .van-loading__text {
        color: #95a5a6;
        font-size: 0.3256rem;
    }
}

/* 动画效果 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(0.4651rem);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.record-item {
    animation: slideInUp 0.3s ease-out;
}

.record-item:nth-child(odd) {
    animation-delay: 0.05s;
}

.record-item:nth-child(even) {
    animation-delay: 0.1s;
}
</style>