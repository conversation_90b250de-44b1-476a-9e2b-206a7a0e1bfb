<template>
    <div class="trading-page">
        <div class="trading-page__header">
            <van-nav-bar title="我的交易" left-arrow @click-left="$router.go(-1)" fixed></van-nav-bar>
        </div>

        <div class="trading-page__tabs">
            <div :class="['tab-item', { 'tab-item--active': itemIndex === 0 }]" @click="changeItemIndex(0)">
                <div class="tab-item__content">
                    <span class="tab-item__text">我的持仓</span>
                    <span class="tab-item__indicator"></span>
                </div>
            </div>
            <div :class="['tab-item', { 'tab-item--active': itemIndex === 1 }]" @click="changeItemIndex(1)">
                <div class="tab-item__content">
                    <span class="tab-item__text">交易记录</span>
                    <span class="tab-item__indicator"></span>
                </div>
            </div>
            <div :class="['tab-item', { 'tab-item--active': itemIndex === 2 }]" @click="changeItemIndex(2)">
                <div class="tab-item__content">
                    <span class="tab-item__text">我的委托</span>
                    <span class="tab-item__indicator"></span>
                </div>
            </div>
        </div>

        <!-- 我的持仓 -->
        <div class="trading-list" v-if="itemIndex === 0">
            <div class="trading-list__header">
                <div class="header-item">名称</div>
                <div class="header-item">持仓 | 市值</div>
                <div class="header-item">现价 | 成本</div>
                <div class="header-item">盈亏 | 涨幅</div>
            </div>
            <div class="trading-list__container">
                <van-list v-model="loading" :finished="finished" :immediate-check="false" :finished-text="$t('hj43')"
                    @load="getOrderList(0)">
                    <div class="trading-item" v-for="value in list" :key="value.id" @click="chicangDetail(value)">
                        <div class="trading-item__stock">
                            <div class="stock-info">
                                <div class="stock-info__name">{{ value.stockName }}</div>
                                <div class="stock-info__code">
                                    <stock-tag-info :stockCode="value.stockGid" />
                                </div>
                            </div>
                        </div>
                        <div class="trading-item__data">
                            <span class="data-primary">{{ parseNumber(value.buyNum) }}</span>
                            <span class="data-secondary">{{ parseNumber(value.buyPrice) }}</span>
                        </div>
                        <div class="trading-item__data">
                            <span class="data-primary">{{ parseNumber(value.now_price) }}</span>
                            <span class="data-secondary">{{ parseNumber(value.buyOrderPrice) }}</span>
                        </div>
                        <div :class="[
                            'trading-item__data',
                            'trading-item__data--profit',
                            { 'trading-item__data--loss': value.profitAndLossRatio <= 0 }
                        ]">
                            <span class="data-primary">{{ parseNumber(value.profitAndLose) }}</span>
                            <span class="data-secondary">{{ parseNumber(value.profitAndLossRatio) }}%</span>
                        </div>
                    </div>
                </van-list>
            </div>
        </div>

        <!-- 交易记录 -->
        <div class="trading-list" v-if="itemIndex === 1">
            <div class="trading-list__header">
                <div class="header-item">股票 | 代码</div>
                <div class="header-item">本金 | 数量</div>
                <div class="header-item">买入 | 卖出价</div>
                <div class="header-item">收益 | 涨幅</div>
            </div>
            <div class="trading-list__container">
                <van-list v-model="loading" :finished="finished" :immediate-check="false" :finished-text="$t('hj43')"
                    @load="getOrderList(1)">
                    <div v-for="value in list" :key="value.id" class="trading-record" @click="chicangDetail(value)">
                        <div class="trading-item">
                            <div class="trading-item__stock">
                                <div class="stock-info">
                                    <div class="stock-info__name">{{ value.stockName }}</div>
                                    <div class="stock-info__code">
                                        <stock-tag-info :stockCode="value.stockGid" />
                                    </div>
                                </div>
                            </div>
                            <div class="trading-item__data">
                                <span class="data-primary">{{ parseNumber(value.buyPrice) }}</span>
                                <span class="data-secondary">{{ parseNumber(value.buyNum) }}</span>
                            </div>
                            <div class="trading-item__data">
                                <span class="data-primary">{{ parseNumber(value.buyOrderPrice) }}</span>
                                <span class="data-secondary">{{ parseNumber(value.sellOrderPrice) }}</span>
                            </div>
                            <div :class="[
                                'trading-item__data',
                                'trading-item__data--profit',
                                { 'trading-item__data--loss': value.profitAndLossRatio <= 0 }
                            ]">
                                <span class="data-primary">{{ parseNumber(value.profitAndLose) }}</span>
                                <span class="data-secondary">{{ parseNumber(value.profitAndLossRatio) }}%</span>
                            </div>
                        </div>
                        <div class="trading-record__time">
                            <div class="time-item">{{ formatTime(value.buyOrderTime) }}</div>
                            <div class="time-item">{{ formatTime(value.sellOrderTime) }}</div>
                        </div>
                        <div class="trading-record__action">
                            <button class="action-button">查看详情</button>
                        </div>
                    </div>
                </van-list>
            </div>
        </div>

        <!-- 我的委托 -->
        <div class="delegation-list" v-if="itemIndex === 2">
            <van-list v-model="loading" :finished="finished" :immediate-check="false" :finished-text="$t('hj43')"
                @load="getOrderList(2)">
                <div v-for="value in list" :key="value.id" class="delegation-item">
                    <div class="delegation-item__header">
                        <div class="stock-info">
                            <div class="stock-info__name">{{ value.stockName }}</div>
                            <div class="stock-info__code">
                                <stock-tag-info :stockCode="value.stockGid" />
                            </div>
                        </div>
                        <div class="delegation-item__action">
                            <button class="withdrawal-button" @click="withdrawOrder(value)"
                                :disabled="value.backStatus !== 0">
                                撤单
                            </button>
                        </div>
                    </div>
                    <div class="delegation-item__info">
                        <div class="info-row">
                            <div class="info-item">
                                <div class="info-item__label">买卖类别</div>
                                <div class="info-item__value">证券买入</div>
                            </div>
                            <div class="info-item">
                                <div class="info-item__label">当前状态</div>
                                <div class="info-item__value">挂单</div>
                            </div>
                        </div>
                        <div class="info-row">
                            <div class="info-item">
                                <div class="info-item__label">委托手数</div>
                                <div class="info-item__value">{{ value.orderNum / 100 }}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-item__label">委托价格</div>
                                <div class="info-item__value">{{ value.buyOrderPrice }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </van-list>
        </div>
    </div>
</template>

<script>
import * as api from "@/axios/api";
import { MessageBox, Toast } from "mint-ui";
import dayjs from "dayjs";
import StockTagInfo from "@/components/stock-tag-info.vue";

export default {
    name: "TradingPage",
    components: {
        StockTagInfo
    },
    data() {
        return {
            type: 1,
            itemIndex: 1,
            list: [],
            pageNum: 1,
            finished: false,
            loading: false,
            currentRequestId: 0, // 添加请求ID来追踪最新请求
        };
    },
    computed: {
        // 当前标签页标题
        currentTabTitle() {
            const tabs = ['我的持仓', '交易记录', '我的委托'];
            return tabs[this.itemIndex] || '';
        },

        // 是否有数据
        hasData() {
            return this.list && this.list.length > 0;
        },

        // 列表标题配置
        listTitleConfig() {
            const configs = {
                0: ['名称', '持仓 | 市值', '现价 | 成本', '盈亏 | 涨幅'],
                1: ['股票 | 代码', '本金 | 数量', '买入 | 卖出价', '收益 | 涨幅'],
                2: [] // 委托页面没有标题
            };
            return configs[this.itemIndex] || [];
        }
    },
    mounted() {
        this.initializePage();
    },
    beforeDestroy() {
        // 组件销毁时重置请求ID，确保不处理过时响应
        this.currentRequestId = 0;
    },
    methods: {
        // 初始化页面
        initializePage() {
            try {
                if (this.$route.query.type) {
                    this.type = parseInt(this.$route.query.type) || 1;
                }
                this.changeItemIndex(this.type - 1);
            } catch (error) {
                console.error('页面初始化失败:', error);
                this.changeItemIndex(0); // 默认显示第一个标签页
            }
        },

        // 已移除 CancelToken 相关方法，使用 currentRequestId 处理竞态条件

        // 数字格式化
        parseNumber(num) {
            if (num === null || num === undefined || num === '') {
                return '0.00';
            }
            return parseFloat(num).toFixed(2);
        },

        // 时间格式化
        formatTime(time) {
            if (!time) return '';
            return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
        },

        // 获取盈亏状态
        getProfitStatus(ratio) {
            if (ratio > 0) return 'profit';
            if (ratio < 0) return 'loss';
            return 'neutral';
        },

        // 持仓详情跳转
        chicangDetail(value) {
            try {
                this.$router.push({
                    path: "/chicangDetail",
                    query: {
                        type: "dazong",
                        item: JSON.stringify(value)
                    }
                });
            } catch (error) {
                console.error('跳转持仓详情失败:', error);
                Toast('页面跳转失败');
            }
        },

        // 切换标签页
        async changeItemIndex(index) {
            try {
                // 重置状态
                this.resetListState();
                this.itemIndex = index;
                
                // 生成新的请求ID
                this.currentRequestId = Date.now();

                // 加载数据
                await this.loadInitialData();
            } catch (error) {
                console.error('切换标签页失败:', error);
                this.handleLoadError(error);
            }
        },

        // 重置列表状态
        resetListState() {
            this.list = [];
            this.pageNum = 1;
            this.finished = false;
            this.loading = false;
        },

        // 加载初始数据
        async loadInitialData() {
            await this.getOrderList(this.itemIndex);
        },

        // 获取订单列表
        async getOrderList(state) {
            if (this.loading || this.finished) {
                return;
            }

            try {
                await this.fetchOrderData(state);
            } catch (error) {
                this.handleLoadError(error);
            }
        },

        // 获取订单数据
        async fetchOrderData(state) {
            this.loading = true;
            
            // 记录当前请求ID
            const requestId = this.currentRequestId;

            const params = {
                state: state,
                stockCode: "",
                stockSpell: "",
                pageNum: this.pageNum,
                pageSize: 15,
            };

            try {
                const response = await api.getOrderList(params);
                
                // 检查是否为最新请求
                if (requestId !== this.currentRequestId) {
                    return; // 忽略过时的响应
                }

                this.handleOrderDataSuccess(response);

            } catch (error) {
                // 检查是否为最新请求
                if (requestId !== this.currentRequestId) {
                    return; // 忽略过时的响应
                }
                throw error;
            } finally {
                this.loading = false;
            }
        },

        // 处理订单数据成功响应
        handleOrderDataSuccess(response) {
            if (!response || !response.data || !response.data.list) {
                console.warn('订单数据格式异常:', response);
                this.finished = true;
                return;
            }

            const newList = response.data.list;

            // 检查是否还有更多数据
            if (newList.length < 15) {
                this.finished = true;
            }

            // 添加新数据到列表
            this.list.push(...newList);
            this.pageNum++;
        },

        // 处理加载错误
        handleLoadError(error) {
            console.error('加载数据失败:', error);
            this.loading = false;

            if (error.response) {
                switch (error.response.status) {
                    case 401:
                        Toast('登录已过期，请重新登录');
                        break;
                    case 403:
                        Toast('没有访问权限');
                        break;
                    case 500:
                        Toast('服务器异常，请稍后重试');
                        break;
                    default:
                        Toast('网络请求失败');
                }
            } else {
                Toast(error.message || '加载失败，请重试');
            }
        },

        // 撤单操作
        async withdrawOrder(item) {
            if (item.backStatus !== 0) {
                Toast('当前订单状态不允许撤单');
                return;
            }

            try {
                await this.confirmWithdrawOrder(item);
            } catch (error) {
                console.error('撤单操作失败:', error);
            }
        },

        // 确认撤单
        async confirmWithdrawOrder(item) {
            try {
                await MessageBox.confirm("确定撤单吗？", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                });

                await this.executeWithdrawOrder(item);

            } catch (error) {
                if (error === 'cancel') {
                    console.log('用户取消撤单');
                } else {
                    throw error;
                }
            }
        },

        // 执行撤单
        async executeWithdrawOrder(item) {
            const params = {
                id: item.id,
                userId: item.userId,
            };

            try {
                const result = await api.entrustedDivestmentDo(params);
                this.handleWithdrawResult(result);
            } catch (error) {
                Toast('撤单失败，请重试');
                throw error;
            }
        },

        // 处理撤单结果
        handleWithdrawResult(result) {
            if (result.status === 0) {
                Toast({
                    message: result.data || '撤单成功',
                    type: "success",
                });
                // 刷新委托列表
                this.refreshCurrentTab();
            } else {
                Toast({
                    message: result.data || result.msg || '撤单失败',
                    type: "error",
                });
            }
        },

        // 刷新当前标签页
        refreshCurrentTab() {
            this.resetListState();
            this.loadInitialData();
        }
    },
};
</script>


<style lang="less" scoped>
.trading-page {
    font-size: 0.3256rem;
    padding: 0;
    background: #F6F6F6;
    min-height: 100vh;

    &__header {
        width: 100%;
        height: 1.07rem;
    }

    &__tabs {
        display: flex;
        background: #fff;
        margin: 0.2326rem;
        border-radius: 0.1860rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

        .tab-item {
            flex: 1;
            display: flex;
            justify-content: center;

            &__content {
                position: relative;
                padding: 0.3488rem 0.6977rem;
                color: #7f8c8d;
                font-weight: 500;
                font-size: 0.3488rem;
                cursor: pointer;
                text-align: center;
                transition: all 0.3s ease;

                .tab-item__text {
                    font-weight: inherit;
                    font-size: inherit;
                }

                .tab-item__indicator {
                    position: absolute;
                    left: 50%;
                    bottom: 0.1162rem;
                    transform: translateX(-50%);
                    width: 0.4651rem;
                    height: 0.0697rem;
                    background: #EA001B;
                    border-radius: 0.0349rem;
                    display: none;
                    transition: all 0.3s ease;
                }

                &:hover {
                    color: #EA001B;
                }
            }

            &--active {
                .tab-item__content {
                    color: #EA001B;
                    font-weight: 600;

                    .tab-item__indicator {
                        display: block;
                    }
                }
            }
        }
    }
}

// 通用股票信息样式
.stock-info {
    &__name {
        font-size: 0.3721rem;
        color: #2c3e50;
        font-weight: 600;
        margin-bottom: 0.1162rem;
        line-height: 1.2;
    }

    &__code {
        display: flex;
        align-items: center;
        gap: 0.1162rem;

        .stock-code {
            color: #7f8c8d;
            font-size: 0.3023rem;
        }
    }
}

// 交易列表样式
.trading-list {
    margin: 0.2326rem;

    &__header {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 1fr;
        background: linear-gradient(135deg, #EA001B, #FF6B3D);
        border-radius: 0.1860rem 0.1860rem 0 0;
        overflow: hidden;

        .header-item {
            padding: 0.3488rem 0.1162rem;
            text-align: center;
            color: #fff;
            font-weight: 600;
            font-size: 0.3023rem;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }
    }

    &__container {
        background: #fff;
        border-radius: 0 0 0.1860rem 0.1860rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        overflow: hidden;

        .trading-item {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            border-bottom: 1px solid #f0f0f0;
            padding: 0.3488rem 0;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;

            &:last-child {
                border-bottom: none;
            }

            &:hover {
                background: linear-gradient(90deg, rgba(234, 0, 27, 0.05), rgba(255, 107, 61, 0.05));
                transform: translateX(0.0697rem);
            }

            &__stock {
                display: flex;
                align-items: center;
                padding-left: 0.3488rem;
            }

            &__data {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                gap: 0.0930rem;

                .data-primary {
                    font-size: 0.3488rem;
                    color: #2c3e50;
                    font-weight: 600;
                }

                .data-secondary {
                    font-size: 0.3023rem;
                    color: #7f8c8d;
                }

                &--profit {

                    .data-primary,
                    .data-secondary {
                        color: #e74c3c;
                    }
                }

                &--loss {

                    .data-primary,
                    .data-secondary {
                        color: #27ae60;
                    }
                }
            }
        }
    }
}

// 交易记录样式
.trading-record {
    background: #fff;
    border-radius: 0.1860rem;
    margin: 0.2326rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    cursor: pointer;
    transition: all 0.3s ease;
    overflow: hidden;
    border: 1px solid #EEEEEE;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
        border-color: #EA001B;
    }

    .trading-item {
        padding: 0.3488rem;
        border: none;
    }

    &__time {
        display: flex;
        justify-content: space-between;
        padding: 0 0.3488rem 0.2326rem;
        color: #95a5a6;
        background: #FAFAFA;

        .time-item {
            font-size: 0.2791rem;
            padding: 0.1162rem 0;
        }
    }

    &__action {
        padding: 0.3488rem;
        background: #FAFAFA;

        .action-button {
            border: 2px solid #EA001B;
            color: #EA001B;
            background: transparent;
            height: 0.9302rem;
            border-radius: 0.4651rem;
            text-align: center;
            width: 100%;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            font-size: 0.3256rem;

            &:hover {
                background: linear-gradient(135deg, #EA001B, #FF6B3D);
                color: #fff;
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(234, 0, 27, 0.3);
            }

            &:active {
                transform: translateY(0);
            }
        }
    }
}

// 委托列表样式
.delegation-list {
    padding: 0 0.2326rem;

    .delegation-item {
        margin-bottom: 0.4651rem;
        background: #fff;
        border-radius: 0.1860rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        overflow: hidden;
        transition: all 0.3s ease;
        border: 1px solid #EEEEEE;

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
            border-color: #EA001B;
        }

        &__header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.3488rem;
            background: #FAFAFA;
            color: #2c3e50;
            border-bottom: 1px solid #EEEEEE;

            .stock-info {
                &__name {
                    color: #2c3e50;
                    font-weight: 600;
                }

                &__code {
                    .stock-code {
                        color: #7f8c8d;
                    }
                }
            }
        }

        &__action {
            .withdrawal-button {
                background: linear-gradient(135deg, #EA001B, #FF6B3D);
                color: #fff;
                border: none;
                width: 1.8604rem;
                height: 0.6976rem;
                border-radius: 0.3488rem;
                font-size: 0.3255rem;
                cursor: pointer;
                transition: all 0.3s ease;
                font-weight: 600;
                box-shadow: 0 2px 8px rgba(234, 0, 27, 0.3);

                &:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 12px rgba(234, 0, 27, 0.4);
                }

                &:active {
                    transform: translateY(0);
                }

                &:disabled {
                    background: linear-gradient(135deg, #bdc3c7, #95a5a6);
                    cursor: not-allowed;
                    transform: none;
                    box-shadow: none;
                }
            }
        }

        &__info {
            padding: 0.3488rem;

            .info-row {
                display: flex;
                justify-content: space-between;
                margin-bottom: 0.2326rem;
                padding: 0.2326rem;
                background: rgba(102, 126, 234, 0.05);
                border-radius: 0.1162rem;
                border-left: 3px solid #667eea;

                &:last-child {
                    margin-bottom: 0;
                    border-left-color: #EA001B;
                    background: rgba(234, 0, 27, 0.05);
                }

                .info-item {
                    flex: 1;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    &__label {
                        color: #7f8c8d;
                        font-size: 0.3023rem;
                        font-weight: 500;
                    }

                    &__value {
                        color: #2c3e50;
                        font-size: 0.3256rem;
                        font-weight: 600;
                    }
                }
            }
        }
    }
}

// 加载状态优化
.van-list {
    .van-loading {
        color: #EA001B;
    }
}

// 响应式适配
@media (max-width: 375px) {
    .trading-page {
        &__tabs {
            margin: 0.1162rem;
            padding: 0.2326rem 0.1162rem 0;

            .tab-item {
                &__content {
                    padding: 0.1860rem 0.2326rem;

                    .tab-item__text {
                        font-size: 0.3023rem;
                    }
                }
            }
        }
    }

    .trading-list {
        margin: 0.1162rem;

        &__header {
            .header-item {
                padding: 0.2326rem 0.0930rem;
                font-size: 0.2791rem;
            }
        }

        &__container {
            .trading-item {
                padding: 0.2326rem 0;

                &__stock {
                    padding-left: 0.2326rem;
                }

                &__data {
                    gap: 0.0697rem;

                    .data-primary {
                        font-size: 0.3023rem;
                    }

                    .data-secondary {
                        font-size: 0.2791rem;
                    }
                }
            }
        }
    }

    .trading-record {
        margin: 0.1162rem;

        &__action {
            padding: 0.2326rem;

            .action-button {
                height: 0.8372rem;
                font-size: 0.3023rem;
            }
        }
    }

    .delegation-list {
        padding: 0 0.1162rem;

        .delegation-item {
            &__header {
                padding: 0.2326rem;
            }

            &__action {
                .withdrawal-button {
                    width: 1.6279rem;
                    height: 0.5814rem;
                    font-size: 0.2791rem;
                }
            }

            &__info {
                padding: 0.2326rem;

                .info-row {
                    padding: 0.1860rem;

                    .info-item {
                        &__label {
                            font-size: 0.2791rem;
                        }

                        &__value {
                            font-size: 0.3023rem;
                        }
                    }
                }
            }
        }
    }
}

// 动画效果
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(0.4651rem);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.trading-list,
.delegation-list {
    animation: slideInUp 0.3s ease-out;
}

.trading-record,
.delegation-item {
    animation: slideInUp 0.3s ease-out;
}

// 滚动条美化
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #F6F6F6;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #EA001B, #FF6B3D);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #d00019, #e55a37);
}
</style>