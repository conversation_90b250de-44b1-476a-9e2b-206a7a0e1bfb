<template>
    <div class="container">
        <div class="header">
            <van-nav-bar title="大宗交易记录" left-arrow @click-left="$router.go(-1)" fixed></van-nav-bar>
        </div>
        <div class="trading-page__tabs">
            <div :class="['tab-item', { 'tab-item--active': itemIndex === 0 }]" @click="changeItemIndex(0)">
                <div class="tab-item__content">
                    <span class="tab-item__text">我的持仓</span>
                    <span class="tab-item__indicator"></span>
                </div>
            </div>
            <div :class="['tab-item', { 'tab-item--active': itemIndex === 1 }]" @click="changeItemIndex(1)">
                <div class="tab-item__content">
                    <span class="tab-item__text">交易记录</span>
                    <span class="tab-item__indicator"></span>
                </div>
            </div>
            <div :class="['tab-item', { 'tab-item--active': itemIndex === 2 }]" @click="changeItemIndex(2)">
                <div class="tab-item__content">
                    <span class="tab-item__text">我的委托</span>
                    <span class="tab-item__indicator"></span>
                </div>
            </div>
        </div>

        <div class="list" v-if="itemIndex == 0">
            <div class="list_title">
                <div class="item">名称</div>
                <div class="item">持仓 | 市值</div>
                <div class="item">现价 | 成本</div>
                <div class="item">盈亏 | 涨幅</div>
            </div>
            <div class="list_container">
                <van-list v-model="loading" :finished="finished" :immediate-check="false" :finished-text="$t('hj43')"
                    @load="getOrderList(1)">
                    <div v-for="value in list" class="item" :key="value.id" @click="chicangDetail(value)">
                        <div class="ebox" style="justify-content: left;">
                            <div class="stock">
                                <div class="name">{{ value.stockName }}</div>
                                <div class="child">
                                    <stock-tag-info :stock-code="value.stockGid" />
                                </div>
                            </div>
                        </div>
                        <div class="cbox">
                            <span>{{ parseNumber(value.buyNum) }}</span>
                            <span>{{ parseNumber(value.buyPrice) }}</span>
                        </div>
                        <div class="cbox">
                            <span>{{ parseNumber(value.now_price) }}</span>
                            <span>{{ parseNumber(value.buyOrderPrice) }}</span>
                        </div>
                        <div class="cbox">
                            <span :class="value.profitAndLose > 0 ? 'red' : 'green'">{{ value.profitAndLose }}</span>
                            <span :class="value.profitAndLossRatio > 0 ? 'red' : 'green'">{{ value.profitAndLossRatio
                                }}%</span>
                        </div>
                    </div>
                </van-list>
            </div>
        </div>

        <div class="list" v-if="itemIndex == 1">
            <div class="list_title">
                <div class="item">股票 | 代码</div>
                <div class="item">本金 | 数量</div>
                <div class="item">买入 | 卖出价</div>
                <div class="item">收益 | 涨幅</div>
            </div>
            <div class="list_container">
                <van-list v-model="loading" :finished="finished" :immediate-check="false" :finished-text="$t('hj43')"
                    @load="getOrderList(1)">
                    <div v-for="value in list" :key="value.id"
                        style="border-bottom: solid 1px rgba(223, 223, 223, 1); padding-bottom: 0.3488rem;">
                        <div class="item" style="border: none;">
                            <div class="ebox" style="justify-content: left;">
                                <div class="stock">
                                    <div class="name">{{ value.stockName }}</div>
                                    <div class="child">
                                        <stock-tag-info :stock-code="value.stockGid" />
                                    </div>
                                </div>
                            </div>
                            <div class="cbox">
                                <span>{{ parseNumber(value.buyPrice) }}</span>
                                <span>{{ parseNumber(value.buyNum) }}</span>
                            </div>
                            <div class="cbox">
                                <span>{{ parseNumber(value.buyOrderPrice) }}</span>
                                <span>{{ parseNumber(value.sellOrderPrice) }}</span>
                            </div>
                            <div :class="`cbox ${value.profitAndLossRatio > 0 ? 'red' : 'green'}`">
                                <span>{{ value.profitAndLose }}</span>
                                <span>{{ value.profitAndLossRatio }}%</span>
                            </div>
                        </div>
                        <div class="time">
                            <div>{{ formatTime(value.buyOrderTime) }}</div>
                            <div>{{ formatTime(value.sellOrderTime) }}</div>
                        </div>
                        <div class="dbtn" @click="chicangDetail(value)">查看详情</div>
                    </div>
                </van-list>
            </div>
        </div>
        <div class="weituo_list" v-if="itemIndex == 2">
            <van-list v-model="loading" :finished="finished" :immediate-check="false" :finished-text="$t('hj43')"
                @load="getOrderList(2)">
                <div v-for="value in list" :key="value.id" @click="weituoDetail(value)" class="weituo_item">
                    <div class="stock">
                        <div class="name">{{ value.stockName }}</div>
                        <div class="child">
                            <stock-tag-info :stock-code="value.stockGid" />
                        </div>
                    </div>
                    <div class="info">
                        <div class="item">
                            <div>买卖类别</div>
                            <div>证券买入</div>
                        </div>
                        <div class="item">
                            <div>当前状态</div>
                            <div>挂单</div>
                        </div>
                        <div class="item">
                            <div>委托手数</div>
                            <div>{{ value.orderNum / 100 }}</div>
                        </div>
                        <div class="item">
                            <div>委托价格</div>
                            <div>{{ parseNumber(value.buyOrderPrice) }}</div>
                        </div>
                    </div>
                </div>
            </van-list>
        </div>
    </div>
</template>

<script>
import * as api from "@/axios/api";
import StockTagInfo from "@/components/stock-tag-info.vue";
import dayjs from "dayjs";

export default {
    name: "DazongTradingPage",
    components: {
        StockTagInfo
    },
    data() {
        return {
            list: [],
            finished: false,
            loading: true,
            itemIndex: 0,
            pageNum: 1,
            currentRequestId: 0, // 添加请求ID来追踪最新请求
        };
    },
    computed: {
        // 当前标签页标题
        currentTabTitle() {
            const tabs = ['我的持仓', '交易记录', '我的委托'];
            return tabs[this.itemIndex] || '未知';
        },
        // 是否有数据
        hasData() {
            return this.list.length > 0;
        }
    },
    mounted() {
        this.initializePage();
    },
    beforeDestroy() {
        // 组件销毁时重置请求ID，确保不处理过时响应
        this.currentRequestId = 0;
    },
    methods: {
        // 初始化页面
        async initializePage() {
            try {
                await this.changeItemIndex(0);
            } catch (error) {
                console.error('页面初始化失败:', error);
                this.$toast('页面加载失败，请重试');
            }
        },

        // 格式化数字
        parseNumber(number) {
            if (!number && number !== 0) return '0.00';
            return parseFloat(number).toFixed(2);
        },

        // 切换标签页
        async changeItemIndex(index) {
            try {
                this.resetPageData();
                this.itemIndex = index;
                
                // 生成新的请求ID
                this.currentRequestId = Date.now();
                
                await this.getOrderList(index);
            } catch (error) {
                console.error(`切换到标签页${index}失败:`, error);
                this.$toast('切换标签页失败');
            }
        },

        // 重置页面数据
        resetPageData() {
            this.pageNum = 1;
            this.list = [];
            this.finished = false;
        },

        // 查看持仓详情
        chicangDetail(value) {
            if (!value || !value.id) {
                this.$toast('数据异常，无法查看详情');
                return;
            }

            this.$router.push({
                path: "/chicangDetail?type=dazong&item=" + JSON.stringify(value),
            });
        },

        // 查看委托详情
        weituoDetail(value) {
            if (!value || !value.id) {
                this.$toast('数据异常，无法查看详情');
                return;
            }

            this.$router.push({
                path: "/weituoDetail?item=" + JSON.stringify(value),
            });
        },

        // 获取订单列表
        async getOrderList(state) {
            this.loading = true;
            
            // 记录当前请求ID
            const requestId = this.currentRequestId;

            try {
                const params = {
                    state: state,
                    type: 3,
                    stockCode: "",
                    stockSpell: "",
                    pageNum: this.pageNum,
                    pageSize: 15,
                };

                const res = await api.getOrderList(params);
                
                // 检查是否为最新请求
                if (requestId !== this.currentRequestId) {
                    return; // 忽略过时的响应
                }

                this.handleOrderListResponse(res);

            } catch (error) {
                // 检查是否为最新请求
                if (requestId !== this.currentRequestId) {
                    return; // 忽略过时的响应
                }
                this.handleOrderListError(error);
            } finally {
                this.loading = false;
            }
        },

        // 处理订单列表响应
        handleOrderListResponse(res) {
            if (!res || !res.data || !Array.isArray(res.data.list)) {
                throw new Error('数据格式异常');
            }

            const newList = res.data.list;

            // 判断是否已加载完毕
            if (newList.length < 15) {
                this.finished = true;
            }

            // 合并数据
            this.list = [...this.list, ...newList];

            // 增加页码
            this.pageNum++;

            console.log(`加载${this.currentTabTitle}数据成功，本次加载${newList.length}条`);
        },

        // 处理订单列表错误
        handleOrderListError(error) {
            console.error('获取订单列表失败:', error);

            // 用户友好的错误提示
            if (error.response) {
                switch (error.response.status) {
                    case 401:
                        this.$toast('登录已过期，请重新登录');
                        break;
                    case 403:
                        this.$toast('没有访问权限');
                        break;
                    case 500:
                        this.$toast('服务器异常，请稍后重试');
                        break;
                    default:
                        this.$toast('网络请求失败，请检查网络连接');
                }
            } else if (error.request) {
                this.$toast('网络连接失败，请检查网络');
            } else {
                this.$toast('数据加载失败，请重试');
            }
        },

        // 时间格式化（虽然模板中已使用dayjs，但可以作为方法提供）
        formatTime(time, format = 'YYYY-MM-DD HH:mm:ss') {
            if (!time) return '--';
            return dayjs(time).format(format);
        },

        // 计算盈亏状态
        getProfitStatus(value) {
            if (!value) return 'neutral';
            return value > 0 ? 'profit' : value < 0 ? 'loss' : 'neutral';
        }
    },
};
</script>


<style lang="less" scoped>
.container {
    font-size: 0.3256rem;
    padding: 0;
    min-height: 100vh;

    .header {
        width: 100%;
        height: 1.07rem;
    }

    .weituo_list {
        padding: 0.4651rem;
        background: transparent;
        .weituo_item{
            margin-bottom: 0.3488rem;
        }
        >div {
            background: #fff;
            border-radius: 0.2326rem;
            margin-bottom: 0.3488rem;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            overflow: hidden;

            &:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
            }

            &:last-child {
                margin-bottom: 0;
            }
        }

        .stock {
            padding: 0.4651rem 0.4651rem 0.2326rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-bottom: 1px solid #f0f2f7;

            .name {
                font-size: 0.4186rem;
                font-weight: 600;
                color: #2c3e50;
                margin-bottom: 0.1162rem;
            }

            .child {
                margin-top: 0.1162rem;
                font-size: 0.3255rem;
                display: flex;
                align-items: center;
                gap: 0.1162rem;
            }
        }

        .info {
            padding: 0.3488rem 0.4651rem 0.4651rem;

            .item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.3488rem 0;
                border-bottom: 1px solid #f0f2f7;
                transition: all 0.3s ease;

                &:last-child {
                    border-bottom: none;
                }

                &:hover {
                    background: linear-gradient(90deg, #f8f9fa 0%, #ffffff 100%);
                    margin: 0 -0.2326rem;
                    padding-left: 0.2326rem;
                    padding-right: 0.2326rem;
                    border-radius: 0.1395rem;
                }

                div:nth-of-type(1) {
                    color: #7f8c8d;
                    font-size: 0.3256rem;
                    font-weight: 500;
                    position: relative;

                    &::before {
                        content: '';
                        position: absolute;
                        left: -0.2326rem;
                        top: 50%;
                        transform: translateY(-50%);
                        width: 0.0930rem;
                        height: 0.0930rem;
                        background: #EA001B;
                        border-radius: 50%;
                        opacity: 0.7;
                    }
                }

                div:nth-of-type(2) {
                    color: #2c3e50;
                    font-weight: 600;
                    font-size: 0.3721rem;
                    text-align: right;
                    max-width: 60%;
                    word-break: break-all;
                }
            }
        }
    }

    .list {
        margin: 0.2326rem;
        background: #fff;
        border-radius: 0.2326rem;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
        overflow: hidden;

        .list_title {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            background: linear-gradient(135deg, #EA001B 0%, #C8001A 100%);
            box-shadow: 0 2px 8px rgba(234, 0, 27, 0.3);

            .item {
                flex: 1;
                text-align: center;
                line-height: 1.1627rem;
                color: #fff;
                font-weight: 600;
                font-size: 0.3488rem;
                padding: 0.2326rem 0.1162rem;
            }
        }

        .list_container {
            .item {
                display: grid;
                grid-template-columns: 1fr 1fr 1fr 1fr;
                border-bottom: 1px solid #f0f2f7;
                padding: 0.4651rem 0;
                transition: all 0.3s ease;
                position: relative;

                &:hover {
                    background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
                    transform: translateY(-1px);
                }

                &:last-child {
                    border-bottom: none;
                }

                .ebox {
                    .stock {
                        padding-left: 0.4651rem;

                        .name {
                            font-size: 0.3721rem;
                            font-weight: 600;
                            color: #2c3e50;
                            margin-bottom: 0.1162rem;
                        }

                        .child {
                            margin-top: 0.1162rem;
                            font-size: 0.3255rem;
                            display: flex;
                            align-items: center;
                            gap: 0.1162rem;
                        }
                    }
                }

                .cbox {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    line-height: 0.4651rem;
                    gap: 0.0930rem;

                    span {
                        font-size: 0.3488rem;
                        font-weight: 500;

                        &:first-child {
                            font-weight: 600;
                            font-size: 0.3721rem;
                        }

                        &.red {
                            color: #EA001B;
                        }

                        &.green {
                            color: #00C851;
                        }
                    }

                    &.red span {
                        color: #EA001B;
                    }

                    &.green span {
                        color: #00C851;
                    }
                }
            }

            .time {
                display: flex;
                justify-content: space-between;
                padding: 0.3488rem 0.4651rem;
                background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
                color: #7f8c8d;
                font-size: 0.3023rem;
                border-top: 1px solid #f0f2f7;
                margin: 0 0.4651rem;
                border-radius: 0.1395rem;

                div {
                    padding: 0.1162rem 0.2326rem;
                    background: #fff;
                    border-radius: 0.0930rem;
                    border: 1px solid #e9ecef;
                    font-weight: 500;
                }
            }

            .cbtn {
                display: block;
                background: linear-gradient(135deg, #EA001B 0%, #C8001A 100%);
                height: 0.7907rem;
                color: #fff;
                text-align: center;
                line-height: 0.7907rem;
                border-radius: 0.3953rem;
                width: 1.6279rem;
                font-weight: 500;
                box-shadow: 0 3px 6px rgba(234, 0, 27, 0.3);
                transition: all 0.3s ease;

                &:hover {
                    background: linear-gradient(135deg, #C8001A 0%, #A50015 100%);
                    transform: translateY(-2px);
                    box-shadow: 0 6px 12px rgba(234, 0, 27, 0.4);
                }
            }

            .dbtn {
                border: 2px solid #EA001B;
                color: #EA001B;
                height: 0.9302rem;
                border-radius: 0.2326rem;
                line-height: 0.8837rem;
                text-align: center;
                margin: 0.4651rem;
                margin-bottom: 0.2326rem;
                font-weight: 600;
                transition: all 0.3s ease;
                cursor: pointer;
                background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);

                &:hover {
                    background: linear-gradient(135deg, #EA001B 0%, #C8001A 100%);
                    color: #fff;
                    transform: translateY(-1px);
                    box-shadow: 0 4px 8px rgba(234, 0, 27, 0.3);
                }

                &:active {
                    transform: translateY(0);
                }
            }
        }
    }

    .trading-page__tabs {
        display: flex;
        background: #fff;
        margin: 0.2326rem;
        border-radius: 0.1860rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

        .tab-item {
            flex: 1;
            display: flex;
            justify-content: center;

            &__content {
                position: relative;
                padding: 0.3488rem 0.6977rem;
                color: #7f8c8d;
                font-weight: 500;
                font-size: 0.3488rem;
                cursor: pointer;
                text-align: center;
                transition: all 0.3s ease;

                .tab-item__text {
                    font-weight: inherit;
                    font-size: inherit;
                }

                .tab-item__indicator {
                    position: absolute;
                    left: 50%;
                    bottom: 0.1162rem;
                    transform: translateX(-50%);
                    width: 0.4651rem;
                    height: 0.0697rem;
                    background: #EA001B;
                    border-radius: 0.0349rem;
                    display: none;
                    transition: all 0.3s ease;
                }

                &:hover {
                    color: #EA001B;
                }
            }

            &--active {
                .tab-item__content {
                    color: #EA001B;
                    font-weight: 600;

                    .tab-item__indicator {
                        display: block;
                    }
                }
            }
        }
    }
}

/* 全局颜色类 */
.red {
    color: #EA001B !important;
}

.green {
    color: #00C851 !important;
}

/* 加载状态样式 */
:deep(.van-list__finished-text) {
    color: #95a5a6;
    font-size: 0.3256rem;
    padding: 0.4651rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    margin: 0.2326rem;
    border-radius: 0.2326rem;
    text-align: center;
    border: 1px solid #f0f2f7;
}

:deep(.van-loading) {
    padding: 0.4651rem 0;
}

/* 动画效果 */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(0.4651rem);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.list,
.weituo_list {
    animation: slideIn 0.4s ease-out;
}

/* 响应式优化 */
@media (max-width: 375px) {
    .container {
        .trading-page__tabs {
            margin: 0.1162rem;

            .tab-item {
                &__content {
                    padding: 0.2791rem 0.3488rem;

                    .tab-item__text {
                        font-size: 0.3023rem;
                    }
                }
            }
        }

        .list .list_title .item {
            font-size: 0.3023rem;
            padding: 0.1395rem 0.0465rem;
        }
    }
}
</style>