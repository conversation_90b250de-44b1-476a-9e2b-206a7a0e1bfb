<template>
    <div class="container" v-if="currentItem && currentItem.stockName">
        <div class="header">
            <van-nav-bar title="委托详情" left-arrow @click-left="$router.go(-1)" fixed></van-nav-bar>
        </div>
        <div class="ebox">
            <div class="elabel">委托股票</div>
            <div class="enr">
                <div class="stock">
                    <div class="name"></div>
                    <div class="child">
                        <stock-tag-info :stock-code="currentItem.stockGid" />
                    </div>
                </div>
            </div>
        </div>
        <div class="ebox">
            <div class="elabel">买卖类型</div>
            <div class="enr">证券买入</div>
        </div>
        <div class="ebox">
            <div class="elabel">成交类型</div>
            <div class="enr">挂单</div>
        </div>
        <div class="ebox">
            <div class="elabel">委托手数</div>
            <div class="enr">{{currentItem.orderNum / 100}}</div>
        </div>
        <div class="ebox">
            <div class="elabel">委托股数</div>
            <div class="enr">{{currentItem.orderNum}}</div>
        </div>
        <div class="ebox">
            <div class="elabel">委托价格</div>
            <div class="enr">{{parseNumber(currentItem.buyOrderPrice)}}</div>
        </div>
        <div class="ebox">
            <div class="elabel">委托时间</div>
            <div class="enr">{{ dayjs(currentItem.buyOrderTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
        </div>
        <div class="ebtn" @click="withdrawOrder(currentItem)">取消委托</div>
    </div>
</template>

<script>
import * as api from "@/axios/api";
import { MessageBox, Toast } from "mint-ui";
import StockTagInfo from "@/components/stock-tag-info.vue";

export default {
    data() {
        return {
            currentItem: {},
        };
    },
    components: {
        StockTagInfo,
    },
    mounted() {
        this.currentItem = JSON.parse(
            decodeURIComponent(this.$route.query.item)
        );
        console.log(this.currentItem);
    },
    methods: {
        parseNumber(number) {
            return parseFloat(number).toFixed(2);
        },
        withdrawOrder(item) {
            if (item.backStatus != 0) {
                return false;
            }
            const params = {
                id: item.id,
                userId: item.userId,
            };
            MessageBox.confirm("确定撤单吗？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                let result = await api.entrustedDivestmentDo(params);
                if (result.status === 0) {
                    Toast({
                        message: result.data,
                        type: "success",
                    });
                    this.$router.go(-1);
                } else {
                    Toast({
                        message: result.data,
                        type: "error",
                    });
                }
            });
        },
    },
};
</script>

<style lang="less" scoped>
.container {
    font-size: 0.3256rem;
    padding: 0;
    .header {
        width: 100%;
        height: 1.07rem;
    }
    .ebox {
        border-bottom: solid 1px #f1f1f1;
        display: flex;
        justify-content: space-between;
        margin: 0 0.3488rem;
        padding: 0.3488rem 0;
        .elabel {
            color: rgba(125, 125, 125, 1);
        }
        .enr {
            .stock {
                .name {
                    font-size: 0.372rem;
                }
                .child {
                    margin-top: 0.1162rem;
                    font-size: 0.3255rem;
                    display: flex;
                    .tag {
                        background: #6d9cff;
                        font-size: 0.2791rem;
                        color: #fff;
                        width: 0.3256rem;
                        height: 0.3256rem;
                        line-height: 0.3256rem;
                        text-align: center;
                        margin-right: 0.1162rem;
                    }
                }
            }
        }
    }
    .ebtn {
        display: block;
        background: rgba(238, 0, 17, 1);
        height: 0.9302rem;
        color: #fff;
        text-align: center;
        line-height: 0.9302rem;
        border-radius: 0.4651rem;
        margin: 0.3488rem;
    }
}
</style>