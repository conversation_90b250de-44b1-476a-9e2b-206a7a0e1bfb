<template>
    <div class="modern-register-container">
        <!-- 动态背景系统 -->
        <div class="background-system">
            <!-- 主背景渐变 -->
            <div class="main-gradient"></div>
            
            <!-- 渐变光晕 -->
            <div class="gradient-orbs">
                <div class="orb orb-1"></div>
                <div class="orb orb-2"></div>
                <div class="orb orb-3"></div>
            </div>
        </div>
<!-- 
        <div class="header">
            <van-nav-bar title="开户注册" />
        </div> -->

        <!-- 主要内容 -->
        <div class="content">
            <!-- Logo 品牌区域 -->
            <div class="brand-section">
                <div class="logo-container">
                    <div class="logo-box">
                        账户开户
        </div>
                </div>
                
                <!-- 品牌特色展示 -->
                <div class="brand-features">
                    <div class="feature-item">
                        <img src="~@/assets/newtemp/22.png" alt="零佣金开户" class="feature-icon">
                        <span>零佣金开户</span>
                    </div>
                    <div class="feature-separator">|</div>
                    <div class="feature-item">
                        <img src="~@/assets/newtemp/22.png" alt="极速交易" class="feature-icon">
                        <span>极速交易</span>
                    </div>
                    <div class="feature-separator">|</div>
                    <div class="feature-item">
                        <img src="~@/assets/newtemp/22.png" alt="安全可靠" class="feature-icon">
                        <span>安全可靠</span>
                    </div>
                </div>
            </div>

            <!-- 注册表单面板 -->
            <div class="register-form-panel">
                <div class="form-content">
                    <!-- 手机号 -->
                    <div class="input-section">
                        <div class="input-label">手机号</div>
                        <div class="input-wrapper">
                            <input 
                                type="text" 
                                placeholder="请输入手机号" 
                                v-model="phone"
                                class="form-input"
                                autocomplete="off"
                            />
                        </div>
                    </div>

                    <!-- 密码 -->
                    <div class="input-section">
                        <div class="input-label">登录密码</div>
                        <div class="input-wrapper">
                            <input 
                                type="password" 
                                placeholder="设置登录密码" 
                                v-model="userPassword"
                                class="form-input"
                                autocomplete="new-password"
                            />
                        </div>
                    </div>

                    <!-- 确认密码 -->
                    <div class="input-section">
                        <div class="input-label">确认密码</div>
                        <div class="input-wrapper">
                            <input 
                                type="password" 
                                placeholder="确认登录密码" 
                                v-model="rePassword"
                                class="form-input"
                                autocomplete="new-password"
                            />
                        </div>
                    </div>

                    <!-- 邀请码 -->
                    <div class="input-section">
                        <div class="input-label">邀请码</div>
                        <div class="input-wrapper">
                            <input 
                                type="text" 
                                placeholder="请输入邀请码" 
                                v-model="userName"
                                class="form-input"
                                autocomplete="off"
                            />
                        </div>
                    </div>

                    <!-- 协议条款 -->
                    <div class="simple-agreement-section">
                        <van-checkbox v-model="checked" checked-color="#1890ff">
                            <span class="simple-agreement-text">
                                已阅读并同意
                                <span @click.stop="$router.push({ path: '/xieyiMianze' })" class="simple-agreement-link">
                                    《服务协议》
                                </span>
                            </span>
                        </van-checkbox>
                    </div>

                    <!-- 注册按钮 -->
                    <button class="simple-register-button" @click="gook()">
                        立即开户
                    </button>
                    
                    <!-- 登录链接 -->
                    <div class="simple-login-redirect">
                        <span class="login-text">已有账户？</span>
                        <span @click="$router.push({ path: '/login' })" class="login-link">立即登录</span>
                </div>

                    <!-- 服务优势 -->
                    <div class="service-highlights">
                        <div class="highlight-item">
                            <img src="~@/assets/newtemp/23.png" alt="急速开户" class="highlight-icon">
                            <div class="highlight-text">
                                <div class="highlight-title">急速开户</div>
                                <div class="highlight-desc">3分钟完成</div>
                </div>
            </div>
                        <div class="highlight-item">
                            <img src="~@/assets/newtemp/24.png" alt="银行级安全" class="highlight-icon">
                            <div class="highlight-text">
                                <div class="highlight-title">银行级安全</div>
                                <div class="highlight-desc">资金保障</div>
                            </div>
                </div>
                        <div class="highlight-item">
                            <img src="~@/assets/newtemp/25.png" alt="专业服务" class="highlight-icon">
                            <div class="highlight-text">
                                <div class="highlight-title">专业服务</div>
                                <div class="highlight-desc">投资顾问</div>
                </div>
            </div>
                </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import headers from "./components/header.vue";
import Logo from "@/assets/img/LOGO2.png";
import { isNull, isPhone } from "@/utils/utils";
import { Toast } from "mint-ui";
import * as api from "@/axios/api";
export default {
    components: {
        headers,
    },
    name: "newRegister",
    data() {
        return {
            checked: false,
            verification: this.$t("hj25"),
            loginWay: this.$t("hj26"),
            placeholder: this.$t("hj27"),
            Logo,
            phone: "",
            userName: "",
            code: "",
            userPassword: "",
            btnClass: false,
            codeshow: true,
            count: "",
            clickFalg: 0,
            rePassword: "",
            agree: false,
            dengl: false,
            alertShow: false,
            closable: false,
            texts: "",
            elType: "warning",
            loginBtn: false,
        };
    },
    mounted() {
        console.log("222222");
        const agentCode = this.$route.query.agentCode;
        console.log("" + this.$route.query.agentCode);

        if (agentCode !== undefined && agentCode !== "") {
            this.userName = agentCode;
        }
    },
    methods: {
        handleInput() {
            if (
                this.userPassword !== "" &&
                this.phone !== "" &&
                this.userPassword == this.rePassword &&
                this.userName != ""
            ) {
                this.btnClass = true;
            } else {
                this.btnClass = false;
            }
        },
        checkCodeBox() {
            if (isNull(this.phone) || !isPhone(this.phone)) {
                // Toast('请输入正确的手机号')
                // this.texts = this.$t('hj28')
                // this.alertShow = true
                // setTimeout(() => {
                //   this.alertShow = false
                // }, 2000)
                Toast(this.$t("hj28"));
            } else {
                this.checkPhone();
            }
        },
        async is_login() {
            // 获取localStorage中的token
            let token = localStorage.getItem("USERTOEKN");
            if (token) {
                this.$router.push("/home");
            }
        },

        async getcode() {
            // if(!this.checkCode()){
            //     // 验证图形码是否正确
            //     Toast('请输入正确图形验证码')
            //     return
            // }
            // 获取验证码
            if (this.clickFalg !== 0) {
                this.clickFalg = 0;
                return;
            }
            this.clickFalg++;
            //   var reg = 11 && /^((13|14|15|17|18)[0-9]{1}\d{8})$/
            let reg = /^[0-9]{11}$/; // 手机号码验证
            if (isNull(this.phone)) {
                // this.texts = this.$t('hj29')
                // this.alertShow = true
                // setTimeout(() => {
                //   this.alertShow = false
                // }, 2000)
                Toast(this.$t("hj29"));
            } else {
                if (!reg.test(this.phone)) {
                    // this.texts = this.$t('hj28')
                    // this.alertShow = true
                    // setTimeout(() => {
                    //   this.alertShow = false
                    // }, 2000)
                    Toast(this.$t("hj28"));
                } else {
                    //   var sign  = this.$md5(this.phone+'W&WzL42v').toUpperCase()
                    let result = await api.getCode({ phoneNum: this.phone });
                    if (result.status === 0) {
                        const TIME_COUNT = 60;
                        if (!this.timer) {
                            this.count = TIME_COUNT;
                            this.codeshow = false;
                            this.clickFalg = 0;
                            this.timer = setInterval(() => {
                                if (
                                    this.count > 0 &&
                                    this.count <= TIME_COUNT
                                ) {
                                    this.count--;
                                } else {
                                    this.codeshow = true;
                                    clearInterval(this.timer);
                                    this.timer = null;
                                }
                            }, 1000);
                        } else {
                            Toast(result.msg);
                            // this.texts = result.msg
                            // this.elType = "success"
                            // this.alertShow = true
                            // setTimeout(() => {
                            //   this.alertShow = false
                            //   this.elType = "warning"
                            // }, 2000)
                        }
                    } else {
                        // this.texts = result.msg
                        // this.alertShow = true
                        // setTimeout(() => {
                        //   this.alertShow = false
                        // }, 2000)
                        Toast(result.msg);
                    }
                }
            }
        },
        async gook() {
            this.dengl = true;
            if (!this.checked) {
                Toast("需同意注册协议才能注册!");
                return;
            }
            setTimeout(() => {
                this.dengl = false;
            }, 1000);
            if (this.loginBtn) {
                return;
            }
            this.loginBtn = true;
            // 注册
            // if (!this.agree) {
            //   Toast('需同意注册协议才能注册!')
            //   this.loginBtn = false;
            // } else
            if (isNull(this.phone) || !isPhone(this.phone)) {
                // this.texts = this.$t('hj28')
                // this.alertShow = true
                // setTimeout(() => {
                //   this.alertShow = false
                // }, 2000)
                Toast(this.$t("hj28"));
                this.loginBtn = false;
            } else if (isNull(this.userPassword)) {
                // this.texts = this.$t('hj30')
                // this.alertShow = true
                // setTimeout(() => {
                //   this.alertShow = false
                // }, 2000)
                Toast(this.$t("hj30"));
                this.loginBtn = false;
            } else if (isNull(this.rePassword)) {
                // this.texts = this.$t('hj31')
                // this.alertShow = true
                // setTimeout(() => {
                //   this.alertShow = false
                // }, 2000)
                Toast(this.$t("hj31"));
                this.loginBtn = false;
            } else {
                if (this.userPassword !== this.rePassword) {
                    // this.texts = this.$t('hj32')
                    // this.alertShow = true
                    // setTimeout(() => {
                    //   this.alertShow = false
                    // }, 2000)
                    Toast(this.$t("hj32"));
                    this.password = 0;
                    this.password2 = 0;
                    this.loginBtn = false;
                } else if (this.userPassword.length < 6 || this.userPassword.length > 12) {
                    // this.texts = this.$t('hj19')
                    // this.alertShow = true
                    // setTimeout(() => {
                    //   this.alertShow = false
                    // }, 2000)
                    Toast("密码长度必须为6-12位");
                    this.loginBtn = false;
                } else if (isNull(this.userName)) {
                    // this.texts = this.$t('hj33')
                    // this.alertShow = true
                    // setTimeout(() => {
                    //   this.alertShow = false
                    // }, 2000)
                    Toast(this.$t("hj33"));
                    this.loginBtn = false;
                } else {
                    let opts = {
                        // agentCode:'4023', // SR330001
                        phone: this.phone,
                        yzmCode: "6666",
                        userPwd: this.userPassword,
                        agentCode: this.userName,
                    };
                    let data = await api.register(opts);
                    if (data.status === 0) {
                        // this.texts = this.$t('hj34')
                        // this.elType = "success"
                        // this.alertShow = true
                        Toast(this.$t("hj34"));
                        setTimeout(() => {
                            this.$router.push("/login");
                            // this.alertShow = false
                            // this.elType = "warning"
                        }, 1000);
                        this.loginBtn = false;
                    } else {
                        Toast(data.msg);
                        // this.texts = data.msg
                        // this.alertShow = true
                        // setTimeout(() => {
                        //   this.alertShow = false
                        // }, 2000)
                        this.loginBtn = false;
                    }
                }
            }
            if (navigator.vibrate) {
                // 支持
                navigator.vibrate([55]);
            }
            //   if (isNull(this.code)) {
            //     this.texts="请输入验证码"
            // this.alertShow=true
            // setTimeout(()=>{
            //   this.alertShow=false
            // },2000)
            //     this.loginBtn = false;
            //   } else
        },
        isAgree() {
            this.agree = !this.agree;
        },
        async checkPhone() {
            // 先验证是否已经注册
            let data = await api.checkPhone({ phoneNum: this.phone });
            if (data.status === 0) {
                // 如果用户已存在返回 0
                Toast(this.$t("hj35"));
                // this.texts = this.$t('hj35')
                //     this.alertShow = true
                //     setTimeout(() => {
                //       this.alertShow = false
                //     }, 2000)
                this.$router.push("/login");
            } else {
                // this.dialogShow = false
                // this.adminUrl = process.env.API_HOST
                // if (this.adminUrl === undefined) {
                //   this.adminUrl = ''
                // }
                // this.gook()
                this.getcode();
            }
        },
    },
};
</script>

<style lang="less" scoped>
.modern-register-container {
    min-height: 100vh;
    position: relative;
    overflow: hidden;

    // 背景系统
    .background-system {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 0;

        // 主渐变背景
        .main-gradient {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #F6F6F6;
        }

        // 渐变光晕
        .gradient-orbs {
            position: absolute;
            width: 100%;
            height: 100%;

            .orb {
                position: absolute;
                border-radius: 50%;
                filter: blur(40px);

                &.orb-1 {
                    width: 200px;
                    height: 200px;
                    background: rgba(59, 130, 246, 0.3);
                    top: 20%;
                    left: 10%;
                }

                &.orb-2 {
                    width: 150px;
                    height: 150px;
                    background: rgba(147, 197, 253, 0.25);
                    top: 60%;
                    right: 20%;
                }

                &.orb-3 {
                    width: 100px;
                    height: 100px;
                    background: rgba(219, 234, 254, 0.2);
                    top: 40%;
                    left: 60%;
                }
            }
        }
    }

    .header {
        width: 100%;
        height: 46px;
        position: relative;
        z-index: 10;

        /deep/ .van-nav-bar {
            background: transparent;

            .van-nav-bar__title {
                color: #333;
                font-weight: 700;
                font-size: 16px;
            }

            .van-icon {
                color: #333;
            }
        }
    }

    // 主要内容
    .content {
        position: relative;
        z-index: 1;
        min-height: calc(100vh - 46px);
            display: flex;
        flex-direction: column;
        justify-content: flex-start;

        // 品牌区域
        .brand-section {
            text-align: center;
            margin-bottom: -20px;
            padding: 40px 16px;
            background: url('~@/assets/newtemp/21.jpg') no-repeat center center;
            background-size: cover;
            position: relative;
            height: 200px;
            .logo-container {
                margin-bottom: 16px;

                .logo-box {
                    font-size: 28px;
                    font-weight: bolder;
                    letter-spacing: 0px;
                    line-height: 40.54px;
                    color: rgba(255, 255, 255, 1);

                }
            }

            .register-company-name {
                color: rgba(255, 255, 255, 1);
                font-size: 18px;
                font-weight: bolder;
                letter-spacing: 0px;
                line-height: 26.06px;
                text-shadow: 0 2px 20px rgba(0, 0, 0, 0.2);
                margin-bottom: 16px;
            }

            .brand-features {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 8px;

                .feature-item {
                    display: flex;
                    align-items: center;
                    color: rgba(255, 255, 255, 0.9);
                    font-size: 12px;
                    font-weight: 500;

                    .feature-icon {
                        margin-right: 4px;
                        width: 14px;
                        height: 14px;
                    }
                }

                .feature-separator {
                    color: rgba(255, 255, 255, 0.7);
                    margin: 0 6px;
                    font-weight: bold;
                }
            }
        }

        // 注册面板
        .register-form-panel {
            background: white;
            border-radius: 24px 24px 0 0;
            padding: 32px 24px 40px;
            box-shadow: 0 -10px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
            width: 100%;
                flex: 1;
            min-height: 400px;

            .form-content {
                .input-section {
                    margin-bottom: 24px;

                    .input-label {
                        color: #374151;
                        font-size: 16px;
                        font-weight: 600;
                        margin-bottom: 8px;
                    }

                    .input-wrapper {
                        position: relative;

                        .form-input {
                            width: 100%;
                            height: 52px;
                            border: 1px solid #e5e7eb;
                            border-radius: 12px;
                            padding: 0 16px;
                            font-size: 16px;
                            color: #374151;
                            background: #f9fafb;
                            transition: all 0.3s ease;
                            box-sizing: border-box;

                            &::placeholder {
                                color: #9ca3af;
                            }

                            &:focus {
                                outline: none;
                                border-color: #3b82f6;
                                background: white;
                                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
                            }
                        }
                    }
                }

                .simple-agreement-section {
                    margin-bottom: 24px;

        /deep/ .van-checkbox {
            .van-checkbox__label {
                            color: #6b7280;
                            font-size: 14px;

                            .simple-agreement-text {
                                .simple-agreement-link {
                                    color: #3b82f6;
                                    text-decoration: none;

                                    &:hover {
                                        text-decoration: underline;
                                    }
                                }
                            }
                        }
                    }
                }

                .simple-register-button {
                    width: 100%;
                    height: 45px;
                    background: #dc2626;
                    border: none;
                    border-radius: 26px;
                    color: white;
                    font-size: 16px;
            font-weight: 600;
                    cursor: pointer;
            position: relative;
            overflow: hidden;
                    transition: all 0.3s ease;
                    margin-bottom: 20px;

                    &:hover {
                        background: #b91c1c;
                        transform: translateY(-2px);
                        box-shadow: 0 8px 25px rgba(220, 38, 38, 0.3);
            }

            &:active {
                        transform: translateY(0);
                    }
                }

                .simple-login-redirect {
                    text-align: center;
                    position: relative;

                    .login-text {
                   
font-size: 14px;
font-weight: 400;
letter-spacing: 0px;
line-height: 20.27px;
color: rgba(0, 0, 0, 1);
/** 文本2 */


                    }

                    .login-link {
                        font-size: 14px;
font-weight: 400;
letter-spacing: 0px;
line-height: 20.27px;
color: rgba(37, 103, 255, 1);
                        text-decoration: none;
                        margin-left: 8px;

                        &:hover {
                            text-decoration: underline;
                        }
                    }
                }

                .service-highlights {
                    display: flex;
                    justify-content: space-between;
                    margin-top: 20px;

                    .highlight-item {
                        display: flex;
                        flex-direction: column;
                        align-items: center;

                        .highlight-icon {
                            width: 32px;
                            height: 32px;
                            margin-bottom: 8px;
                        }

                        .highlight-text {
                            text-align: center;

                            .highlight-title {
                             

/** 文本1 */
font-size: 14px;
font-weight: bold;
letter-spacing: 0px;
line-height: 20.27px;
color: rgba(0, 0, 0, 1);

                            }

                            .highlight-desc {
                            

/** 文本1 */
font-size: 11px;
font-weight: 400;
letter-spacing: 0px;
line-height: 15.93px;
color: rgba(117, 117, 117, 1);

                            }
                        }
                    }
                }
            }
        }
    }
}
</style>