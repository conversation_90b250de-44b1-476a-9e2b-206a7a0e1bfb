<template>
    <div class="container">
        <div class="header">
            <van-nav-bar title="招商证券账户服务协议" left-arrow @click-left="$router.go(-1)" fixed></van-nav-bar>
        </div>
        <div class="content">
            <div class="header-section">
                <div class="agreement-title">📋 用户协议</div>
                <div class="agreement-desc">请仔细阅读以下条款</div>
            </div>

            <div class="intro-card">
                <p>本协议是您(个人或单位)与我们之间关于股票交易软件的法律协议。一旦安装、复制或以其他方式使用本软件产品，即表示同意接受本协议各项条件的约束。</p>
                <div class="warning-tip">
                    <span class="warning-icon">⚠️</span>
                    <span>如果您不同意本协议的条件，则不能获得使用本软件产品的权力。</span>
                </div>
            </div>

            <div class="section-card">
                <div class="section-header">
                    <span class="section-num">一</span>
                    <span class="section-title">软件产品版权及保护声明</span>
                </div>
                <div class="section-content">
                    <div class="point-item">
                        <span class="point-num">1</span>
                        <p>未经授权或者许可，任何个人、单位不得以任何方式将本软件及注册码或注册文件对外发布、复制并传播，或用于商业目的的销售。授权用户或单位限在授权范围内使用。</p>
                    </div>
                    <div class="point-item">
                        <span class="point-num">2</span>
                        <p>任何个人或团体、单位不得试图破解或提供他人破解或反编译本软件，本软件受《著作权法》、《物权法》及《计算机软件保护条例》保护，违者将依法提起诉讼，追究其法律责任，并有权终止其使用权。
                        </p>
                    </div>
                    <div class="point-item">
                        <span class="point-num">3</span>
                        <p>不能保证软件没有任何的瑕疵，但是我们尽可能的减少或避免程序中的错误，对于已出现的错误及时更正，或对软件进行升级并对客户免费提供及时和必要的服务。</p>
                    </div>
                    <div class="point-item">
                        <span class="point-num">4</span>
                        <p>任何公司或个人定制的版本因自身原因或使用不当而造成的损失或纠纷自行承担。</p>
                    </div>
                    <div class="point-item">
                        <span class="point-num">5</span>
                        <p>如果您未遵守本协议的任何一项条款，我们有权立即终止本协议，并保留通过法律手段追究责任的权利。</p>
                    </div>
                </div>
            </div>

            <div class="section-card">
                <div class="section-header">
                    <span class="section-num">二</span>
                    <span class="section-title">软件免责声明</span>
                </div>
                <div class="section-content">
                    <p>我们敬请使用者仔细阅读以下内容，以便正确、全面地了解网上股票自动交易的风险。</p>
                    <div class="highlight-box">
                        <p>如果使用者购买或使用我们的股票交易软件，我们将认为使用者已完全了解网上股票交易的风险，并自愿承受由此带来的一切可能的损失。我们已对本软件尽力采取了有效措施保证软件的正确性和安全性。尽管如此，本着对使用者负责的态度，我们在此郑重提醒使用者，网上股票交易除具有普通的手工委托交易方式所共有的风险外，使用者还应充分了解和认识到其存在且不限于以下风险：
                        </p>
                    </div>

                    <div class="risk-list">
                        <div class="risk-item">
                            <span class="risk-num">1</span>
                            <p>互联网数据传输可能会出现中断、停顿、延迟、数据错误等情况而导致自动交易失败；</p>
                        </div>
                        <div class="risk-item">
                            <span class="risk-num">2</span>
                            <p>互联网上存在黑客恶意攻击的可能性，网络服务器可能会出现故障及其他不可预测的因素，导致自动交易失败；</p>
                        </div>
                        <div class="risk-item">
                            <span class="risk-num">3</span>
                            <p>使用者的网络终端设备及软件系统可能会受到非法攻击或病毒感染，导致自动交易失败；</p>
                        </div>
                        <div class="risk-item">
                            <span class="risk-num">4</span>
                            <p>使用者不熟悉本软件操作方法或缺乏网上委托经验，可能因操作不当造成自动交易失败或失误；</p>
                        </div>
                        <div class="risk-item">
                            <span class="risk-num">5</span>
                            <p>使用者所开户的证券公司的网络系统和交易系统出错，均会造成自动交易失败或失误；</p>
                        </div>
                        <div class="risk-item">
                            <span class="risk-num">6</span>
                            <p>互联网上的破解版很可能捆绑了木马或病毒，使用这些版本，可能会造成你账户密码被盗或资金损失，更有可能使自动交易失败或失误；</p>
                        </div>
                        <div class="risk-item">
                            <span class="risk-num">7</span>
                            <p>由于长时间离开电脑而造成账户密码或用户资料被他人窃取，从而使你账户资金损失，因此请不要在公共场合下使用本软件；</p>
                        </div>
                        <div class="risk-item">
                            <span class="risk-num">8</span>
                            <p>除以上原因外的其它可能造成自动交易失败的原因。</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="section-card">
                <div class="section-header">
                    <span class="section-num">三</span>
                    <span class="section-title">忠告建议</span>
                </div>
                <div class="section-content">
                    <div class="advice-box">
                        <div class="advice-icon">💡</div>
                        <div class="advice-text">股市有风险，入市需谨慎。</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style lang="less" scoped>
.container {
    font-size: 12px;
    padding: 0;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;

    .header {
        width: 100%;
        height: 46px;

        /deep/ .van-nav-bar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);

            .van-nav-bar__title {
                color: #333;
                font-weight: 600;
                font-size: 15px;
            }

            .van-icon {
                color: #333;
            }
        }
    }

    .content {
        padding: 12px;
        font-size: 12px;
        line-height: 1.5;
        padding-bottom: 30px;

        .header-section {
            margin-bottom: 20px;

            .agreement-title {
                font-size: 15px;
                font-weight: 600;
                color: white;
                padding: 8px 12px;
                background: linear-gradient(135deg, #ee0011 0%, #ff4757 100%);
                border-radius: 6px;
                box-shadow:
                    0 1px 6px rgba(238, 0, 17, 0.2),
                    0 1px 3px rgba(238, 0, 17, 0.1);
            }

            .agreement-desc {
                font-size: 12px;
                color: #333;
                background: rgba(255, 255, 255, 0.95);
                padding: 12px;
                border-radius: 6px;
                box-shadow:
                    0 1px 3px rgba(0, 0, 0, 0.05),
                    0 0 1px rgba(0, 0, 0, 0.08);
                backdrop-filter: blur(10px);
            }
        }

        .intro-card {
            margin-bottom: 20px;
            padding: 12px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 6px;
            box-shadow:
                0 1px 3px rgba(0, 0, 0, 0.05),
                0 0 1px rgba(0, 0, 0, 0.08);
            backdrop-filter: blur(10px);

            p {
                margin: 10px 0;
                color: #333;
                line-height: 1.6;
            }

            .warning-tip {
                margin-top: 10px;
                padding: 12px;
                background: rgba(255, 255, 255, 0.95);
                border-radius: 6px;
                box-shadow:
                    0 1px 3px rgba(0, 0, 0, 0.05),
                    0 0 1px rgba(0, 0, 0, 0.08);
                backdrop-filter: blur(10px);

                .warning-icon {
                    margin-right: 6px;
                }
            }
        }

        .section-card {
            margin-bottom: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 6px;
            box-shadow:
                0 1px 3px rgba(0, 0, 0, 0.05),
                0 0 1px rgba(0, 0, 0, 0.08);
            backdrop-filter: blur(10px);
            overflow: hidden;

            .section-header {
                display: flex;
                align-items: center;
                padding: 10px 12px;
                background: linear-gradient(135deg, #ee0011 0%, #ff4757 100%);
                color: white;

                .section-num {
                    width: 20px;
                    height: 20px;
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 11px;
                    font-weight: 600;
                    margin-right: 8px;
                }

                .section-title {
                    font-size: 13px;
                    font-weight: 600;
                }
            }

            .section-content {
                padding: 12px;

                p {
                    margin: 10px 0;
                    color: #333;
                    line-height: 1.6;
                    font-size: 12px;
                }

                .point-item {
                    display: flex;
                    align-items: flex-start;
                    margin-bottom: 10px;
                    padding: 8px;
                    background: #f8f9fa;
                    border-radius: 4px;
                    border-left: 2px solid #ee0011;

                    .point-num {
                        width: 16px;
                        height: 16px;
                        background: #ee0011;
                        color: white;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 10px;
                        font-weight: 600;
                        margin-right: 8px;
                        flex-shrink: 0;
                        margin-top: 2px;
                    }

                    p {
                        margin: 0;
                        flex: 1;
                    }
                }

                .highlight-box {
                    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
                    padding: 10px;
                    border-radius: 4px;
                    border-left: 3px solid #2196f3;
                    margin: 10px 0;

                    p {
                        margin: 0;
                        color: #1565c0;
                        font-weight: 500;
                    }
                }

                .risk-list {
                    .risk-item {
                        display: flex;
                        align-items: flex-start;
                        margin-bottom: 8px;
                        padding: 8px;
                        background: #fff5f5;
                        border-radius: 4px;
                        border-left: 2px solid #f56565;

                        .risk-num {
                            width: 16px;
                            height: 16px;
                            background: #f56565;
                            color: white;
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-size: 10px;
                            font-weight: 600;
                            margin-right: 8px;
                            flex-shrink: 0;
                            margin-top: 2px;
                        }

                        p {
                            margin: 0;
                            flex: 1;
                        }
                    }
                }

                .advice-box {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
                    padding: 16px;
                    border-radius: 6px;
                    border-left: 3px solid #4caf50;

                    .advice-icon {
                        font-size: 20px;
                        margin-right: 8px;
                    }

                    .advice-text {
                        font-size: 14px;
                        color: #2e7d32;
                        font-weight: 600;
                    }
                }
            }
        }
    }

    // 滚动条美化
    ::-webkit-scrollbar {
        width: 4px;
    }

    ::-webkit-scrollbar-track {
        background: rgba(241, 241, 241, 0.3);
        border-radius: 2px;
    }

    ::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, #ee0011, #ff4757);
        border-radius: 2px;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, #d00010, #e63946);
    }
}
</style>
