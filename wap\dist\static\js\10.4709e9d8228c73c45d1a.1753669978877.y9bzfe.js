/* auto-build-1753669979406-rdxg2j */
webpackJsonp([10],{TruT:function(t,e,s){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var a=s("Gu7T"),i=s.n(a),r=s("Xxa5"),n=s.n(r),c=s("exGp"),o=s.n(c),l=s("mvHQ"),d=s.n(l),u=s("c2Ch"),v=s("Au9i"),_=s("oqQY"),m=s.n(_),f={name:"TradingPage",components:{StockTagInfo:s("ICZX").a},data:function(){return{type:1,itemIndex:1,list:[],pageNum:1,finished:!1,loading:!1,currentRequestId:0}},computed:{currentTabTitle:function(){return["我的持仓","交易记录","我的委托"][this.itemIndex]||""},hasData:function(){return this.list&&this.list.length>0},listTitleConfig:function(){return{0:["名称","持仓 | 市值","现价 | 成本","盈亏 | 涨幅"],1:["股票 | 代码","本金 | 数量","买入 | 卖出价","收益 | 涨幅"],2:[]}[this.itemIndex]||[]}},mounted:function(){this.initializePage()},beforeDestroy:function(){this.currentRequestId=0},methods:{initializePage:function(){try{this.$route.query.type&&(this.type=parseInt(this.$route.query.type)||1),this.changeItemIndex(this.type-1)}catch(t){console.error("页面初始化失败:",t),this.changeItemIndex(0)}},parseNumber:function(t){return null===t||void 0===t||""===t?"0.00":parseFloat(t).toFixed(2)},formatTime:function(t){return t?m()(t).format("YYYY-MM-DD HH:mm:ss"):""},getProfitStatus:function(t){return t>0?"profit":t<0?"loss":"neutral"},chicangDetail:function(t){try{this.$router.push({path:"/chicangDetail",query:{type:"dazong",item:d()(t)}})}catch(t){console.error("跳转持仓详情失败:",t),Object(v.Toast)("页面跳转失败")}},changeItemIndex:function(t){var e=this;return o()(n.a.mark(function s(){return n.a.wrap(function(s){for(;;)switch(s.prev=s.next){case 0:return s.prev=0,e.resetListState(),e.itemIndex=t,e.currentRequestId=Date.now(),s.next=6,e.loadInitialData();case 6:s.next=12;break;case 8:s.prev=8,s.t0=s.catch(0),console.error("切换标签页失败:",s.t0),e.handleLoadError(s.t0);case 12:case"end":return s.stop()}},s,e,[[0,8]])}))()},resetListState:function(){this.list=[],this.pageNum=1,this.finished=!1,this.loading=!1},loadInitialData:function(){var t=this;return o()(n.a.mark(function e(){return n.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.getOrderList(t.itemIndex);case 2:case"end":return e.stop()}},e,t)}))()},getOrderList:function(t){var e=this;return o()(n.a.mark(function s(){return n.a.wrap(function(s){for(;;)switch(s.prev=s.next){case 0:if(!e.loading&&!e.finished){s.next=2;break}return s.abrupt("return");case 2:return s.prev=2,s.next=5,e.fetchOrderData(t);case 5:s.next=10;break;case 7:s.prev=7,s.t0=s.catch(2),e.handleLoadError(s.t0);case 10:case"end":return s.stop()}},s,e,[[2,7]])}))()},fetchOrderData:function(t){var e=this;return o()(n.a.mark(function s(){var a,i,r;return n.a.wrap(function(s){for(;;)switch(s.prev=s.next){case 0:return e.loading=!0,a=e.currentRequestId,i={state:t,stockCode:"",stockSpell:"",pageNum:e.pageNum,pageSize:15},s.prev=3,s.next=6,u.U(i);case 6:if(r=s.sent,a===e.currentRequestId){s.next=9;break}return s.abrupt("return");case 9:e.handleOrderDataSuccess(r),s.next=17;break;case 12:if(s.prev=12,s.t0=s.catch(3),a===e.currentRequestId){s.next=16;break}return s.abrupt("return");case 16:throw s.t0;case 17:return s.prev=17,e.loading=!1,s.finish(17);case 20:case"end":return s.stop()}},s,e,[[3,12,17,20]])}))()},handleOrderDataSuccess:function(t){var e;if(!t||!t.data||!t.data.list)return console.warn("订单数据格式异常:",t),void(this.finished=!0);var s=t.data.list;s.length<15&&(this.finished=!0),(e=this.list).push.apply(e,i()(s)),this.pageNum++},handleLoadError:function(t){if(console.error("加载数据失败:",t),this.loading=!1,t.response)switch(t.response.status){case 401:Object(v.Toast)("登录已过期，请重新登录");break;case 403:Object(v.Toast)("没有访问权限");break;case 500:Object(v.Toast)("服务器异常，请稍后重试");break;default:Object(v.Toast)("网络请求失败")}else Object(v.Toast)(t.message||"加载失败，请重试")},withdrawOrder:function(t){var e=this;return o()(n.a.mark(function s(){return n.a.wrap(function(s){for(;;)switch(s.prev=s.next){case 0:if(0===t.backStatus){s.next=3;break}return Object(v.Toast)("当前订单状态不允许撤单"),s.abrupt("return");case 3:return s.prev=3,s.next=6,e.confirmWithdrawOrder(t);case 6:s.next=11;break;case 8:s.prev=8,s.t0=s.catch(3),console.error("撤单操作失败:",s.t0);case 11:case"end":return s.stop()}},s,e,[[3,8]])}))()},confirmWithdrawOrder:function(t){var e=this;return o()(n.a.mark(function s(){return n.a.wrap(function(s){for(;;)switch(s.prev=s.next){case 0:return s.prev=0,s.next=3,v.MessageBox.confirm("确定撤单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});case 3:return s.next=5,e.executeWithdrawOrder(t);case 5:s.next=14;break;case 7:if(s.prev=7,s.t0=s.catch(0),"cancel"!==s.t0){s.next=13;break}console.log("用户取消撤单"),s.next=14;break;case 13:throw s.t0;case 14:case"end":return s.stop()}},s,e,[[0,7]])}))()},executeWithdrawOrder:function(t){var e=this;return o()(n.a.mark(function s(){var a,i;return n.a.wrap(function(s){for(;;)switch(s.prev=s.next){case 0:return a={id:t.id,userId:t.userId},s.prev=1,s.next=4,u.t(a);case 4:i=s.sent,e.handleWithdrawResult(i),s.next=12;break;case 8:throw s.prev=8,s.t0=s.catch(1),Object(v.Toast)("撤单失败，请重试"),s.t0;case 12:case"end":return s.stop()}},s,e,[[1,8]])}))()},handleWithdrawResult:function(t){0===t.status?(Object(v.Toast)({message:t.data||"撤单成功",type:"success"}),this.refreshCurrentTab()):Object(v.Toast)({message:t.data||t.msg||"撤单失败",type:"error"})},refreshCurrentTab:function(){this.resetListState(),this.loadInitialData()}}},h={render:function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"trading-page"},[s("div",{staticClass:"trading-page__header"},[s("van-nav-bar",{attrs:{title:"我的交易","left-arrow":"",fixed:""},on:{"click-left":function(e){return t.$router.go(-1)}}})],1),t._v(" "),s("div",{staticClass:"trading-page__tabs"},[s("div",{class:["tab-item",{"tab-item--active":0===t.itemIndex}],on:{click:function(e){return t.changeItemIndex(0)}}},[t._m(0)]),t._v(" "),s("div",{class:["tab-item",{"tab-item--active":1===t.itemIndex}],on:{click:function(e){return t.changeItemIndex(1)}}},[t._m(1)]),t._v(" "),s("div",{class:["tab-item",{"tab-item--active":2===t.itemIndex}],on:{click:function(e){return t.changeItemIndex(2)}}},[t._m(2)])]),t._v(" "),0===t.itemIndex?s("div",{staticClass:"trading-list"},[t._m(3),t._v(" "),s("div",{staticClass:"trading-list__container"},[s("van-list",{attrs:{finished:t.finished,"immediate-check":!1,"finished-text":t.$t("hj43")},on:{load:function(e){return t.getOrderList(0)}},model:{value:t.loading,callback:function(e){t.loading=e},expression:"loading"}},t._l(t.list,function(e){return s("div",{key:e.id,staticClass:"trading-item",on:{click:function(s){return t.chicangDetail(e)}}},[s("div",{staticClass:"trading-item__stock"},[s("div",{staticClass:"stock-info"},[s("div",{staticClass:"stock-info__name"},[t._v(t._s(e.stockName))]),t._v(" "),s("div",{staticClass:"stock-info__code"},[s("stock-tag-info",{attrs:{stockCode:e.stockGid}})],1)])]),t._v(" "),s("div",{staticClass:"trading-item__data"},[s("span",{staticClass:"data-primary"},[t._v(t._s(t.parseNumber(e.buyNum)))]),t._v(" "),s("span",{staticClass:"data-secondary"},[t._v(t._s(t.parseNumber(e.buyPrice)))])]),t._v(" "),s("div",{staticClass:"trading-item__data"},[s("span",{staticClass:"data-primary"},[t._v(t._s(t.parseNumber(e.now_price)))]),t._v(" "),s("span",{staticClass:"data-secondary"},[t._v(t._s(t.parseNumber(e.buyOrderPrice)))])]),t._v(" "),s("div",{class:["trading-item__data","trading-item__data--profit",{"trading-item__data--loss":e.profitAndLossRatio<=0}]},[s("span",{staticClass:"data-primary"},[t._v(t._s(t.parseNumber(e.profitAndLose)))]),t._v(" "),s("span",{staticClass:"data-secondary"},[t._v(t._s(t.parseNumber(e.profitAndLossRatio))+"%")])])])}),0)],1)]):t._e(),t._v(" "),1===t.itemIndex?s("div",{staticClass:"trading-list"},[t._m(4),t._v(" "),s("div",{staticClass:"trading-list__container"},[s("van-list",{attrs:{finished:t.finished,"immediate-check":!1,"finished-text":t.$t("hj43")},on:{load:function(e){return t.getOrderList(1)}},model:{value:t.loading,callback:function(e){t.loading=e},expression:"loading"}},t._l(t.list,function(e){return s("div",{key:e.id,staticClass:"trading-record",on:{click:function(s){return t.chicangDetail(e)}}},[s("div",{staticClass:"trading-item"},[s("div",{staticClass:"trading-item__stock"},[s("div",{staticClass:"stock-info"},[s("div",{staticClass:"stock-info__name"},[t._v(t._s(e.stockName))]),t._v(" "),s("div",{staticClass:"stock-info__code"},[s("stock-tag-info",{attrs:{stockCode:e.stockGid}})],1)])]),t._v(" "),s("div",{staticClass:"trading-item__data"},[s("span",{staticClass:"data-primary"},[t._v(t._s(t.parseNumber(e.buyPrice)))]),t._v(" "),s("span",{staticClass:"data-secondary"},[t._v(t._s(t.parseNumber(e.buyNum)))])]),t._v(" "),s("div",{staticClass:"trading-item__data"},[s("span",{staticClass:"data-primary"},[t._v(t._s(t.parseNumber(e.buyOrderPrice)))]),t._v(" "),s("span",{staticClass:"data-secondary"},[t._v(t._s(t.parseNumber(e.sellOrderPrice)))])]),t._v(" "),s("div",{class:["trading-item__data","trading-item__data--profit",{"trading-item__data--loss":e.profitAndLossRatio<=0}]},[s("span",{staticClass:"data-primary"},[t._v(t._s(t.parseNumber(e.profitAndLose)))]),t._v(" "),s("span",{staticClass:"data-secondary"},[t._v(t._s(t.parseNumber(e.profitAndLossRatio))+"%")])])]),t._v(" "),s("div",{staticClass:"trading-record__time"},[s("div",{staticClass:"time-item"},[t._v(t._s(t.formatTime(e.buyOrderTime)))]),t._v(" "),s("div",{staticClass:"time-item"},[t._v(t._s(t.formatTime(e.sellOrderTime)))])]),t._v(" "),s("div",{staticClass:"trading-record__action"},[s("button",{staticClass:"action-button"},[t._v("查看详情")])])])}),0)],1)]):t._e(),t._v(" "),2===t.itemIndex?s("div",{staticClass:"delegation-list"},[s("van-list",{attrs:{finished:t.finished,"immediate-check":!1,"finished-text":t.$t("hj43")},on:{load:function(e){return t.getOrderList(2)}},model:{value:t.loading,callback:function(e){t.loading=e},expression:"loading"}},t._l(t.list,function(e){return s("div",{key:e.id,staticClass:"delegation-item"},[s("div",{staticClass:"delegation-item__header"},[s("div",{staticClass:"stock-info"},[s("div",{staticClass:"stock-info__name"},[t._v(t._s(e.stockName))]),t._v(" "),s("div",{staticClass:"stock-info__code"},[s("stock-tag-info",{attrs:{stockCode:e.stockGid}})],1)]),t._v(" "),s("div",{staticClass:"delegation-item__action"},[s("button",{staticClass:"withdrawal-button",attrs:{disabled:0!==e.backStatus},on:{click:function(s){return t.withdrawOrder(e)}}},[t._v("\n                            撤单\n                        ")])])]),t._v(" "),s("div",{staticClass:"delegation-item__info"},[s("div",{staticClass:"info-row"},[s("div",{staticClass:"info-item"},[s("div",{staticClass:"info-item__label"},[t._v("买卖类别")]),t._v(" "),s("div",{staticClass:"info-item__value"},[t._v("证券买入")])]),t._v(" "),s("div",{staticClass:"info-item"},[s("div",{staticClass:"info-item__label"},[t._v("当前状态")]),t._v(" "),s("div",{staticClass:"info-item__value"},[t._v("挂单")])])]),t._v(" "),s("div",{staticClass:"info-row"},[s("div",{staticClass:"info-item"},[s("div",{staticClass:"info-item__label"},[t._v("委托手数")]),t._v(" "),s("div",{staticClass:"info-item__value"},[t._v(t._s(e.orderNum/100))])]),t._v(" "),s("div",{staticClass:"info-item"},[s("div",{staticClass:"info-item__label"},[t._v("委托价格")]),t._v(" "),s("div",{staticClass:"info-item__value"},[t._v(t._s(e.buyOrderPrice))])])])])])}),0)],1):t._e()])},staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"tab-item__content"},[e("span",{staticClass:"tab-item__text"},[this._v("我的持仓")]),this._v(" "),e("span",{staticClass:"tab-item__indicator"})])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"tab-item__content"},[e("span",{staticClass:"tab-item__text"},[this._v("交易记录")]),this._v(" "),e("span",{staticClass:"tab-item__indicator"})])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"tab-item__content"},[e("span",{staticClass:"tab-item__text"},[this._v("我的委托")]),this._v(" "),e("span",{staticClass:"tab-item__indicator"})])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"trading-list__header"},[e("div",{staticClass:"header-item"},[this._v("名称")]),this._v(" "),e("div",{staticClass:"header-item"},[this._v("持仓 | 市值")]),this._v(" "),e("div",{staticClass:"header-item"},[this._v("现价 | 成本")]),this._v(" "),e("div",{staticClass:"header-item"},[this._v("盈亏 | 涨幅")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"trading-list__header"},[e("div",{staticClass:"header-item"},[this._v("股票 | 代码")]),this._v(" "),e("div",{staticClass:"header-item"},[this._v("本金 | 数量")]),this._v(" "),e("div",{staticClass:"header-item"},[this._v("买入 | 卖出价")]),this._v(" "),e("div",{staticClass:"header-item"},[this._v("收益 | 涨幅")])])}]};var p=s("VU/8")(f,h,!1,function(t){s("WKBe")},"data-v-649f2c15",null);e.default=p.exports},UXuT:function(t,e,s){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var a=s("Xxa5"),i=s.n(a),r=s("exGp"),n=s.n(r),c=s("c2Ch"),o=s("Au9i"),l=s("ICZX"),d=s("oqQY"),u=s.n(d),v={name:"PositionDetailPage",components:{StockTagInfo:l.a},data:function(){return{currentItem:{},loading:!1}},computed:{profitStatusClass:function(){return this.currentItem.profitAndLossRatio?this.currentItem.profitAndLossRatio>0?"profit":this.currentItem.profitAndLossRatio<0?"loss":"neutral":""},isSold:function(){return!!this.currentItem.sellOrderId}},mounted:function(){this.initializeData()},methods:{initializeData:function(){try{var t=this.$route.query.item;if(!t)throw new Error("缺少持仓数据");if(this.currentItem=JSON.parse(decodeURIComponent(t)),console.log("持仓详情数据:",this.currentItem),!this.currentItem.stockCode)throw new Error("持仓数据不完整")}catch(t){console.error("数据初始化失败:",t),this.$toast("数据加载失败"),this.goBack()}},formatNumber:function(t){return t||0===t?parseFloat(t).toFixed(2):"0.00"},formatTime:function(t){return t?u()(t).format("YYYY-MM-DD HH:mm:ss"):"--"},goBack:function(){this.$router.go(-1)},confirmSell:function(){var t=this;this.isSold?this.$toast("该持仓已卖出"):this.loading||o.MessageBox.confirm(this.$t("hj139")+"?",this.$t("hj165"),{confirmButtonText:this.$t("hj161"),cancelButtonText:this.$t("hj106")}).then(function(){t.executeSell()}).catch(function(t){console.log("用户取消卖出:",t)})},executeSell:function(){var t=this;return n()(i.a.mark(function e(){var s,a;return i.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t.loading=!0,e.prev=1,(s={positionSn:t.currentItem.positionSn}).positionSn){e.next=5;break}throw new Error("持仓编号不存在");case 5:return e.next=7,c._31(s);case 7:a=e.sent,t.handleSellResponse(a),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(1),t.handleSellError(e.t0);case 14:return e.prev=14,t.loading=!1,e.finish(14);case 17:case"end":return e.stop()}},e,t,[[1,11,14,17]])}))()},handleSellResponse:function(t){var e=this;if(!t)throw new Error("服务器无响应");0===t.status?(Object(o.Toast)(t.msg||"卖出成功"),setTimeout(function(){e.goBack()},1e3)):this.handleSellBusinessError(t)},handleSellBusinessError:function(t){var e=t.msg||"卖出失败";e.indexOf("不在交易时段内")>-1?Object(o.Toast)(this.$t("hj140")):Object(o.Toast)(e)},handleSellError:function(t){if(console.error("卖出操作失败:",t),t.response)switch(t.response.status){case 401:Object(o.Toast)("登录已过期，请重新登录");break;case 403:Object(o.Toast)("没有操作权限");break;case 500:Object(o.Toast)("服务器异常，请稍后重试");break;default:Object(o.Toast)("网络请求失败")}else Object(o.Toast)(t.message||"操作失败，请重试")}}},_={render:function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"position-detail-page"},[s("div",{staticClass:"header"},[s("van-nav-bar",{attrs:{title:"持仓详情","left-arrow":"",fixed:""},on:{"click-left":function(e){return t.$router.go(-1)}}})],1),t._v(" "),s("div",{staticClass:"stock-card"},[s("div",{staticClass:"stock-info"},[s("div",{staticClass:"stock-name"},[t._v(t._s(t.currentItem.stockName))]),t._v(" "),s("stock-tag-info",{attrs:{"stock-code":t.currentItem.stockGid,"show-code":""}})],1),t._v(" "),s("div",{staticClass:"profit-info",class:t.profitStatusClass},[s("div",{staticClass:"profit-amount"},[t._v(t._s(t.formatNumber(t.currentItem.profitAndLose)))]),t._v(" "),s("div",{staticClass:"profit-ratio"},[t._v(t._s(t.formatNumber(t.currentItem.profitAndLossRatio))+"%")])])]),t._v(" "),s("div",{staticClass:"detail-list"},[s("div",{staticClass:"detail-item"},[s("span",{staticClass:"label"},[t._v("股票代码")]),t._v(" "),s("span",{staticClass:"value"},[t._v(t._s(t.currentItem.stockCode))])]),t._v(" "),s("div",{staticClass:"detail-item"},[s("span",{staticClass:"label"},[t._v("持股数")]),t._v(" "),s("span",{staticClass:"value"},[t._v(t._s(t.currentItem.buyNum)+" 股")])]),t._v(" "),s("div",{staticClass:"detail-item"},[s("span",{staticClass:"label"},[t._v("买入价格")]),t._v(" "),s("span",{staticClass:"value"},[t._v("¥"+t._s(t.formatNumber(t.currentItem.buyOrderPrice)))])]),t._v(" "),t.currentItem.sellOrderId?s("div",{staticClass:"detail-item"},[s("span",{staticClass:"label"},[t._v("卖出价格")]),t._v(" "),s("span",{staticClass:"value"},[t._v("¥"+t._s(t.formatNumber(t.currentItem.sellOrderPrice)))])]):t._e(),t._v(" "),s("div",{staticClass:"detail-item"},[s("span",{staticClass:"label"},[t._v("买入市值")]),t._v(" "),s("span",{staticClass:"value"},[t._v("¥"+t._s(t.formatNumber(t.currentItem.buyPrice)))])]),t._v(" "),s("div",{staticClass:"detail-item"},[s("span",{staticClass:"label"},[t._v("手续费")]),t._v(" "),s("span",{staticClass:"value"},[t._v("¥"+t._s(t.formatNumber(t.currentItem.orderFee)))])]),t._v(" "),t.currentItem.sellOrderId?s("div",{staticClass:"detail-item"},[s("span",{staticClass:"label"},[t._v("印花税")]),t._v(" "),s("span",{staticClass:"value"},[t._v("¥"+t._s(t.formatNumber(t.currentItem.orderSpread)))])]):t._e(),t._v(" "),s("div",{staticClass:"detail-item"},[s("span",{staticClass:"label"},[t._v("买入时间")]),t._v(" "),s("span",{staticClass:"value"},[t._v(t._s(t.formatTime(t.currentItem.buyOrderTime)))])]),t._v(" "),t.currentItem.sellOrderId?s("div",{staticClass:"detail-item"},[s("span",{staticClass:"label"},[t._v("卖出时间")]),t._v(" "),s("span",{staticClass:"value"},[t._v(t._s(t.formatTime(t.currentItem.sellOrderTime)))])]):t._e()]),t._v(" "),t.currentItem.sellOrderId?s("div",{staticClass:"action-btn",on:{click:t.goBack}},[t._v("\n        返回\n    ")]):s("div",{staticClass:"action-btn sell-btn",on:{click:t.confirmSell}},[t._v("\n        我要平仓\n    ")])])},staticRenderFns:[]};var m=s("VU/8")(v,_,!1,function(t){s("X0/c")},"data-v-e9430250",null);e.default=m.exports},WKBe:function(t,e){},"X0/c":function(t,e){}});
/* auto-build-1753669979406-rdxg2j */