<template>
    <div class="bank-card-update-page">
        <div class="bank-card-update-page__header">
            <van-nav-bar :title="pageTitle" left-arrow fixed @click-left="$router.go(-1)" />
        </div>

        <div class="bank-card-update-page__content" v-if="!isLoading">
            <!-- 页面说明 -->
            <div class="info-card">
                <div class="info-card__icon">
                    <van-icon name="exchange" size="1.2rem" />
                </div>
                <div class="info-card__content">
                    <h3 class="info-card__title">更换银行卡</h3>
                    <p class="info-card__description">请修改需要更换的银行卡信息，确保信息准确无误</p>
                </div>
            </div>

            <!-- 原银行卡信息对比 -->
            <div class="compare-section" v-if="isDataLoaded">
                <div class="compare-section__header">
                    <van-icon name="info-o" size="0.4rem" />
                    <span>当前银行卡信息</span>
                </div>
                <div class="original-card">
                    <div class="original-card__item">
                        <span class="original-card__label">银行名称：</span>
                        <span class="original-card__value">{{ originalData.bankName || '未设置' }}</span>
                    </div>
                    <div class="original-card__item">
                        <span class="original-card__label">开户支行：</span>
                        <span class="original-card__value">{{ originalData.bankAddress || '未设置' }}</span>
                    </div>
                    <div class="original-card__item">
                        <span class="original-card__label">银行卡号：</span>
                        <span class="original-card__value">{{ formattedBankNo || '未设置' }}</span>
                    </div>
                </div>
            </div>

            <!-- 表单区域 -->
            <div class="form-section">
                <div class="form-section__header">
                    <h3 class="form-section__title">新银行卡信息</h3>
                    <button class="reset-button" @click="resetForm" v-if="isDataLoaded && hasDataChanged">
                        重置
                    </button>
                </div>

                <div class="form-item">
                    <label class="form-item__label">
                        <van-icon name="shop-o" class="form-item__icon" />
                        银行名称
                        <span class="form-item__required">*</span>
                    </label>
                    <div class="form-item__input">
                        <input type="text" v-model="formData.bankName" placeholder="请输入银行名称" class="input-field" :class="{
                            'input-field--error': !formData.bankName.trim() && isSubmitting,
                            'input-field--changed': isDataLoaded && formData.bankName !== originalData.bankName
                        }" />
                    </div>
                </div>

                <div class="form-item">
                    <label class="form-item__label">
                        <van-icon name="location-o" class="form-item__icon" />
                        开户支行
                        <span class="form-item__required">*</span>
                    </label>
                    <div class="form-item__input">
                        <input type="text" v-model="formData.bankAddress" placeholder="请输入开户支行" class="input-field"
                            :class="{
                                'input-field--error': !formData.bankAddress.trim() && isSubmitting,
                                'input-field--changed': isDataLoaded && formData.bankAddress !== originalData.bankAddress
                            }" />
                    </div>
                </div>

                <div class="form-item">
                    <label class="form-item__label">
                        <van-icon name="credit-pay" class="form-item__icon" />
                        银行卡号
                        <span class="form-item__required">*</span>
                    </label>
                    <div class="form-item__input">
                        <input type="text" placeholder="请输入银行卡号" v-model="formData.bankNo" @input="formatBankCardInput"
                            class="input-field" :class="{
                                'input-field--error': !formData.bankNo.trim() && isSubmitting,
                                'input-field--changed': isDataLoaded && formData.bankNo !== originalData.bankNo
                            }" maxlength="23" />
                    </div>
                </div>
            </div>

            <!-- 变更提示 -->
            <div class="change-notice" v-if="hasDataChanged">
                <div class="change-notice__header">
                    <van-icon name="warning-o" size="0.4rem" />
                    <span>变更提示</span>
                </div>
                <div class="change-notice__content">
                    <p>检测到以下信息发生变更：</p>
                    <ul class="change-list">
                        <li v-if="formData.bankName !== originalData.bankName" class="change-list__item">
                            银行名称：{{ originalData.bankName }} → {{ formData.bankName }}
                        </li>
                        <li v-if="formData.bankAddress !== originalData.bankAddress" class="change-list__item">
                            开户支行：{{ originalData.bankAddress }} → {{ formData.bankAddress }}
                        </li>
                        <li v-if="formData.bankNo !== originalData.bankNo" class="change-list__item">
                            银行卡号：**** → ****
                        </li>
                    </ul>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="action-section">
                <button class="action-button action-button--primary" :class="{
                    'action-button--loading': isSubmitting,
                    'action-button--disabled': !isFormValid || !hasDataChanged || isSubmitting
                }" @click="handleSubmit" :disabled="!isFormValid || !hasDataChanged || isSubmitting">
                    <van-loading size="0.4rem" v-if="isSubmitting" />
                    <span>{{ buttonText }}</span>
                </button>
            </div>

            <!-- 安全提示 -->
            <div class="security-tips">
                <div class="security-tips__header">
                    <van-icon name="shield-o" size="0.4rem" />
                    <span>安全提示</span>
                </div>
                <ul class="security-tips__list">
                    <li class="security-tips__item">更换银行卡后，原银行卡将无法继续使用</li>
                    <li class="security-tips__item">请确保新银行卡为本人实名认证</li>
                    <li class="security-tips__item">更换后的银行卡必须支持在线支付功能</li>
                    <li class="security-tips__item">请仔细核对新银行卡号，避免输入错误</li>
                </ul>
            </div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-overlay" v-if="isLoading">
            <van-loading size="1rem" text-size="0.28rem">正在加载银行卡信息...</van-loading>
        </div>
    </div>
</template>

<script>
import * as api from '@/axios/api'
import { Toast } from 'mint-ui'
import { isNull, bankNoReg, isName } from '@/utils/utils'

export default {
    name: 'BankCardUpdatePage',
    data() {
        return {
            formData: {
                bankName: '', // 银行名称
                bankAddress: '', // 开户支行
                bankNo: '', // 银行卡号
            },
            originalData: {
                bankName: '',
                bankAddress: '',
                bankNo: '',
            },
            isLoading: false, // 页面加载状态
            isSubmitting: false, // 提交状态
            isDataLoaded: false, // 数据是否已加载
        }
    },
    computed: {
        // 页面标题
        pageTitle() {
            return '更换银行卡';
        },

        // 按钮文字
        buttonText() {
            if (this.isSubmitting) {
                return '更新中...';
            }
            return '确认更换';
        },

        // 表单是否有效
        isFormValid() {
            const { bankName, bankAddress, bankNo } = this.formData;
            const isValid = bankName.trim() && bankAddress.trim() && bankNo.trim();
            console.log('表单有效性检查:', {
                bankName: bankName.trim(),
                bankAddress: bankAddress.trim(),
                bankNo: bankNo.trim(),
                isValid
            });
            return isValid;
        },

        // 数据是否有变更
        hasDataChanged() {
            if (!this.isDataLoaded) {
                console.log('数据未加载，hasDataChanged返回false');
                return false;
            }

            const current = this.getCleanedFormData();
            const original = {
                bankName: String(this.originalData.bankName).replace(/\s+/g, ''),
                bankAddress: String(this.originalData.bankAddress).replace(/\s+/g, ''),
                bankNo: String(this.originalData.bankNo).replace(/\s+/g, ''),
            };

            const hasChanged = current.bankName !== original.bankName ||
                current.bankAddress !== original.bankAddress ||
                current.bankNo !== original.bankNo;

            console.log('数据变更检查:', {
                current,
                original,
                hasChanged,
                isDataLoaded: this.isDataLoaded
            });

            return hasChanged;
        },

        // 格式化银行卡号显示
        formattedBankNo() {
            if (!this.formData.bankNo) return '';
            // 隐藏中间数字，只显示前4位和后4位
            const bankNo = this.formData.bankNo;
            if (bankNo.length <= 8) return bankNo;
            return `${bankNo.substring(0, 4)} **** **** ${bankNo.substring(bankNo.length - 4)}`;
        },

        // 兼容旧属性名
        addBank() {
            return true; // 更换页面始终允许编辑
        },
        bankName: {
            get() {
                return this.formData.bankName;
            },
            set(value) {
                this.formData.bankName = value;
            }
        },
        bankAddress: {
            get() {
                return this.formData.bankAddress;
            },
            set(value) {
                this.formData.bankAddress = value;
            }
        },
        bankNo: {
            get() {
                return this.formData.bankNo;
            },
            set(value) {
                this.formData.bankNo = value;
            }
        }
    },
    async mounted() {
        await this.initializePage();
    },
    methods: {
        // 初始化页面
        async initializePage() {
            try {
                this.isLoading = true;
                await this.loadBankCardInfo();
            } catch (error) {
                console.error('页面初始化失败:', error);
                this.handleError(error, '页面加载失败');
            } finally {
                this.isLoading = false;
            }
        },

        // 加载银行卡信息
        async loadBankCardInfo() {
            try {
                const response = await api.getBankCard();
                this.handleBankCardResponse(response);
            } catch (error) {
                console.error('获取银行卡信息失败:', error);
                this.handleError(error, '获取银行卡信息失败');
                throw error;
            }
        },

        // 处理银行卡信息响应
        handleBankCardResponse(response) {
            if (!response) {
                throw new Error('服务器无响应');
            }

            if (response.status === 0 && response.data) {
                // 有银行卡信息，填充表单
                const { bankAddress, bankName, bankNo } = response.data;
                this.formData = {
                    bankAddress: bankAddress || '',
                    bankName: bankName || '',
                    bankNo: bankNo || '',
                };

                // 保存原始数据用于比较
                this.originalData = {
                    bankAddress: bankAddress || '',
                    bankName: bankName || '',
                    bankNo: bankNo || '',
                };

                this.isDataLoaded = true;
            } else {
                // 没有银行卡信息
                Toast('未找到银行卡信息，请先绑定银行卡');
                this.navigateToBindCard();
            }
        },

        // 处理表单提交
        async handleSubmit() {
            if (this.isSubmitting) {
                return;
            }

            // 检查数据是否有变更
            if (!this.hasDataChanged) {
                Toast('数据未发生变更');
                return;
            }

            await this.submitBankCardUpdate();
        },

        // 提交银行卡更新
        async submitBankCardUpdate() {
            try {
                // 表单验证
                if (!this.validateForm()) {
                    return;
                }

                this.isSubmitting = true;

                const cleanedData = this.getCleanedFormData();
                const response = await api.updateBankCard(cleanedData);

                this.handleSubmitResponse(response);

            } catch (error) {
                this.handleError(error, '更新失败');
            } finally {
                this.isSubmitting = false;
            }
        },

        // 表单验证
        validateForm() {
            const cleanedData = this.getCleanedFormData();

            // 添加调试信息
            console.log('验证表单数据:', cleanedData);
            console.log('原始表单数据:', this.formData);

            // 验证银行名称 - 允许中文、英文、数字和常见符号
            if (!cleanedData.bankName || cleanedData.bankName.trim() === '') {
                console.log('银行名称验证失败:', cleanedData.bankName);
                Toast(this.$t('hj218') || '请输入正确的银行名称');
                return false;
            }

            // 银行名称格式验证：允许中文、英文、数字、括号、点等常见字符
            const bankNameReg = /^[\u4E00-\u9FA5a-zA-Z0-9（）()·\.\-\s]{2,30}$/;
            if (!bankNameReg.test(cleanedData.bankName)) {
                console.log('银行名称格式验证失败:', cleanedData.bankName);
                Toast('银行名称格式不正确，请输入2-30位中文、英文或数字');
                return false;
            }

            // 验证开户支行 - 允许中文、英文、数字和常见符号
            if (!cleanedData.bankAddress || cleanedData.bankAddress.trim() === '') {
                console.log('开户支行验证失败:', cleanedData.bankAddress);
                Toast(this.$t('hj219') || '请输入正确的开户支行');
                return false;
            }

            // 开户支行格式验证：允许中文、英文、数字、括号、点等常见字符
            const bankAddressReg = /^[\u4E00-\u9FA5a-zA-Z0-9（）()·\.\-\s]{2,50}$/;
            if (!bankAddressReg.test(cleanedData.bankAddress)) {
                console.log('开户支行格式验证失败:', cleanedData.bankAddress);
                Toast('开户支行格式不正确，请输入2-50位中文、英文或数字');
                return false;
            }

            // 验证银行卡号
            if (!cleanedData.bankNo || cleanedData.bankNo.trim() === '') {
                console.log('银行卡号验证失败:', cleanedData.bankNo);
                Toast(this.$t('hj217') || '请输入正确的银行卡号');
                return false;
            }

            if (!bankNoReg(cleanedData.bankNo)) {
                console.log('银行卡号格式验证失败:', cleanedData.bankNo);
                Toast(this.$t('hj217') || '请输入正确的银行卡号');
                return false;
            }

            console.log('表单验证通过');
            return true;
        },

        // 获取清理后的表单数据
        getCleanedFormData() {
            const cleaned = {
                bankNo: String(this.formData.bankNo).replace(/\s+/g, ''),
                bankName: String(this.formData.bankName).replace(/\s+/g, ''),
                bankAddress: String(this.formData.bankAddress).replace(/\s+/g, ''),
            };
            console.log('清理前的表单数据:', this.formData);
            console.log('清理后的表单数据:', cleaned);
            return cleaned;
        },

        // 处理提交响应
        handleSubmitResponse(response) {
            if (!response) {
                throw new Error('服务器无响应');
            }

            if (response.status === 0) {
                Toast(this.$t('hj256') || '银行卡更换成功');
                // 延迟跳转，让用户看到成功提示
                setTimeout(() => {
                    this.navigateToUserPage();
                }, 1500);
            } else {
                Toast(response.msg || '更换失败，请重试');
            }
        },

        // 跳转到用户页面
        navigateToUserPage() {
            try {
                this.$router.push('/newUser');
            } catch (error) {
                console.error('页面跳转失败:', error);
                this.$router.go(-1);
            }
        },

        // 跳转到绑定卡片页面
        navigateToBindCard() {
            try {
                this.$router.push('/bankCard');
            } catch (error) {
                console.error('跳转绑定页面失败:', error);
                this.$router.go(-1);
            }
        },

        // 重置表单到原始状态
        resetForm() {
            if (this.isDataLoaded) {
                this.formData = { ...this.originalData };
                Toast('已重置为原始数据');
            }
        },

        // 统一错误处理
        handleError(error, defaultMessage = '操作失败') {
            console.error('错误详情:', error);

            if (error.response) {
                switch (error.response.status) {
                    case 401:
                        Toast('登录已过期，请重新登录');
                        break;
                    case 403:
                        Toast('没有操作权限');
                        break;
                    case 500:
                        Toast('服务器异常，请稍后重试');
                        break;
                    default:
                        Toast((error.response.data && error.response.data.msg) || '网络请求失败');
                }
            } else {
                Toast(error.message || defaultMessage);
            }
        },

        // 输入框格式化（银行卡号）
        formatBankCardInput() {
            // 自动格式化银行卡号输入（每4位加空格）
            let value = this.formData.bankNo.replace(/\s/g, '');
            let formatted = value.replace(/(\d{4})(?=\d)/g, '$1 ');
            this.formData.bankNo = formatted;
        },

        // 兼容旧方法名
        async toSure() {
            await this.handleSubmit();
        },

        async getCardDetail() {
            await this.loadBankCardInfo();
        }
    }
}
</script>

<style lang="less" scoped>
.bank-card-update-page {
    font-size: 0.3256rem;
    padding: 0;
    background: linear-gradient(180deg, #F6F6F6 0%, #FAFAFA 50%, #F0F0F0 100%);
    min-height: 100vh;
    box-sizing: border-box;
    overflow-x: hidden;

    &__header {
        width: 100%;
        height: 1.07rem;
        z-index: 999;
        position: relative;
    }

    &__content {
        margin-top: 0.2326rem;
        padding-bottom: 0.4651rem;
        min-height: calc(100vh - 1.3023rem);
        box-sizing: border-box;
        width: 100%;
        overflow-x: hidden;
    }
}

// 信息卡片样式
.info-card {
    margin: 0.3488rem;
    padding: 0.4651rem;
    background: #FFFFFF;
    border-radius: 0.2326rem;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
    border: 1px solid #EEEEEE;
    display: flex;
    align-items: center;
    box-sizing: border-box;

    &__icon {
        margin-right: 0.3488rem;
        color: #f39c12;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 1.3953rem;
        height: 1.3953rem;
        background: #FEF9E7;
        border-radius: 50%;
    }

    &__content {
        flex: 1;

        .info-card__title {
            font-size: 0.4186rem;
            font-weight: 600;
            color: #2c3e50;
            margin: 0 0 0.1162rem 0;
            line-height: 1.2;
        }

        .info-card__description {
            font-size: 0.3023rem;
            color: #7f8c8d;
            margin: 0;
            line-height: 1.4;
        }
    }
}

// 对比区域样式
.compare-section {
    margin: 0.3488rem;
    box-sizing: border-box;

    &__header {
        display: flex;
        align-items: center;
        margin-bottom: 0.2326rem;
        color: #2980b9;
        font-weight: 600;
        font-size: 0.3488rem;

        .van-icon {
            margin-right: 0.1860rem;
        }
    }

    .original-card {
        background: #FFFFFF;
        padding: 0.3488rem;
        border-radius: 0.2326rem;
        border: 1px solid #E3F2FD;
        border-left: 4px solid #2196f3;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
        box-sizing: border-box;

        &__item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.2326rem;

            &:last-child {
                margin-bottom: 0;
            }
        }

        &__label {
            font-size: 0.3023rem;
            color: #546e7a;
            font-weight: 500;
        }

        &__value {
            font-size: 0.3256rem;
            color: #2c3e50;
            font-weight: 600;
        }
    }
}

// 表单区域样式
.form-section {
    margin: 0.3488rem;
    padding: 0.4651rem;
    background: #FFFFFF;
    border-radius: 0.2326rem;
    border: 1px solid #EEEEEE;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
    box-sizing: border-box;

    &__header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.4651rem;
        padding-bottom: 0.2326rem;
        border-bottom: 1px solid #F0F0F0;
    }

    &__title {
        font-size: 0.4186rem;
        font-weight: 600;
        color: #2c3e50;
        margin: 0;
    }

    .reset-button {
        background: #F8F9FA;
        color: #6C757D;
        border: 1px solid #DEE2E6;
        padding: 0.1860rem 0.3488rem;
        border-radius: 0.1860rem;
        font-size: 0.2791rem;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
            background: #E9ECEF;
            border-color: #ADB5BD;
            transform: translateY(-1px);
        }

        &:active {
            transform: translateY(0);
        }
    }

    .form-item {
        margin-bottom: 0.4651rem;

        &__label {
            display: flex;
            align-items: center;
            font-size: 0.3721rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.2326rem;
        }

        &__icon {
            margin-right: 0.1860rem;
            color: #EA001B;
        }

        &__required {
            color: #e74c3c;
            margin-left: 0.1162rem;
            font-weight: bold;
        }

        &__input {
            position: relative;
            width: 100%;
            box-sizing: border-box;

            .input-field {
                width: 100%;
                height: 1.1627rem;
                padding: 0 0.3488rem;
                background: #FAFAFA;
                border: 1px solid #E0E0E0;
                border-radius: 0.2326rem;
                font-size: 0.3488rem;
                color: #2c3e50;
                transition: all 0.3s ease;
                box-sizing: border-box;
                outline: none;

                &:focus {
                    outline: none;
                    background: #FFFFFF;
                    border-color: #EA001B;
                    box-shadow: 0 0 0 3px rgba(234, 0, 27, 0.1);
                }

                &::placeholder {
                    color: #BDBDBD;
                    font-size: 0.3256rem;
                }

                &--error {
                    border-color: #e74c3c;
                    background: #FFF5F5;

                    &:focus {
                        border-color: #e74c3c;
                        box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
                    }
                }

                &--changed {
                    border-color: #f39c12;
                    background: #FFFBF0;

                    &:focus {
                        border-color: #f39c12;
                        box-shadow: 0 0 0 3px rgba(243, 156, 18, 0.1);
                    }
                }
            }
        }
    }
}

// 变更提示样式
.change-notice {
    margin: 0.4651rem 0.3488rem 0;
    padding: 0.3488rem;
    background: #FFFFFF;
    border-radius: 0.2326rem;
    border: 1px solid #FFF3CD;
    border-left: 4px solid #f39c12;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
    box-sizing: border-box;

    &__header {
        display: flex;
        align-items: center;
        margin-bottom: 0.2326rem;
        color: #f39c12;
        font-weight: 600;
        font-size: 0.3488rem;

        .van-icon {
            margin-right: 0.1860rem;
        }
    }

    &__content {
        p {
            margin: 0 0 0.2326rem 0;
            font-size: 0.3023rem;
            color: #2c3e50;
        }

        .change-list {
            list-style: none;
            padding: 0;
            margin: 0;

            &__item {
                font-size: 0.3023rem;
                color: #e67e22;
                line-height: 1.6;
                margin-bottom: 0.1162rem;
                position: relative;
                padding-left: 0.3488rem;

                &:last-child {
                    margin-bottom: 0;
                }

                &::before {
                    content: '→';
                    position: absolute;
                    left: 0;
                    color: #f39c12;
                    font-weight: bold;
                }
            }
        }
    }
}

// 操作按钮样式
.action-section {
    padding: 0.4651rem 0.3488rem 0;
    box-sizing: border-box;

    .action-button {
        width: 100%;
        height: 1.2791rem;
        border: none;
        border-radius: 0.2326rem;
        font-size: 0.3721rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.1860rem;
        position: relative;
        overflow: hidden;
        box-sizing: border-box;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        &:hover::before {
            left: 100%;
        }

        &--primary {
            background: linear-gradient(135deg, #EA001B, #FF6B3D);
            color: #fff;
            box-shadow: 0 2px 8px rgba(234, 0, 27, 0.2);

            &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(234, 0, 27, 0.3);
            }

            &:active {
                transform: translateY(0);
            }
        }

        &--disabled {
            background: #E9ECEF;
            color: #6C757D;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

            &:hover {
                transform: none;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            }

            &::before {
                display: none;
            }
        }

        &--loading {
            pointer-events: none;

            &::before {
                display: none;
            }
        }
    }
}

// 安全提示样式
.security-tips {
    margin: 0.4651rem 0.3488rem 0;
    padding: 0.3488rem;
    background: #FFFFFF;
    border-radius: 0.2326rem;
    border: 1px solid #F8D7DA;
    border-left: 4px solid #e74c3c;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
    box-sizing: border-box;

    &__header {
        display: flex;
        align-items: center;
        margin-bottom: 0.2326rem;
        color: #e74c3c;
        font-weight: 600;
        font-size: 0.3488rem;

        .van-icon {
            margin-right: 0.1860rem;
        }
    }

    &__list {
        list-style: none;
        padding: 0;
        margin: 0;

        .security-tips__item {
            font-size: 0.3023rem;
            color: #7f8c8d;
            line-height: 1.6;
            margin-bottom: 0.0930rem;
            position: relative;
            padding-left: 0.3488rem;

            &:last-child {
                margin-bottom: 0;
            }

            &::before {
                content: '•';
                position: absolute;
                left: 0;
                color: #e74c3c;
                font-weight: bold;
            }
        }
    }
}

// 加载遮罩样式
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(246, 246, 246, 0.95);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(3px);

    .van-loading {
        color: #EA001B;
    }
}

// 响应式设计
@media (max-width: 375px) {
    .bank-card-update-page {
        &__content {
            margin-top: 0.1162rem;
        }
    }

    .info-card {
        margin: 0.2326rem;
        padding: 0.3488rem;

        &__icon {
            width: 1.1627rem;
            height: 1.1627rem;
            margin-right: 0.2326rem;
        }

        &__content {
            .info-card__title {
                font-size: 0.3721rem;
            }

            .info-card__description {
                font-size: 0.2791rem;
            }
        }
    }

    .compare-section {
        margin: 0.2326rem;

        .original-card {
            padding: 0.2791rem;

            &__item {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.1162rem;
            }

            &__label {
                font-size: 0.2791rem;
            }

            &__value {
                font-size: 0.3023rem;
            }
        }
    }

    .form-section {
        margin: 0.2326rem;

        &__title {
            font-size: 0.3721rem;
        }

        .reset-button {
            padding: 0.1162rem 0.2326rem;
            font-size: 0.2558rem;
        }

        .form-item {
            &__label {
                font-size: 0.3488rem;
            }

            &__input {
                .input-field {
                    height: 1.0465rem;
                    font-size: 0.3256rem;
                    padding: 0 0.2791rem;
                }
            }
        }
    }

    .change-notice {
        margin: 0.3488rem 0.2326rem 0;
        padding: 0.2791rem;

        &__header {
            font-size: 0.3256rem;
        }

        &__content {
            p {
                font-size: 0.2791rem;
            }

            .change-list {
                &__item {
                    font-size: 0.2791rem;
                    padding-left: 0.2791rem;
                }
            }
        }
    }

    .action-section {
        padding: 0.3488rem 0.2326rem 0;

        .action-button {
            height: 1.1627rem;
            font-size: 0.3488rem;
        }
    }

    .security-tips {
        margin: 0.3488rem 0.2326rem 0;
        padding: 0.2791rem;

        &__header {
            font-size: 0.3256rem;
        }

        &__list {
            .security-tips__item {
                font-size: 0.2791rem;
                padding-left: 0.2791rem;
            }
        }
    }
}

// 滚动条美化
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: rgba(241, 241, 241, 0.3);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #EA001B, #FF6B3D);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #d00019, #e55a37);
}

// 焦点可见性
.input-field:focus-visible,
.action-button:focus-visible {
    outline: 2px solid #EA001B;
    outline-offset: 2px;
}
</style>
