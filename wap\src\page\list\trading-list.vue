<template>
    <div class="trading-list">
        <div class="hot-section">
            <div class="hot-section-title">行情</div>
            <div class="hot-desc">
                <div class="hot-desc-item">
                    <div class="hot-desc-item-title">
                        <img src="@/assets/newtemp/1.png" alt="" class="hot-desc-item-title-img">
                        门槛低
                    </div>
                    <div class="hot-desc-item-title">
                        <img src="@/assets/newtemp/1.png" alt="" class="hot-desc-item-title-img">
                        收益高
                    </div>
                </div>
            </div>

            <div class="hot-grid">
                <div :class="`hot-item ${value.floatPoint < 0 ? 'down' : 'up'}`" v-for="value in hotStockList"
                    :key="value.id">
                    <div class="hot-title">{{ value.indexName }}</div>
                    <div class="hot-price">{{ value.currentPoint }}</div>
                    <div class="hot-info">
                        <div class="hot-change">{{ Number(value.floatPoint).toFixed(2) }}</div>
                        <div class="hot-rate">{{ value.floatRate }}%</div>
                        <div class="clear"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="main-content">
            <div class="tab-menu">
                <div :class="`tab-item ${itemIndex == 0 ? 'active' : ''}`" @click="changeItemIndex(0)">
                    <div class="tab-title">自选</div>
                    <div class="tab-bar"></div>
                </div>
                <div :class="`tab-item ${itemIndex == 1 ? 'active' : ''}`" @click="changeItemIndex(1)">
                    <div class="tab-title">上交所</div>
                    <div class="tab-bar"></div>
                </div>
                <div :class="`tab-item ${itemIndex == 2 ? 'active' : ''}`" @click="changeItemIndex(2)">
                    <div class="tab-title">深交所</div>
                    <div class="tab-bar"></div>
                </div>
                <div :class="`tab-item ${itemIndex == 3 ? 'active' : ''}`" @click="changeItemIndex(3)">
                    <div class="tab-title">科创</div>
                    <div class="tab-bar"></div>
                </div>
                <div :class="`tab-item ${itemIndex == 4 ? 'active' : ''}`" @click="changeItemIndex(4)">
                    <div class="tab-title">北交所</div>
                    <div class="tab-bar"></div>
                </div>
                <div class="clear"></div>
            </div>
            <div class="data-table">
                <div class="table-header">
                    <div class="col name-col">股票名称</div>
                    <div class="col price-col">最新价格</div>
                    <div class="col change-col">涨跌幅</div>
                    <div class="clear"></div>
                </div>
                <div class="table-body">
                    <van-list v-model="loading" :finished="finished" :immediate-check="false"
                        :finished-text="$t('hj43')" @load="getStockList">
                        <div class="table-row" v-for="value in stockList" :key="value.id" @click="goDetail(value)">
                            <div class="cell name-cell">
                                <div class="stock-title" v-if="itemIndex == 0">{{ value.stockName }}</div>
                                <div class="stock-title" v-else>{{ value.name }}</div>
                                <stock-tag-info v-if="itemIndex == 0" :stock-code="value.stockGid" />
                                <stock-tag-info v-else :stock-code="value.symbol" />
                            </div>

                            <div :class="`cell price-cell ${value.hcrate > 0 ? 'up' : 'down'}`" v-if="itemIndex == 0">{{
                                Number(value.nowPrice).toFixed(2) }}</div>
                            <div :class="`cell price-cell ${value.changepercent > 0 ? 'up' : 'down'}`" v-else>{{
                                Number(value.buy).toFixed(2) }}</div>

                            <div class="cell change-cell" v-if="itemIndex == 0">
                                <div :class="`change-badge ${value.hcrate > 0 ? 'up' : 'down'}`">
                                    {{ value.hcrate }}%</div>
                            </div>
                            <div class="cell change-cell" v-else>
                                <div :class="`change-badge ${value.changepercent > 0 ? 'up' : 'down'}`">
                                    {{ value.changepercent }}%
                                </div>
                            </div>
                            <div class="clear"></div>
                        </div>
                    </van-list>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import * as api from "@/axios/api";
import StockTagInfo from "@/components/stock-tag-info.vue";

export default {
    components: {
        StockTagInfo
    },
    data() {
        return {
            itemIndex: 0,
            hotStockList: [],
            stockList: [],
            pageNum: 1,
            finished: false,
            loading: false,
            currentRequestId: 0, // 添加请求ID来追踪最新请求
            pageSize: 15
        };
    },
    mounted() {
        this.getListMarket();
        this.changeItemIndex(0);
    },
    methods: {
        changeItemIndex(index) {
            this.itemIndex = index;
            this.resetPagination();
            
            // 生成新的请求ID
            this.currentRequestId = Date.now();

            if (this.itemIndex === 0) {
                this.getMyList();
            } else {
                this.getStockList();
            }
        },

        resetPagination() {
            this.pageNum = 1;
            this.finished = false;
            this.stockList = [];
        },

        getMyList() {
            this.loading = true;
            
            // 记录当前请求ID
            const requestId = this.currentRequestId;

            const opt = {
                pageNum: this.pageNum,
                pageSize: this.pageSize,
                keyWords: this.gpcodes,
            };

            api.getMyList(opt).then((res) => {
                // 检查是否为最新请求
                if (requestId !== this.currentRequestId) {
                    return; // 忽略过时的响应
                }
                this.handleListResponse(res.data.list);
            }).catch((error) => {
                // 检查是否为最新请求
                if (requestId !== this.currentRequestId) {
                    return; // 忽略过时的响应
                }
                console.error('获取自选股票失败:', error);
                this.loading = false;
            });
        },

        getStockList() {
            this.loading = true;
            
            // 记录当前请求ID
            const requestId = this.currentRequestId;

            const opt = {
                pageNum: this.pageNum,
                pageSize: 20,
                asc: 1,
                node: this.getNodeByIndex()
            };

            api.getStockSort(opt).then((res) => {
                // 检查是否为最新请求
                if (requestId !== this.currentRequestId) {
                    return; // 忽略过时的响应
                }
                this.handleListResponse(res.data);
            }).catch((error) => {
                // 检查是否为最新请求
                if (requestId !== this.currentRequestId) {
                    return; // 忽略过时的响应
                }
                console.error('获取股票列表失败:', error);
                this.loading = false;
            });
        },

        getNodeByIndex() {
            const nodeMap = {
                1: "sh_a",
                2: "sz_a",
                3: "kcb",
                4: "hs_bjs"
            };
            return nodeMap[this.itemIndex] || "hs_a";
        },

        handleListResponse(data) {
            if (data.length < this.pageSize) {
                this.finished = true;
            }
            this.stockList.push(...data);
            this.loading = false;
            this.pageNum++;
        },

        getListMarket() {
            const val = {
                pageNum: 1,
                pageSize: 15,
            };

            api.getListMarket(val).then((res) => {
                this.hotStockList = res.data.slice(0, 3);
            }).catch((error) => {
                console.error('获取市场数据失败:', error);
            });
        },

        goDetail(item) {
            const symbol = this.itemIndex === 0 ? item.stockGid : item.symbol;
            const codes = this.itemIndex === 0 ? item.stockCode : item.code;
            const names = item.name;
            const stock_type = symbol.substring(0, 2);
            const soks = this.filterSH(symbol);
            const if_us = item.stock_type === "us" ? "1" : "";

            this.$router.push({
                path: "/kline",
                query: {
                    name: names,
                    stockplate: item.stock_plate,
                    code: codes,
                    type: stock_type,
                    sok: soks,
                    if_us: if_us,
                    usType: item.type,
                    if_zhishu: "0",
                    symbol: symbol,
                },
            });
        },

        filterSH(val) {
            if (val.indexOf("sh") >= 0) {
                return 1;
            } else if (val.indexOf("bj") >= 0 || val.indexOf("sz") >= 0) {
                return 0;
            }
            return 0;
        },
    },

    beforeDestroy() {
        // 组件销毁时重置请求ID，确保不处理过时响应
        this.currentRequestId = 0;
    }
};
</script>

<style lang="less" scoped>
.trading-list {}

.hot-section {
    position: relative;

    &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        height: 240px;
        width: 100%;
        background: url("~@/assets/newtemp/3.png") no-repeat center center;
        background-size: 100% 100%;
        z-index: 0;
        mask: linear-gradient(to bottom, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.1) 80%, rgba(0, 0, 0, 0) 100%);
        -webkit-mask: linear-gradient(to bottom, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.1) 80%, rgba(0, 0, 0, 0) 100%);
    }

    &::after {
        content: "";
        position: absolute;
        bottom: 93px;
        right: 10px;
        width: 142px;
        height: 117px;
        background: url("~@/assets/newtemp/2.png") no-repeat center center;
        background-size: 100% 100%;
        z-index: 0;

    }


    .hot-section-title {
        /** 文本1 */
        font-size: 24px;
        font-weight: 700;
        letter-spacing: 0px;
        line-height: 34.75px;
        color: rgba(29, 36, 51, 1);
        text-align: left;
        vertical-align: top;
        padding-top: 30px;
        padding-left: 16px;
        position: relative;
        z-index: 1;
    }

    .hot-desc {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-start;
        position: relative;
        z-index: 1;
        padding-bottom: 23px;
        padding-left: 16px;
        padding-top: 8px;

        .hot-desc-item {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            gap: 13px;


            .hot-desc-item-title {
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: center;
                gap: 6px;

                /** 文本1 */
                font-size: 13px;
                font-weight: 400;
                letter-spacing: 0px;
                line-height: 18.82px;
                color: rgba(29, 36, 51, 1);
                text-align: left;
                vertical-align: top;

                .hot-desc-item-title-img {
                    width: 14px;
                    height: 14px;
                }
            }
        }
    }
}

.hot-grid {
    position: relative;
    z-index: 1;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin: 16px;

    border-radius: 10px;
    background: rgba(250, 232, 232, 1);

    border: 1px solid rgba(255, 255, 255, 1);
    padding: 10px;


}

.hot-item {

    border-radius: 10px;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 2px 4px rgba(168, 111, 64, 0.16);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 16px 0;
    gap: 4px;

}

.hot-item.up {
    color: rgba(217, 22, 1, 1);


}

.hot-item.down {
    color: rgba(0, 164, 68, 1);

}

.hot-title {

    /** 文本1 */
    font-size: 12px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 16.46px;
    color: rgba(29, 36, 51, 1);

}

.hot-price {

    /** 文本1 */
    font-size: 16px;
    font-weight: bold;
    letter-spacing: 0px;
    line-height: 19.38px;

}

.hot-info {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 10px;

    /** 文本1 */
    font-size: 10px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 12.11px;


}

.hot-change {}

.hot-rate {}

.main-content {
    background: #fff;
    border-radius: 10px;
    padding-top: 16px;
}

.tab-menu {
    padding: 0 16px;
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.tab-item {
    flex: 1;

    border-radius: 4px;
    background: rgba(243, 244, 248, 1);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 5px 15px 5px 15px;



    /** 文本1 */
    font-size: 13px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 18.82px;
    color: rgba(128, 128, 128, 1);
    border-radius: 8px;

}

.tab-item.active {
    border: 0.5px solid rgba(252, 61, 55, 1);

    background: rgba(224, 57, 54, 0.05);
    /** 文本1 */
    font-size: 13px;
    font-weight: bold;
    letter-spacing: 0px;
    line-height: 18.82px;
    color: rgba(224, 57, 54, 1);

}

.tab-title {
    white-space: nowrap;
}

.tab-bar {}

.data-table {}

.table-header {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
    border-bottom: 1px solid rgba(242, 242, 242, 1);
    padding-bottom: 7px;
    padding-left: 16px;
}

.table-body {}

.table-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
    border-bottom: 1px solid rgba(242, 242, 242, 1);
    padding: 10px;
}

.col {
    flex: 1;
    font-size: 13px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 17.84px;
    color: rgba(179, 179, 179, 1);

}

.name-col {
    text-align: left;
}

.price-col {
    text-align: center;
}

.change-col {
    text-align: right;
}

.cell {
    flex: 1;
}

.name-cell {
    text-align: left;
}

.price-cell {
    text-align: center;
    font-size: 16px;
    font-weight: bold;
    letter-spacing: 0px;
    line-height: 21.95px
}

.change-cell {
    text-align: right;
}

.stock-title {
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 19.21px;
    color: rgba(29, 36, 51, 1);
}

.change-badge {


    font-size: 16px;
    font-weight: bold;
    letter-spacing: 0px;
    line-height: 21.95px;

}

.up {
    color: rgba(217, 22, 1, 1);

}

.down {
    color: rgba(0, 164, 68, 1);

}

.clear {
    clear: both;
}
</style>
