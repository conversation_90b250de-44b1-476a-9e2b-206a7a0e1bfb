<template>
    <div class="new-share-detail-page">
        <div class="header">
            <van-nav-bar :title="pageTitle" left-arrow @click-left="$router.go(-1)" fixed></van-nav-bar>
        </div>

        <div v-if="loading" class="loading-state">
            <van-loading size="24px" vertical>加载中...</van-loading>
        </div>

        <div v-else class="detail-content">
            <!-- 股票基本信息卡片 -->
            <div class="info-card">
                <div class="card-header">
                    <div class="stock-info">
                        <stock-tag-info :stock-code="stockGid" />
                        <div class="stock-name">{{ currentItem.name }}</div>
                    </div>
                    <!-- <div class="stock-code">[{{ currentItem.code }}]</div> -->
                </div>

                <div class="card-title">基本信息</div>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="label">股票代码</span>
                        <span class="value">{{ currentItem.code }}</span>
                    </div>
                    <!-- <div class="info-item">
                        <span class="label">申购代码</span>
                        <span class="value">{{ currentItem.code }}</span>
                    </div> -->
                    <div class="info-item">
                        <span class="label">股票名称</span>
                        <span class="value">{{ currentItem.name }}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">所属板块</span>
                        <span class="value">{{ currentItem.stockType }}</span>
                    </div>
                </div>
            </div>

            <!-- 发行信息卡片 -->
            <div class="info-card">
                <div class="card-title">发行信息</div>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="label">发行价格</span>
                        <span class="value price">¥{{ formatPrice(currentItem.price) }} / 股</span>
                    </div>
                    <div class="info-item">
                        <span class="label">发行数量</span>
                        <span class="value">{{ formatNumber(currentItem.orderNumber) }} 万股</span>
                    </div>
                    <div class="info-item">
                        <span class="label">市盈率</span>
                        <span class="value">{{ formatPE(currentItem.pe) }}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">申购时间</span>
                        <span class="value">{{ formatTime(currentItem.subscribeTime) }}</span>
                    </div>
                </div>
            </div>

            <!-- 申购信息提示 -->
            <!-- <div class="subscription-info" v-if="userinfo.enableAmt">
                <div class="info-title">
                    <span class="icon">💰</span>
                    申购额度信息
                </div>
                <div class="amount-info">
                    <div class="amount-item">
                        <span class="label">可用资金</span>
                        <span class="amount">¥{{ formatPrice(userinfo.enableAmt) }}</span>
                    </div>
                    <div class="amount-item">
                        <span class="label">可申购数量</span>
                        <span class="shares">{{ maxSubscriptionShares }} 股</span>
                    </div>
                </div>
            </div> -->

            <!-- 新股申购按钮 -->
            <div v-if="type === '1'" class="subscription-btn" @click="confirmSubscription"
                :class="{ disabled: isSubscribing }">
                <span v-if="isSubscribing">申购中...</span>
                <span v-else>立即申购</span>
            </div>

            <!-- 配售操作区 -->
            <div v-if="type === '2'" class="allotment-area">
                <div class="allotment-box">
                    <div class="stepper-container">
                        <div class="stepper-label">配售(手数)</div>
                        <div class="custom-stepper">
                            <button class="stepper-btn stepper-minus" @click="decreaseAmount"
                                :disabled="applyAmount <= 1" :class="{ disabled: applyAmount <= 1 }">
                                <span class="btn-icon">−</span>
                            </button>
                            <div class="stepper-input-wrapper">
                                <input class="stepper-input" v-model.number="applyAmount" type="number" :min="1"
                                    :max="maxApplyAmount" @input="validateAmount" @blur="validateAmount" />
                            </div>
                            <button class="stepper-btn stepper-plus" @click="increaseAmount"
                                :disabled="applyAmount >= maxApplyAmount"
                                :class="{ disabled: applyAmount >= maxApplyAmount }">
                                <span class="btn-icon">+</span>
                            </button>
                        </div>
                    </div>
                    <div class="allotment-btn" @click="confirmSubscription" :class="{ disabled: isSubscribing }">
                        <span v-if="isSubscribing">配售中...</span>
                        <span v-else>¥{{ totalPrice.toFixed(2) }}配售</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import * as api from "@/axios/api";
import { Toast, MessageBox } from "mint-ui";
import StockTagInfo from "@/components/stock-tag-info.vue";
import dayjs from "dayjs";

export default {
    name: "NewShareDetailPage",
    components: {
        StockTagInfo
    },
    data() {
        return {
            currentItem: {},
            userinfo: {},
            loading: false,
            isSubscribing: false,
            type: '1', // 默认为新股申购
            applyAmount: 1 // 配售手数，默认1手
        };
    },
    computed: {
        // 页面标题
        pageTitle() {
            return this.type === '2' ? '配售详情' : '新股详情';
        },

        // 股票GID用于标签组件
        stockGid() {
            if (!this.currentItem.stockType || !this.currentItem.code) return '';

            const typeMap = {
                '深': 'sz',
                '创': 'sz',
                '沪': 'sh',
                '科': 'sh',
                '北': 'bj'
            };

            const prefix = typeMap[this.currentItem.stockType] || 'sz';
            return `${prefix}${this.currentItem.code}`;
        },

        // 最大可申购股数（新股申购模式）
        maxSubscriptionShares() {
            if (!this.userinfo.enableAmt || !this.currentItem.price) return 0;
            const maxShares = Math.floor(this.userinfo.enableAmt / this.currentItem.price);
            return maxShares > 0 ? maxShares : 0;
        },

        // 最大可申购手数（配售模式）
        maxApplyAmount() {
            if (!this.userinfo.enableAmt || !this.currentItem.price) {
                return 1;
            }
            // 计算最大可用手数（向下取整）
            const maxAmount = Math.floor(this.userinfo.enableAmt / (this.currentItem.price * 100));
            return maxAmount > 0 ? maxAmount : 1;
        },

        // 总配售金额
        totalPrice() {
            return this.applyAmount * this.currentItem.price * 100;
        }
    },
    mounted() {
        // 从 URL 获取 type 参数
        this.type = this.$route.query.type || '1';
        this.initializePage();
    },
    methods: {
        // 初始化页面
        async initializePage() {
            this.loading = true;

            try {
                // 解析股票数据
                await this.parseStockData();

                // 获取用户信息
                await this.loadUserInfo();

            } catch (error) {
                console.error('页面初始化失败:', error);
                this.$toast('页面加载失败，请重试');
                this.goBack();
            } finally {
                this.loading = false;
            }
        },

        // 解析股票数据
        async parseStockData() {
            try {
                const itemQuery = this.$route.query.item;
                if (!itemQuery) {
                    throw new Error('缺少股票数据');
                }

                this.currentItem = JSON.parse(decodeURIComponent(itemQuery));

                if (!this.currentItem.code || !this.currentItem.name) {
                    throw new Error('股票数据不完整');
                }

                console.log('新股详情数据:', this.currentItem);

            } catch (error) {
                console.error('解析股票数据失败:', error);
                throw new Error('股票数据解析失败');
            }
        },

        // 获取用户信息
        async loadUserInfo() {
            try {
                const response = await api.getUserInfo();

                if (!response || response.status !== 0) {
                    throw new Error('获取用户信息失败');
                }

                this.userinfo = response.data || {};
                console.log('用户信息加载成功:', this.userinfo);

            } catch (error) {
                console.error('获取用户信息失败:', error);
                this.$toast('获取用户信息失败');
                // 不阻断页面，用户信息获取失败时仍可查看详情
            }
        },

        // 确认申购
        confirmSubscription() {
            if (this.isSubscribing) {
                return;
            }

            // 检查用户信息
            if (!this.userinfo.enableAmt) {
                this.$toast('账户余额为0');
                return;
            }

            let _this = this

            // 根据类型确定申购数量和确认信息
            let applyNums, confirmMessage

            if (this.type === '2') {
                // 配售情况：使用用户选择的手数，转换为股数
                applyNums = _this.applyAmount * 100
                confirmMessage = `确定申购${_this.currentItem.name} ${_this.applyAmount}手，总金额￥${_this.totalPrice.toFixed(2)}?`
            } else {
                // 新股申购：计算可申购的最大股数
                applyNums = Math.floor(_this.userinfo.enableAmt / _this.currentItem.price)
                if (applyNums < 1) {
                    applyNums = 0
                }
                confirmMessage = `确定申购${_this.currentItem.name}?`
            }

            // 检查申购数量
            // if (applyNums < 1) {
            //     this.$toast('可用资金不足，无法申购');
            //     return;
            // }

            MessageBox.confirm(
                confirmMessage,
                this.$t("hj165"),
                {
                    confirmButtonText: this.$t("hj161"),
                    cancelButtonText: this.$t("hj106"),
                }
            ).then(() => {
                this.executeSubscription();
            }).catch(() => {
                console.log('用户取消申购');
            });
        },

        // 执行申购
        async executeSubscription() {
            this.isSubscribing = true;

            try {
                let _this = this
                let applyNums

                if (this.type === '2') {
                    // 配售情况：使用用户选择的手数，转换为股数
                    applyNums = _this.applyAmount * 100
                } else {
                    // 新股申购：计算可申购的最大股数
                    applyNums = Math.floor(_this.userinfo.enableAmt / _this.currentItem.price)
                    if (applyNums < 1) {
                        applyNums = 0
                    }
                }

                const params = {
                    newCode: this.currentItem.code,
                    applyNums: applyNums,
                    phone: this.userinfo.phone,
                    type: this.type,
                };

                console.log('申购参数:', params);

                const response = await api.getNewAdd(params);
                this.handleSubscriptionResponse(response);

            } catch (error) {
                this.handleSubscriptionError(error);
            } finally {
                this.isSubscribing = false;
            }
        },

        // 处理申购响应
        handleSubscriptionResponse(response) {
            console.log('申购响应:', response);

            if (!response) {
                throw new Error('服务器无响应');
            }

            if (response.status === 0) {
                Toast('申购成功！');
                setTimeout(() => {
                    this.goBack();
                }, 1500);
            } else {
                const errorMsg = response.msg || '申购失败';
                Toast(errorMsg);
            }
        },

        // 处理申购错误
        handleSubscriptionError(error) {
            console.error('申购失败:', error);

            if (error.response) {
                switch (error.response.status) {
                    case 401:
                        Toast('登录已过期，请重新登录');
                        break;
                    case 403:
                        Toast('没有申购权限');
                        break;
                    case 500:
                        Toast('服务器异常，请稍后重试');
                        break;
                    default:
                        Toast('网络请求失败');
                }
            } else {
                Toast(error.message || '申购失败，请重试');
            }
        },

        // 返回上一页
        goBack() {
            this.$router.go(-1);
        },

        // 格式化价格
        formatPrice(price) {
            if (!price && price !== 0) return '0.00';
            return parseFloat(price).toFixed(2);
        },

        // 格式化数字
        formatNumber(number) {
            if (!number && number !== 0) return '0';
            return parseInt(number).toLocaleString();
        },

        // 格式化市盈率
        formatPE(pe) {
            if (!pe && pe !== 0) return '--';
            const peValue = parseFloat(pe);
            return isNaN(peValue) ? '--' : peValue.toFixed(2);
        },

        // 格式化时间
        formatTime(time) {
            if (!time) return '--';
            return dayjs(time).format('YYYY-MM-DD');
        },

        // 增加数量
        increaseAmount() {
            if (this.applyAmount < this.maxApplyAmount) {
                this.applyAmount++;
                this.$nextTick(() => {
                    // 添加点击反馈动画
                    const plusBtn = document.querySelector('.stepper-plus');
                    if (plusBtn) {
                        plusBtn.classList.add('btn-active');
                        setTimeout(() => plusBtn.classList.remove('btn-active'), 150);
                    }
                });
            }
        },

        // 减少数量
        decreaseAmount() {
            if (this.applyAmount > 1) {
                this.applyAmount--;
                this.$nextTick(() => {
                    // 添加点击反馈动画
                    const minusBtn = document.querySelector('.stepper-minus');
                    if (minusBtn) {
                        minusBtn.classList.add('btn-active');
                        setTimeout(() => minusBtn.classList.remove('btn-active'), 150);
                    }
                });
            }
        },

        // 验证输入数量
        validateAmount() {
            if (this.applyAmount < 1) {
                this.applyAmount = 1;
            } else if (this.applyAmount > this.maxApplyAmount) {
                this.applyAmount = this.maxApplyAmount;
                this.$toast(`最大可申购${this.maxApplyAmount}手`);
            }
            // 确保是整数
            this.applyAmount = parseInt(this.applyAmount) || 1;
        }
    },
};
</script>

<style lang="less" scoped>
.new-share-detail-page {
    background: #f5f5f5;
    min-height: 100vh;
    font-size: 0.3256rem;
    padding-bottom: 1.8604rem;

    .header {
        width: 100%;
        height: 1.07rem;
    }

    .loading-state {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 60vh;
    }

    .detail-content {
        padding: 0.2326rem;

        .info-card {
            background: #fff;
            border-radius: 0.1860rem;
            margin-bottom: 0.2326rem;
            padding: 0.4651rem;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

            .card-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 0.4651rem;
                padding-bottom: 0.3488rem;
                border-bottom: 1px solid #f0f0f0;

                .stock-info {
                    display: flex;
                    align-items: center;
                    gap: 0.2326rem;

                    .stock-name {
                        font-size: 0.4186rem;
                        font-weight: 700;
                        color: #2c3e50;
                    }
                }

                .stock-code {
                    color: #7f8c8d;
                    font-size: 0.3256rem;
                    font-weight: 600;
                    font-family: 'Arial', 'Helvetica', monospace;
                }
            }

            .card-title {
                font-size: 0.3721rem;
                font-weight: 600;
                color: #2c3e50;
                margin-bottom: 0.3488rem;
                display: flex;
                align-items: center;

                &::before {
                    content: '';
                    width: 0.0930rem;
                    height: 0.3721rem;
                    background: linear-gradient(135deg, #EA001B, #FF6B3D);
                    border-radius: 0.0465rem;
                    margin-right: 0.2326rem;
                }
            }

            .info-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 0.3488rem;

                .info-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 0.2326rem 0;

                    .label {
                        color: #7f8c8d;
                        font-size: 0.3256rem;
                        font-weight: 500;
                    }

                    .value {
                        color: #2c3e50;
                        font-size: 0.3256rem;
                        font-weight: 600;
                        font-family: 'Arial', 'Helvetica', monospace;

                        &.price {
                            color: #EA001B;
                            font-weight: 700;
                            font-size: 0.3488rem;
                        }
                    }
                }
            }
        }

        .subscription-info {
            background: linear-gradient(135deg, #FFF5F5, #FFF0F0);
            border: 1px solid #FFE6E6;
            border-radius: 0.1860rem;
            margin-bottom: 0.2326rem;
            padding: 0.4651rem;

            .info-title {
                display: flex;
                align-items: center;
                gap: 0.1162rem;
                font-size: 0.3721rem;
                font-weight: 600;
                color: #EA001B;
                margin-bottom: 0.3488rem;

                .icon {
                    font-size: 0.4186rem;
                }
            }

            .amount-info {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 0.3488rem;

                .amount-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 0.2326rem 0;

                    .label {
                        color: #7f8c8d;
                        font-size: 0.3256rem;
                        font-weight: 500;
                    }

                    .amount,
                    .shares {
                        color: #EA001B;
                        font-size: 0.3488rem;
                        font-weight: 700;
                        font-family: 'Arial', 'Helvetica', monospace;
                    }
                }
            }
        }

        .subscription-btn {
            position: fixed;
            bottom: 0.3488rem;
            left: 5%;
            right: 5%;
            height: 1.1162rem;
            line-height: 1.1162rem;
            text-align: center;
            color: #fff;
            font-size: 0.3721rem;
            font-weight: 600;
            border-radius: 0.1860rem;
            background: linear-gradient(135deg, #EA001B, #FF6B3D);
            box-shadow: 0 4px 12px rgba(234, 0, 27, 0.3);
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(234, 0, 27, 0.4);
            }

            &:active {
                transform: translateY(0);
            }

            &.disabled {
                background: #bdc3c7;
                box-shadow: none;
                cursor: not-allowed;

                &:hover {
                    transform: none;
                    box-shadow: none;
                }
            }
        }

        .allotment-area {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: #fff;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
            padding: 0.4651rem;
            border-radius: 0.1860rem 0.1860rem 0 0;

            .allotment-box {
                display: flex;
                align-items: center;
                justify-content: space-between;

                .stepper-container {
                    display: flex;
                    align-items: center;

                    .stepper-label {
                        color: #2c3e50;
                        font-size: 0.3256rem;
                        margin-right: 0.3488rem;
                        font-weight: 500;
                    }

                    .custom-stepper {
                        display: flex;
                        align-items: center;
                        background: #f8f9fa;
                        border-radius: 0.2791rem;
                        border: 1px solid #e9ecef;
                        overflow: hidden;
                        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

                        .stepper-btn {
                            width: 0.8372rem;
                            height: 0.8372rem;
                            background: linear-gradient(135deg, #ffffff, #f8f9fa);
                            border: none;
                            cursor: pointer;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            transition: all 0.2s ease;
                            position: relative;

                            .btn-icon {
                                font-size: 0.4186rem;
                                font-weight: 600;
                                color: #EA001B;
                                line-height: 1;
                                user-select: none;
                            }

                            &:hover:not(.disabled) {
                                background: linear-gradient(135deg, #EA001B, #FF6B3D);
                                transform: scale(1.05);

                                .btn-icon {
                                    color: #fff;
                                }
                            }

                            &:active:not(.disabled),
                            &.btn-active:not(.disabled) {
                                transform: scale(0.95);
                                background: linear-gradient(135deg, #C8001A, #E55A2B);
                            }

                            &.disabled {
                                background: #f5f5f5;
                                cursor: not-allowed;

                                .btn-icon {
                                    color: #bdc3c7;
                                }
                            }

                            &.stepper-minus {
                                border-right: 1px solid #e9ecef;
                            }

                            &.stepper-plus {
                                border-left: 1px solid #e9ecef;
                            }
                        }

                        .stepper-input-wrapper {
                            flex: 1;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            background: #ffffff;
                            min-width: 1.1628rem;

                            .stepper-input {
                                width: 100%;
                                height: 0.8372rem;
                                border: none;
                                background: transparent;
                                text-align: center;
                                font-size: 0.3721rem;
                                font-weight: 600;
                                color: #2c3e50;
                                outline: none;
                                padding: 0 0.1163rem;
                                font-family: 'Arial', 'Helvetica', monospace;

                                /* 隐藏Chrome中的数字输入框的上下箭头 */
                                &::-webkit-outer-spin-button,
                                &::-webkit-inner-spin-button {
                                    -webkit-appearance: none;
                                    margin: 0;
                                }

                                /* 隐藏Firefox中的数字输入框的上下箭头 */
                                &[type=number] {
                                    -moz-appearance: textfield;
                                }
                            }
                        }
                    }
                }

                .allotment-btn {
                    background: linear-gradient(135deg, #EA001B, #FF6B3D);
                    height: 1.0232rem;
                    line-height: 1.0232rem;
                    padding: 0 0.5814rem;
                    color: #fff;
                    font-size: 0.3488rem;
                    font-weight: 600;
                    border-radius: 0.5116rem;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    box-shadow: 0 4px 12px rgba(234, 0, 27, 0.3);

                    &:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 6px 20px rgba(234, 0, 27, 0.4);
                    }

                    &:active {
                        transform: translateY(0);
                    }

                    &.disabled {
                        background: #bdc3c7;
                        box-shadow: none;
                        cursor: not-allowed;

                        &:hover {
                            transform: none;
                            box-shadow: none;
                        }
                    }
                }
            }
        }
    }
}

/* 加载状态样式 */
:deep(.van-loading) {
    .van-loading__text {
        color: #95a5a6;
        font-size: 0.3256rem;
        margin-top: 0.2326rem;
    }
}

/* 动画效果 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(0.4651rem);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.info-card {
    animation: slideInUp 0.3s ease-out;
}

.info-card:nth-child(1) {
    animation-delay: 0.1s;
}

.info-card:nth-child(2) {
    animation-delay: 0.2s;
}

.subscription-info {
    animation: slideInUp 0.3s ease-out;
    animation-delay: 0.3s;
}

.subscription-btn {
    animation: slideInUp 0.3s ease-out;
    animation-delay: 0.4s;
}

/* 响应式适配 */
@media (max-width: 375px) {
    .new-share-detail-page {
        .detail-content {
            padding: 0.1162rem;

            .info-card {
                padding: 0.3488rem;
                margin-bottom: 0.1162rem;
            }

            .subscription-info {
                padding: 0.3488rem;
            }
        }
    }
}
</style>