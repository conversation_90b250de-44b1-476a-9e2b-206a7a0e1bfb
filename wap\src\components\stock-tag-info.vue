<template>
    <div class="stock-tag-info">
        <div class="stock-tag-info__tag stock-tag-info__tag--sz" v-if="stockCode.indexOf('sz') > -1">深</div>
        <div class="stock-tag-info__tag stock-tag-info__tag--sh" v-if="stockCode.indexOf('sh') > -1">沪</div>
        <div class="stock-tag-info__tag stock-tag-info__tag--bj" v-if="stockCode.indexOf('bj') > -1">北</div>
        <div class="stock-tag-info__code" v-if="showCode">{{ stockCode }}</div>
    </div>
</template>

<script>
export default {
    name: 'StockTagInfo',
    props: {
        stockCode: {
            type: String,
            default: '',
            required: true
        },
        showCode: {
            type: Boolean,
            default: true
        }
    }
}
</script>

<style lang="less" scoped>
.stock-tag-info {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 4px;
}

.stock-tag-info__tag {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 20px;
    height: 20px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    line-height: 1;
    color: #fff;
    text-align: center;
    flex-shrink: 0;

    // 深交所 - 绿色系
    &--sz {
        background: linear-gradient(135deg, #00B050 0%, #00A046 100%);
        border: 1px solid #00A046;
        box-shadow: 0 2px 4px rgba(0, 176, 80, 0.2);
    }

    // 上交所 - 蓝色系  
    &--sh {
        background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
        border: 1px solid #357ABD;
        box-shadow: 0 2px 4px rgba(74, 144, 226, 0.2);
    }

    // 北交所 - 橙色系
    &--bj {
        background: linear-gradient(135deg, #FF8C00 0%, #E67E00 100%);
        border: 1px solid #E67E00;
        box-shadow: 0 2px 4px rgba(255, 140, 0, 0.2);
    }

    // 悬浮效果
    &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        transition: all 0.2s ease;
    }
}

.stock-tag-info__code {
    font-size: 12px;
    font-weight: 400;
    color: rgba(117, 117, 117, 1);
    line-height: 20px;
    letter-spacing: 0.5px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

// 响应式设计
@media (max-width: 375px) {
    .stock-tag-info {
        gap: 6px;

        &__tag {
            min-width: 18px;
            height: 18px;
            font-size: 11px;
        }

        &__code {
            font-size: 11px;
        }
    }
}

// 深色模式支持
@media (prefers-color-scheme: dark) {
    .stock-tag-info__code {
        color: #999999;
    }
}
</style>