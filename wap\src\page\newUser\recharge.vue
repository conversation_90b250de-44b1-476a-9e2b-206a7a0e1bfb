<template>
    <div class="container">
        <div class="header">
            <van-nav-bar title="银证转账" left-arrow @click-left="$router.go(-1)" fixed></van-nav-bar>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
            <van-loading color="#ee0011" vertical>加载中...</van-loading>
        </div>

        <template v-else-if="payEnabled == 0">
            <div class="content-box">
                <!-- 顶部图标和标题 -->
                <div class="content-header">
                    <div class="content-header__icon">
                        <van-icon name="info-o" size="1.2rem" />
                    </div>
                    <h2 class="content-header__title">银证转账说明</h2>
                    <p class="content-header__subtitle">请仔细阅读以下重要信息</p>
                </div>

                <!-- 说明内容 -->
                <div class="content-main">
                    <div class="intro-section">
                        <div class="intro-section__header">
                            <van-icon name="shield-o" size="0.4rem" />
                            <span>监管说明</span>
                        </div>
                        <div class="intro-text">
                            <p>由于该账户受证券监管部门监督，根据证监部门2021反洗黑钱金融条例，为了避免出现不法分子黑钱投入VIP账户，导致三方存管账户全面冻结带来的损失，因采取更高效审查机制支付通道，用户需通过三方存管和银行之间搭建的支付通道风控机制，进行出入金交易，三方存管账户进行一道关卡过滤黑钱！</p>
                        </div>
                    </div>

                    <div class="payment-methods">
                        <div class="payment-methods__header">
                            <van-icon name="credit-pay" size="0.4rem" />
                            <span>支付方式</span>
                        </div>
                        <div class="payment-methods__subtitle">目前机构共有四种支付方式进行注资存款：</div>

                        <div class="payment-list">
                            <div class="payment-item">
                                <div class="payment-item__number">1</div>
                                <div class="payment-item__content">
                                    <h4>银证转入</h4>
                                    <p>小额无卡注资额度1000-10000元</p>
                                </div>
                                <van-icon name="arrow" size="0.32rem" />
                            </div>

                            <!-- <div class="payment-item">
                                <div class="payment-item__number">2</div>
                                <div class="payment-item__content">
                                    <h4>手机银行注资</h4>
                                    <p>通过第三方存管公司进行转账划扣</p>
                                </div>
                                <van-icon name="arrow" size="0.32rem" />
                            </div>

                            <div class="payment-item">
                                <div class="payment-item__number">3</div>
                                <div class="payment-item__content">
                                    <h4>现金注资</h4>
                                    <p>工作人员上门收取现金完成注资10万元以上</p>
                                </div>
                                <van-icon name="arrow" size="0.32rem" />
                            </div>

                            <div class="payment-item">
                                <div class="payment-item__number">4</div>
                                <div class="payment-item__content">
                                    <h4>黄金通道注资</h4>
                                    <p>通过黄金商家合作购买投资类金条置换注资</p>
                                </div>
                                <van-icon name="arrow" size="0.32rem" />
                            </div> -->
                        </div>
                    </div>

                    <div class="contact-notice">
                        <div class="contact-notice__icon">
                            <van-icon name="service-o" size="0.48rem" />
                        </div>
                        <div class="contact-notice__content">
                            <p>各位机构成员如有不了解的地方请联系您的专属顾问进行了解！</p>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="content-actions">
                    <button class="contact-button" @click="silverTransfersInDescription">
                        <van-icon name="service-o" size="0.42rem" />
                        <span>联系客服转入</span>
                        <van-icon name="arrow" size="0.32rem" />
                    </button>
                </div>
            </div>
        </template>
        <template v-else-if="payEnabled == 1">
            <div class="layout">
                <div class="form">
                    <div class="top">
                        <img src="~@/assets/images/qiquan26/anquan.png" />
                        <div>资金交易有监管</div>
                    </div>
                    <div style="padding: 0.3488rem;">
                        <div class="title">银证转入</div>
                        <div class="input">
                            <input type="text" :placeholder="`请输入金额 ${channelMinLimit} - ${channelMaxLimit}`" v-model="price">
                        </div>
                    </div>
                </div>

                <div class="main_bottom">
                    <div class="main_bottom_title">简介说明</div>
                    <div class="main_bottom_content">
                        <p>由于该账户受证券监管部门监督，根据证监部门2021反洗黑钱金融条例，为了避免出现不法分子黑钱投入机构交易单元账户，导致三方存管账户全面冻结带来的损失，因采取更高效审查机制支付通道，用户需通过三方存管和银行之间搭建的支付通道风控机制，进行出入金交易，三方存管账户进行一道关卡过滤黑钱！
                        </p>
                        <p>目前机构共有三种支付方式进行注资存款：</p>
                        <p>1.银证转入一小额无卡注资</p>
                        <p>2.现金注资一工作人员上门服务收取现金完成注资</p>
                        <p>3.手机银行注资一通过第三方存管公司进行转账划扣</p>
                        <p>各位机构成员如有不了解的地方请联系您的专属顾问进行咨询！</p>
                    </div>
                </div>
                <div class="subbtn" @click="checkPriceEvent">确认充值</div>
            </div>
        </template>
    </div>
</template>
<script>
import * as api from "@/axios/api";
import { Toast } from "mint-ui";

export default {
    name: "recharge",
    data() {
        return {
            payEnabled: 0, // 默认值为0，从接口获取
            price: "",
            channelMinLimit: 0,
            channelMaxLimit: 0,
            loading: true, // 加载状态
        };
    },
    mounted() {
        this.initPage();
    },
    methods: {
        // 初始化页面
        async initPage() {
            try {
                // 调用接口获取配置
                const res = await api.getInitConfig();
                console.log("getInitConfig response:", res);

                if (res && res.data) {
                    // 获取payEnabled值，如果没有或者不是0/1，默认为0
                    this.payEnabled =
                        res.data.payEnabled === 0 || res.data.payEnabled === 1
                            ? res.data.payEnabled
                            : 0;
                } else {
                    // 接口返回异常，使用默认值
                    this.payEnabled = 0;
                }

                console.log("payEnabled value:", this.payEnabled);

                // 如果是充值模式(payEnabled = 1)，获取支付信息
                if (this.payEnabled == 1) {
                    await this.getPayInfoEvent();
                } else {
                    // 如果是联系客服模式，直接结束加载
                    this.loading = false;
                }
            } catch (error) {
                console.error("获取配置失败:", error);
                // 出错时使用默认值0
                this.payEnabled = 0;
                this.loading = false;
                Toast("获取配置失败，请稍后重试");
            }
        },

        silverTransfersInDescription() {
            this.$router.push({
                path: "/service",
            });
        },

        checkPriceEvent() {
            if (!/^[0-9]+.?[0-9]*$/.test(this.price)) {
                Toast("请输入正确的金额");
                return;
            }
            if (this.price < this.channelMinLimit) {
                Toast(
                    "金额不能小于" + this.useFormatMoney(this.channelMinLimit)
                );
                return;
            }
            if (this.price > this.channelMaxLimit) {
                Toast(
                    "金额不能大于" + this.useFormatMoney(this.channelMaxLimit)
                );
                return;
            }
            this.$router.push({
                path: "/rechargePay",
                query: {
                    price: this.price,
                },
            });
        },

        useFormatMoney(price, useCurrencySymbol = true, currency = "CNY") {
            const options = {
                minimumFractionDigits: 2, // 最少显示 0 位小数
                maximumFractionDigits: 6, // 最多显示 6 位小数
            };
            if (useCurrencySymbol) {
                options.style = "currency";
                options.currency = currency;
            }
            const number = Number(price || 0); // 确保 price 为数字，即使为 0
            if (isNaN(number)) {
                throw new Error(
                    "Invalid input: price must be a number--->" + price
                );
            }
            // 格式化数字，根据是否包含货币符号
            return number.toLocaleString(undefined, options);
        },

        async getPayInfoEvent() {
            try {
                const res = await api.getPayInfo();
                if (res && res.data && res.data.length > 0) {
                    this.channelMinLimit = res.data[0].channelMinLimit;
                    this.channelMaxLimit = res.data[0].channelMaxLimit;
                }
            } catch (error) {
                console.error("获取支付信息失败:", error);
                Toast("获取支付信息失败");
            } finally {
                this.loading = false;
            }
        },
    },
};
</script>
<style scoped lang="less">
.container {
    padding: 0;
    min-height: 100vh;

    &.bg {
        background: #fff;
    }

    .header {
        width: 100%;
        height: 1.07rem;
    }

    .layout {
        padding: 0.3488rem;

        .form {
            background: #fff;
            border-radius: 0.2325rem;
            overflow: hidden;
            .top {
                background: rgba(227, 235, 250, 1);
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 0.2325rem 0;
                color: rgba(37, 103, 255, 1);
                img {
                    width: 0.4651rem;
                    margin-right: 0.1162rem;
                }
            }
            .title {
                font-size: 0.4651rem;
            }
            .input {
                input {
                    height: 1.1162rem;
                    width: 100%;
                    border: none;
                    border-bottom: solid 1px rgba(230, 227, 227, 1);
                    font-size: 0.372rem;
                }
            }
        }
        .main_bottom {
            background: #fff;
            border-radius: 0.2325rem;
            padding: 0.3488rem;
            margin: 0.3488rem 0;

            .main_bottom_title {
                font-size: 0.372rem;
            }

            .main_bottom_content {
                margin-top: 0.3488rem;
                line-height: 200%;
                font-size: 0.3255rem;
                color: rgba(117, 117, 117, 1);
            }
        }
        .subbtn {
            border-radius: 8px;
            background: rgba(217, 22, 1, 1);
            box-shadow: 0px 2px 4px rgba(224, 57, 54, 0.49);
            color: #fff;
            text-align: center;
            line-height: 1.1162rem;
            margin-top: 0.3488rem;
            font-size: 0.372rem;
        }
    }

    // 优化后的content-box样式
    .content-box {
        margin: 0.3488rem;
        background: #ffffff;
        border-radius: 0.3488rem;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        border: 1px solid #f0f0f0;
        overflow: hidden;
        animation: slideInUp 0.6s ease-out;

        // 内容头部
        .content-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 0.5814rem 0.4651rem;
            text-align: center;
            color: #ffffff;
            position: relative;

            &::before {
                content: "";
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20" fill="white" opacity="0.1"><polygon points="0,20 100,0 100,20"/></svg>');
                background-size: cover;
            }

            &__icon {
                margin-bottom: 0.2326rem;
                position: relative;
                z-index: 1;

                .van-icon {
                    color: #ffffff;
                    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
                }
            }

            &__title {
                font-size: 0.5116rem;
                font-weight: 700;
                margin: 0 0 0.1162rem 0;
                position: relative;
                z-index: 1;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            }

            &__subtitle {
                font-size: 0.3256rem;
                margin: 0;
                opacity: 0.9;
                position: relative;
                z-index: 1;
                font-weight: 400;
            }
        }

        // 主要内容区域
        .content-main {
            padding: 0.4651rem;

            // 监管说明区域
            .intro-section {
                margin-bottom: 0.5814rem;

                &__header {
                    display: flex;
                    align-items: center;
                    margin-bottom: 0.3488rem;
                    color: #e74c3c;
                    font-weight: 600;
                    font-size: 0.3721rem;

                    .van-icon {
                        margin-right: 0.186rem;
                        color: #e74c3c;
                    }
                }

                .intro-text {
                    background: #fff5f5;
                    border-left: 4px solid #e74c3c;
                    padding: 0.3488rem;
                    border-radius: 0 0.2326rem 0.2326rem 0;
                    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.1);

                    p {
                        margin: 0;
                        font-size: 0.3256rem;
                        line-height: 1.6;
                        color: #2c3e50;
                        text-align: justify;
                    }
                }
            }

            // 支付方式区域
            .payment-methods {
                margin-bottom: 0.5814rem;

                &__header {
                    display: flex;
                    align-items: center;
                    margin-bottom: 0.2326rem;
                    color: #27ae60;
                    font-weight: 600;
                    font-size: 0.3721rem;

                    .van-icon {
                        margin-right: 0.186rem;
                        color: #27ae60;
                    }
                }

                &__subtitle {
                    font-size: 0.3256rem;
                    color: #7f8c8d;
                    margin-bottom: 0.3488rem;
                    font-weight: 500;
                }

                .payment-list {
                    .payment-item {
                        display: flex;
                        align-items: center;
                        padding: 0.3488rem;
                        margin-bottom: 0.2326rem;
                        background: #fafafa;
                        border-radius: 0.2326rem;
                        border: 1px solid #e8e8e8;
                        transition: all 0.3s ease;
                        cursor: pointer;

                        &:hover {
                            background: #f0f8ff;
                            border-color: #3498db;
                            transform: translateX(0.1162rem);
                            box-shadow: 0 2px 12px rgba(52, 152, 219, 0.15);
                        }

                        &:last-child {
                            margin-bottom: 0;
                        }

                        &__number {
                            width: 0.6977rem;
                            height: 0.6977rem;
                            background: linear-gradient(
                                135deg,
                                #3498db,
                                #2980b9
                            );
                            color: #ffffff;
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-size: 0.3256rem;
                            font-weight: 700;
                            margin-right: 0.3488rem;
                            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
                        }

                        &__content {
                            flex: 1;

                            h4 {
                                font-size: 0.3721rem;
                                color: #2c3e50;
                                margin: 0 0 0.093rem 0;
                                font-weight: 600;
                            }

                            p {
                                font-size: 0.3023rem;
                                color: #7f8c8d;
                                margin: 0;
                                line-height: 1.4;
                            }
                        }

                        .van-icon {
                            color: #bdc3c7;
                            margin-left: 0.2326rem;
                            transition: all 0.3s ease;
                        }

                        &:hover .van-icon {
                            color: #3498db;
                            transform: translateX(0.093rem);
                        }
                    }
                }
            }

            // 联系提示区域
            .contact-notice {
                display: flex;
                align-items: center;
                padding: 0.3488rem;
                background: #e8f5e8;
                border-radius: 0.2326rem;
                border-left: 4px solid #27ae60;
                box-shadow: 0 2px 8px rgba(39, 174, 96, 0.1);

                &__icon {
                    margin-right: 0.3488rem;
                    color: #27ae60;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 1.1627rem;
                    height: 1.1627rem;
                    background: #ffffff;
                    border-radius: 50%;
                    box-shadow: 0 2px 8px rgba(39, 174, 96, 0.2);
                }

                &__content {
                    flex: 1;

                    p {
                        margin: 0;
                        font-size: 0.3256rem;
                        color: #2c3e50;
                        line-height: 1.5;
                        font-weight: 500;
                    }
                }
            }
        }

        // 操作按钮区域
        .content-actions {
            padding: 0.4651rem;
            background: #fafafa;
            border-top: 1px solid #f0f0f0;

            .contact-button {
                width: 100%;
                height: 1.3953rem;
                background: linear-gradient(135deg, #e74c3c, #c0392b);
                color: #ffffff;
                border: none;
                border-radius: 0.3488rem;
                font-size: 0.3721rem;
                font-weight: 600;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 0.2326rem;
                cursor: pointer;
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;
                box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);

                &::before {
                    content: "";
                    position: absolute;
                    top: 0;
                    left: -100%;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(
                        90deg,
                        transparent,
                        rgba(255, 255, 255, 0.2),
                        transparent
                    );
                    transition: left 0.5s ease;
                }

                &:hover {
                    transform: translateY(-0.093rem);
                    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);

                    &::before {
                        left: 100%;
                    }
                }

                &:active {
                    transform: translateY(-0.0465rem);
                }

                .van-icon {
                    color: #ffffff;
                }

                span {
                    font-weight: 600;
                }
            }
        }
    }

    // 动画效果
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(0.9302rem);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
        }
        to {
            opacity: 1;
        }
    }

    // 响应式设计
    @media (max-width: 375px) {
        .content-box {
            margin: 0.2326rem;
            border-radius: 0.2326rem;

            .content-header {
                padding: 0.4651rem 0.3488rem;

                &__title {
                    font-size: 0.4651rem;
                }

                &__subtitle {
                    font-size: 0.3023rem;
                }
            }

            .content-main {
                padding: 0.3488rem;

                .intro-section {
                    margin-bottom: 0.4651rem;

                    &__header {
                        font-size: 0.3488rem;
                    }

                    .intro-text {
                        padding: 0.2791rem;

                        p {
                            font-size: 0.3023rem;
                        }
                    }
                }

                .payment-methods {
                    margin-bottom: 0.4651rem;

                    &__header {
                        font-size: 0.3488rem;
                    }

                    &__subtitle {
                        font-size: 0.3023rem;
                    }

                    .payment-list {
                        .payment-item {
                            padding: 0.2791rem;

                            &__number {
                                width: 0.5814rem;
                                height: 0.5814rem;
                                font-size: 0.3023rem;
                                margin-right: 0.2791rem;
                            }

                            &__content {
                                h4 {
                                    font-size: 0.3488rem;
                                }

                                p {
                                    font-size: 0.2791rem;
                                }
                            }
                        }
                    }
                }

                .contact-notice {
                    padding: 0.2791rem;

                    &__icon {
                        width: 0.9302rem;
                        height: 0.9302rem;
                        margin-right: 0.2791rem;
                    }

                    &__content {
                        p {
                            font-size: 0.3023rem;
                        }
                    }
                }
            }

            .content-actions {
                padding: 0.3488rem;

                .contact-button {
                    height: 1.2791rem;
                    font-size: 0.3488rem;
                    gap: 0.186rem;
                }
            }
        }
    }
}
</style>