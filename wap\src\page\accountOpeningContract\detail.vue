<template>
    <div class="account-opening-contract-page">
        <div class="headf">
            <div>
                <h2>
                    <span class="hbnh"><a class="fan" @click="$router.back()"></a></span>{{ type == 1 ? '服务协议' :
                        '保密协议'
                    }}
                </h2>
            </div>
        </div>
        <div class="content-class">
            <template v-if="type == 1">
                <h3>证券投资顾问咨询服务协议</h3>
                <div class="row-class">
                    <span class="title-class"> 甲方： </span>
                    <span class="value-class"> 招商证券股份有限公司 </span>
                </div>

                <div class="row-class">
                    <span class="title-class"> 地址： </span>
                    <span class="value-class"> 深圳市福田区福田街道福华一路111号 </span>
                </div>

                <div class="row-class">
                    <span class="title-class"> 乙方： </span>
                    <span class="value-class"> {{ user.realName }}</span>
                </div>

                <div class="row-class">
                    <span class="title-class"> 地址： </span>
                    <span class="value-class"> {{ user.addr }}</span>
                </div>

                <div class="row-class">
                    <span class="title-class"> 身份证号： </span>
                    <span class="value-class"> {{ user.idCard }}</span>
                </div>

                <div class="row-class">
                    根据《中华人民共和国证券法》、《中华人民共和国协议法》、《证券投资咨询管理暂行办法》和《会员制证券投资咨询业务管理暂行规定》等相关法律、法规，以及中国证券业协会（以下称协会）的有关自律规则，甲、乙双方本着平等、自愿，诚实信用的原则，就甲方向乙方提供证券投资咨询服务事项，签订本协议。
                </div>
                <h4>第一章：双方申明</h4>
                <div class="row-class">
                    第一条、甲方系中国证监会核准的证券投资咨询专业机构，具备提供分成制证券投资咨询服务的必要条件和专业能力。
                </div>
                <div class="row-class">
                    第二条、甲、乙双方共同遵守有关的法律、法规，不得利用本协议的关系从事任何违法、违规行为。
                </div>
                <h4>第二章：服务内容及方式</h4>
                <div class="row-class">
                    第一条、甲方向乙方提供A股市场证券代码和名称，甲方向乙方提供具体证券买入、卖出建议，乙方据此操作获取利润后按月向甲方支付咨询服务费。
                </div>
                <div class="row-class">
                    第二条、甲方向乙方提供服务，服务内容还包括：证券投资信息、分析、预测或咨询意见，个股研究成果及操作建议，提高新股申购中签率。
                </div>
                <div class="row-class">
                    第三条、甲方向乙方提供服务时仅使用内部加密软件通知，不通过其它方式提供服务。
                </div>
                <h4>第三章：甲方的权利和义务</h4>
                <div class="row-class">
                    第一条、甲方本着勤勉尽责、诚实守信的原则，为乙方提供专业、有偿服务。
                </div>
                <div class="row-class">
                    第二条、甲方有权根据提供的服务和协议约定收取咨询服务费，并享有所提供的研究分析结果的知识产权。
                </div>
                <div class="row-class">
                    第三条、甲方向乙方提供证券买入、卖出建议，乙方据此操作，甲方须承担乙方的投资损失。
                </div>
                <div class="row-class">第四条、甲方不得代理乙方直接从事证券投资。</div>
                <div class="row-class">
                    第五条、甲方保证其所提供的服务内容有相关根据并经合理论证，无虚假、片面和误导性的陈述。
                </div>
                <div class="row-class">
                    第六条、乙方如果未按照甲方的要求进行每日配合拉升，甲方有权利停止乙方的成交分配份额。
                </div>
                <div class="row-class">
                    第七条、甲方依法保护因服务关系而知悉的有关乙方的财产状况及其他个人隐私。
                </div>
                <div class="row-class">
                    第八条、乙方根据甲方提供的机构单元VIP账户中，存入资金联系在线客服领取的三方存管账户和港币注资账户明确规定为甲方监管资金账户，资金存入账户后出现任何问题都由甲方承担。
                </div>
                <h4>第四章：乙方的权利与义务</h4>
                <div class="row-class">
                    第一条、乙方成为甲方的证券投资顾问业务服务对象，享受甲方提供的专业服务。
                </div>
                <div class="row-class">
                    第二条、乙方基于甲方的判断，做出的证券投资决定，由双方共同承担投资损失。
                </div>
                <div class="row-class">
                    第三条、乙方与甲方按约定比例分享证券投资收益、承担投资损失。
                </div>
                <div class="row-class">
                    第四条、乙方不得将甲方提供的研究分析结果或建议泄露给任何第三人。
                </div>
                <div class="row-class">第五条、乙方根据协议约定按月支付咨询服务费。</div>
                <h4>第五章：咨询服务费</h4>
                <div class="row-class">
                    第一条、甲方向乙方提供具体证券买入、卖出建议，乙方据此操作获取利润后按照月收益的20%向甲方支付咨询服务费（月盈利达到60%以上收取利润的20%作为咨询费，达不到60%则不收取任何咨询费）
                </div>
                <div class="row-class">
                    第二条、乙方若出现亏损，甲方补偿乙方亏损金额的50%，每次操作乙方必须截图股票账户发给甲方统计数据。
                </div>
                <h4>第六章：服务期限</h4>
                <div class="row-class">
                    第一条、协议约定服务期为一个月，以甲乙双方签订协议日期开始计算，至本月末止一个月。
                </div>
                <h4>第七章：不可抗力</h4>
                <div class="row-class">
                    第一条、对于甲方无法预测或无法控制的设备故障、通讯故障及其它人力不可抗力而造成服务中断的情况，甲方不承担责任，但应尽快将发生不可抗力事故的情况通知乙方，并在乙方要求时提供有关证明文件。
                </div>
                <h4>第八章：争议的解决</h4>
                <div class="row-class">
                    第一条、凡因执行本协议所发生的或与本协议有关的一切争议，甲乙双方应本着善意通过友好协商解决。协商不成的，可申请由协会调解，调解不成或不符合协会调解规定的，应按下述几种方式解决：
                </div>
                <div class="row-class">
                    （一）提交仲裁委员会，按照申请仲裁时该会现行有效的仲裁规则进行仲裁。仲裁裁决是终局的，对双方均有约束力。仲裁费由败诉方负担。
                </div>
                <div class="row-class">（二）向有管辖权的法院提起诉讼。</div>
                <div class="row-class">
                    第二条、在争议处理过程中，除正在进行审理的部分外，协议的其它部分将继续执行，任何一方不得因发生争议故意损害或恶意诋毁另一方，不得威逼、利诱、恐吓对方，不得干扰和破坏对方的工作、学习和生活，如果违背本条款必须向对方赔付双倍于本协议所涉款项之违约金。
                </div>
                <h4>第九章：协议的生效、变更和终止</h4>
                <div class="row-class">
                    第一条、本协议经双方签字（盖章）即生效，服务期满终止，经双方协商同意，协议可提前终止。
                </div>
                <div class="row-class">
                    第二条、本协议签署生效后，若有关法律、法规及行业规定己修订，本协议涉及内容及条款按新修订的法律法规执行，但其它内容及条款继续有效，如本协议与新的法律法规严重背离双方有义务在体现原协议要义的基础上修改本协议，或重新签署协议。
                </div>
                <div class="row-class">第三条、甲乙双方经协商同意，可以修改本协议。</div>
                <div class="row-class">
                    第四条、若此协议一方愿意续约，必须在此协议到期之日前征得另一方同意后重新签订服务协议。
                </div>
                <div class="row-class">
                    第五条、乙方在协议签署后，及开通用于操作股票的机构单元VIP账户，甲方提供的A股市场买卖标的以及配售等功能，乙方须使用机构单元VIP账户来交易，甲方须协助乙方开通机构单元VIP账户。
                </div>

            </template>

            <template v-if="type == 2">

                <h3>招商证券商业核心信息保密协议</h3>

                <div class="row-class">
                    <span class="title-class"> 乙方： </span>
                    <span class="value-class"> {{ user.realName }}</span>
                </div>

                <div class="row-class">
                    <span class="title-class"> 地址： </span>
                    <span class="value-class"> {{ user.addr }}</span>
                </div>

                <div class="row-class">
                    <span class="title-class"> 身份证号： </span>
                    <span class="value-class"> {{ user.idCard }}</span>
                </div>

                <div class="row-class">
                    乙方系甲方的客户，经双方协商一致，达成以下商业核心信息保密条款：
                </div>
                <div class="row-class">
                    第一条：为防止商业核心信息的不当流动和使用，防范内幕交易和管理利益冲突，依据《证券公司信息隔离墙制度指引》等相关制度，签订本协议。
                </div>
                <div class="row-class">
                    第二条：本协议所称商业核心信息，是指公司在业务经营过程中掌握或知悉的内幕信息或者可能对投资决策产生重大影响的尚未公开的其他信息。
                </div>
                <div class="row-class">
                    第三条： 甲方商业核心信息包括但不限于下列事项：
                    《中华人民共和国证券法》第七十五条规定的内幕信息；与甲方及
                    其子公司经营的证券承销与保荐及财务顾问、证券经纪、证券自营、证券资产管理、发布证券研究报告、证券投资顾问、融资融券、直接投资等业务有关的可能导致甲方及其员工与客户之间、甲方不同客户之间产生利益冲突的其他信息。涉及甲方的经营、财务或者对甲方证券及其衍生品种的市场价格有重大影响，尚未在中国证监会指定的上市公司信息披露刊物或网站上正式公开披露的信息。
                </div>
                <div class="row-class">
                    第四条：乙方未经授权或批准不应获取商业核心信息，对已经获取的商业核心信息负有保密义务，并在甲方规章制度要求的范围、程度范围合理使用。乙方应当妥善保管涉及商业核心信息的文件资料，非因工作需要不得询问、谈论商业核心信息，不得将含有商业核心信息的资料擅自传播，不能通过任何途径泄漏给其他无关人员，不应利用商业核心信息为自己或他人谋取不当利益。
                </div>
                <div class="row-class">
                    第五条：乙方因其他原因终止合作时，应将接触到的所有记录着商业核心信息的文件、资料、报告、信件、传真、磁带、磁盘、仪器以及其他形式的载体交回甲方，并不得备份。
                </div>
                <div class="row-class">
                    第六条：乙方承诺终止合作后承担与合作期间同样的保密义务，直到该商业核心信息成为公开信息为止。
                </div>
                <div class="row-class">
                    第七条：乙方若违反本协议，须承担由其违约行为而给甲方造成的全部损失。乙方行为涉嫌违法违规的，甲方有权移送监管部门或司法机关处理。
                </div>
                <div class="row-class">
                    第八条：乙方承认已经阅读本协议，并确切了解协议中的法律含义。
                </div>
                <div class="row-class">
                    第九条：凡是挂靠在我们机构旗下各阶级投资者，每个月产生的利润，需要向我们机构账户缴纳利润20%分红，若该用户无法兑现，我们将移除该用户机构专用码使用权限，终身禁止合作。
                </div>
                <div class="row-class">
                    第十条：不得向除专项经理以外的任何人包括本公司及其子公司所属客服泄露本次合作事宜。
                </div>
                <div class="row-class">
                    第十一条：本协议一式两份，甲乙双方各执一份，自双方盖章或签字之日起生效。
                </div>
                <div class="row-class">
                    注：不得向任何有关机构、金融、证券、银行以及非我司助理透露任何关于我司倒筹运作事宜，违者必追究其法律责任！
                </div>

            </template>

            <div class="bb-class" v-if="signatureUrl">
                <div class="gz-class">
                    <div>甲方（盖章）：</div>
                    <div>{{ dayjs(signatureDate).format('YYYY-MM-DD HH:mm:ss') }}</div>
                </div>

                <div class="qm-class" v-if="signatureUrl">
                    <div class="row-class"> <span>甲方代表（签字）：</span> <img src="@/assets/me/qm.png" /></div>
                    <div>{{ dayjs(signatureDate).format('YYYY-MM-DD HH:mm:ss') }}</div>
                </div>
            </div>

            <div class="bb-class" v-if="signatureUrl">
                <div class="gz-class" style="background:none">
                    <div>乙方（盖章）：</div>
                    <div>{{ dayjs(signatureDate).format('YYYY-MM-DD HH:mm:ss') }}</div>
                </div>

                <div class="qm-class" v-if="signatureUrl">
                    <div class="row-class"> <span>乙方代表（签字）：</span> <img :src="signatureUrl" /></div>
                    <div>{{ dayjs(signatureDate).format('YYYY-MM-DD HH:mm:ss') }}</div>
                </div>
            </div>

            <div class="yf-class" v-show="!signatureUrl">
                乙方(签字/手印)：
            </div>
            <contract v-show="!signatureUrl" :id="id" @getInfo="getSignatureDo" :qiniuDomain="qiniuDomain" :signatureUrl="signatureUrl" :type="type">
            </contract>

        </div>
        <div style="height: 100px;width: 100%;"></div>
    </div>
</template>
<script>
import { Toast, MessageBox } from "mint-ui";
import contract from "@/components/contract/index";
import * as api from "@/axios/api";
import dayjs from "dayjs";
export default {
    name: "accountOpeningContractDetail",
    components: {
        contract,
    },
    data() {
        return {
            id: "",
            type: "",
            signatureUrl: "",
            signatureDate: "",
            user: "",
            qiniuDomain: "",
        };
    },
    created() {
        const { id, type } = this.$route.query;
        this.user = this.$store.state.userInfo;
        this.id = id;
        this.type = type;
    },
    async mounted() {
        const data = await api.getInitConfig();
        this.qiniuDomain = data.data.qiniuDomain;
        await this.getSignatureDo();
        this.getUserInfo();
    },
    methods: {
        getUserInfo() {
            // 获取用户信息
            api.getUserInfo().then((res) => {
                if (res.status === 0) {
                    this.user = res.data;
                } else {
                    Toast(res.msg);
                }
            });
        },
        async getSignatureDo() {
            const { data } = await api.getSignature({
                userId: this.id,
                type: this.type,
            });
            if (data) {
                this.signatureUrl = data.signatureMsg;
                this.signatureDate = data.signatureDate;
            }
        },
    },
};
</script>

<style scoped lang="less">
.account-opening-contract-page {
    background: #fff;

    .content-class {
        padding: 20px 20px;
        box-sizing: border-box;

        .yf-class {
            margin-bottom: 20px;
        }

        h3 {
            text-align: center;
            padding: 20px 0;
            font-weight: bolder;
        }

        h4 {
            padding-bottom: 20px;
            font-weight: bolder;
        }

        .bb-class {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;

            .qm-class {
                text-align: center;
                display: flex;
                align-items: flex-end;
                justify-content: center;
                flex-direction: column;
                min-width: 0;
                /* 防止内容溢出 */

                .row-class {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 12px;
                    /* 允许换行 */
                }

                img {
                    width: 50px;
                    /* 稍微缩小图片 */
                }

                span {
                    font-size: 12px;
                    white-space: nowrap;
                    /* 稍微缩小字体 */
                }
            }

            .gz-class {
                height: 300px;
                background: url(~@/assets/me/yz.png);
                background-size: 180px 180px;
                background-repeat: no-repeat;
                background-position: center 30%;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: column;
                min-width: 0;
                /* 防止内容溢出 */
                gap: 30px;
            }
        }

        .row-class {
            margin-bottom: 20px;
            text-indent: 40px;
            line-height: 33px;

            .title-class {
                font-weight: bolder;
            }
        }
    }

    .headf {
        width: 100%;
        height: 1.1748rem;
        background: linear-gradient(-55deg, rgb(223, 48, 28), rgb(223, 48, 28));
        position: sticky;
        top: 0;
    }

    h2 {
        text-align: center;
        height: 1.2549rem;
        width: 100%;
        position: relative;
        line-height: 1.2549rem;
        font-size: 0.4806rem;
        color: #fff;
        background: transparent;
        font-weight: 500;
        z-index: 3;
    }

    .hbnh {
        position: absolute;
        left: 0.4005rem;
        font-size: 0.4272rem;
        font-weight: 500;
    }

    .fan {
        width: 0.2403rem;
        height: 0.4272rem;
        background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAgCAYAAAAffCjxAAAAAXNSR0IArs4c6QAAAVdJREFUSEut1usqBVEYxvH/c1MuRJJDkhwTEpIkJUlyCEk++CJJckpycw49Gs3Wa+y9Z82ePd/Xr5n38KwRNR7bE0APMK1OHdtTwAWQGZcdQbZngPMc+QImK0O2Z4GzgIxLuqoE2Z4DTgMyJuk6K08yZHseOAnIqKSbRo2TINsLwHFARiTdxkaVQrYXgaOADEu6K3a7LWR7CTjMD2XdaYq0rZHtFWA/Rz6BIUn3reau6RvZXgX28kMfOfLQbnj/QbbXgN380HuOPJZtwB/I9jqwE5ABSc9lyJ8a2d4AtgPSL+klBfmFbG8CWwHpk/SaivxAtjMgg7Inq0mvpLcqSHehTOvKpzU+oSvFDlj99gesOJCDkp7Kip+yIknT3XL7C0tbum9lMbIMHKQkQEqwJWVSKZTPWTElq0dt6GYxt6uHf8DqX0cBq39BBqz+lR2w+j8RAfv9rfkGqF24CUdT9E4AAAAASUVORK5CYII=)
            no-repeat 50%;
        background-size: 100%;
        display: inline-block;
        margin-right: 0.1335rem;
        vertical-align: middle;
        margin-top: -0.0534rem;
    }

    .hezi {
        width: 100%;
        border-bottom: 0.0267rem solid #e0e0e0;
        margin: 0 auto;
        margin-top: 1.1748rem;
        text-align: center;
        padding-bottom: 0.534rem;
    }
}
</style>