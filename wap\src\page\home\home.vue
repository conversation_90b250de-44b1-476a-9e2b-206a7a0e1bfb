<template>
    <div class="home-container">
        <!-- 主要内容区域 -->
        <div class="home-main-content">
            <!-- 头部与轮播容器 -->
            <div class="home-header-banner-container">
                <!-- 头部区域 -->
                <div class="home-header">
                    <div class="home-header-left">
                        <img src="~@/assets/images/qiquan26/17336181584430A720474.png" class="home-logo">
                    </div>
                    <!-- 搜索区域 -->
                    <div class="home-search-container">
                        <div class="home-search-box" @click="getsearch()">
                            <div class="home-search-icon">
                                <img src="~@/assets/home/<USER>/3.png" class="home-search-icon-class">
                            </div>
                            <span class="home-search-placeholder">请输入股票代码/简拼</span>
                        </div>
                    </div>
                    <div class="home-header-right">
                        <img src="~@/assets/home/<USER>/12.png" class="home-wifi-icon" @click="getwifi()">
                    </div>
                </div>

                <!-- 轮播区域 -->
                <div class="home-banner-section">
                    <van-swipe :autoplay="3000" indicator-color="#ff6b47" class="home-custom-swipe">
                        <van-swipe-item>
                            <div class="home-banner-item">
                                <img src="~@/assets/images/qiquan26/1.png">
                            </div>
                        </van-swipe-item>
                        <van-swipe-item>
                            <div class="home-banner-item">
                                <img src="~@/assets/images/qiquan26/2.png">
                            </div>
                        </van-swipe-item>
                        <van-swipe-item>
                            <div class="home-banner-item">
                                <img src="~@/assets/images/qiquan26/6.jpg">
                            </div>
                        </van-swipe-item>
                    </van-swipe>
                </div>
            </div>

            <!-- 快捷功能菜单 -->
            <div class="home-quick-menu">
                <div class="home-menu-item" @click="$router.push({ path: '/newshares?type=1' })">
                    <div class="home-menu-icon">
                        <img src="~@/assets/home/<USER>/5.png" alt="">
                    </div>
                    <span>新股申购</span>
                </div>
                <div class="home-menu-item" @click="$router.push({ path: '/newshares?type=2' })">
                    <div class="home-menu-icon">
                        <img src="~@/assets/home/<USER>/6.png" alt="">
                    </div>
                    <span>线下配售</span>
                </div>
                <div class="home-menu-item" @click="$router.push({ path: '/biglist' })">
                    <div class="home-menu-icon">
                        <img src="~@/assets/home/<USER>/7.png" alt="">
                    </div>
                    <span>大宗交易</span>
                </div>
                <div class="home-menu-item" @click="$router.push({ path: '/recharge' })">
                    <div class="home-menu-icon">
                        <img src="~@/assets/home/<USER>/8.png" alt="">
                    </div>
                    <span>银证转账</span>
                </div>
                <div class="home-menu-item" @click="$router.push({ path: '/service' })">
                    <div class="home-menu-icon">
                        <img src="~@/assets/home/<USER>/9.png" alt="">
                    </div>
                    <span>客服</span>
                </div>
            </div>
            <!-- 我的资产 -->
            <div class="home-asset-card">
                <div class="home-asset-header">
                    <div class="home-asset-title">
                        <span class="home-title-text">我的资产</span>
                        <div class="home-title-decoration">
                            <img src="~@/assets/home/<USER>/13.png" class="home-arrow-icon">
                        </div>
                    </div>
                </div>
                <div class="home-asset-data">
                    <div class="home-asset-item">
                        <div class="home-asset-label">总资产</div>
                        <div class="home-asset-value home-asset-total">{{ formattedTotalAsset }}</div>
                    </div>
                    <div class="home-asset-divider"></div>
                    <div class="home-asset-item">
                        <div class="home-asset-label">可用资金</div>
                        <div class="home-asset-value home-asset-available">{{ formattedAvailableAmount }}</div>
                    </div>
                </div>
            </div>

            <!-- 实时大盘 -->
            <div class="home-market-indices">
                <div class="home-section-header">
                    <div class="home-section-title">
                        <span>实时大盘</span>
                        <div class="home-title-star">
                            <img src="~@/assets/home/<USER>/13.png" class="home-arrow-icon">
                        </div>
                    </div>
                </div>
                <div class="home-indices-grid-container">
                    <div class="home-indices-grid-title">
                        <span>主题精选</span>
                        <span>优选产品</span>
                    </div>
                    <div class="home-indices-grid">
                        <div class="home-index-card" v-for="value in hotStockList" :key="value.id">
                            <div class="home-index-name">{{ value.indexName }}</div>
                            <div :class="`home-index-price ${value.floatRate > 0 ? 'home-up' : 'home-down'}`">
                                {{ value.currentPoint }}
                            </div>
                            <div :class="`home-index-change ${value.floatRate > 0 ? 'home-up' : 'home-down'}`">
                                <span class="home-change-value">{{ Number(value.floatPoint).toFixed(2) }}</span>
                                <span class="home-change-percent">{{ value.floatRate }}%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 热门头条 -->
            <div class="home-news-section">
                <div class="home-section-header">
                    <div class="home-section-title home-section-hot-title ">
                        <span>热门头条</span>
                        <div class="home-news-badge">
                            <img src="~@/assets/home/<USER>/13.png" class="home-news-badge-icon">
                        </div>
                    </div>
                </div>
                <div class="home-news-container">
                    <div class="home-news-tabs">
                        <div :class="`home-tab-item ${tabactive == 1 ? 'home-active' : ''}`" @click="getNewsList(1)">
                            财经要闻
                        </div>
                        <div :class="`home-tab-item ${tabactive == 2 ? 'home-active' : ''}`" @click="getNewsList(2)">
                            经济数据
                        </div>
                        <div :class="`home-tab-item ${tabactive == 3 ? 'home-active' : ''}`" @click="getNewsList(3)">
                            7*24全球
                        </div>
                        <div :class="`home-tab-item ${tabactive == 4 ? 'home-active' : ''}`" @click="getNewsList(4)">
                            国际经济
                        </div>
                    </div>
                    <div class="home-news-list">
                        <div class="home-news-item" v-for="(value, index) in newsContent1" :key="value.id"
                            @click="getnewsdetail(value)">
                            <div class="home-news-content">
                                <span class="home-news-tag" v-if="index === 0">焦点</span>
                                <span class="home-news-tag" v-if="index === 1">焦点</span>
                                <span class="home-news-tag home-news-tag-important" v-if="index === 2">重磅</span>
                                <div class="home-news-title">
                                    {{ value.title }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <div class="bottom-blank-area"></div>

        <!-- 未认缴弹窗 -->
        <van-popup 
            v-model="showUnsubscribedPopup" 
            class="unsubscribed-popup"
            :close-on-click-overlay="false"
        >
            <div class="popup-content">
                <div class="popup-header">
                    <h3>恭喜您中签</h3>
                </div>
                
                <div class="popup-body" v-if="unsubscribedData">
                    <div class="dialog-subtitle">中签{{ unsubscribedList.length }}只新股</div>
                    
                    <div class="stock-info">
                        <stock-tag-info :stock-code="getStockGid(unsubscribedData)" />
                        <div class="stock-details">
                            <div class="stock-name">{{ unsubscribedData.newName }}</div>
                        </div>
                    </div>
                    
                    <div class="amount-info">
                        <span>数量：{{ unsubscribedData.applyNumber || 0 }}</span>
                        <span class="amount-highlight">{{ ((unsubscribedData.applyNumber || 0)  * unsubscribedData.buyPrice).toFixed(2)}}</span>
                    </div>
                </div>
                
                <div class="popup-footer">
                    <van-button 
                        class="cancel-btn" 
                        @click="handleCancelPayment"
                    >
                        稍后处理
                    </van-button>
                    <van-button 
                        class="confirm-btn" 
                        type="danger" 
                        @click="handleConfirmPayment"
                    >
                        前往认缴
                    </van-button>
                </div>
            </div>
        </van-popup>
    </div>
</template>

<script>
import * as api from "@/axios/api";
import { init, dispose } from "klinecharts";
import SearchIcon from "@/components/icons/SearchIcon.vue";
import EyeIcon from "@/components/icons/EyeIcon.vue";
import RefreshIcon from "@/components/icons/RefreshIcon.vue";
import ArrowIcon from "@/components/icons/ArrowIcon.vue";
import StockTagInfo from "@/components/stock-tag-info.vue";

export default {
    name: 'HomePage',
    components: {
        SearchIcon,
        EyeIcon,
        RefreshIcon,
        ArrowIcon,
        StockTagInfo
    },
    data() {
        return {
            isShow: false,
            tabactive: 1,
            newsContent1: [],
            noticebar: "",
            hushentiao: "",
            isshow: true,
            hotStockList: [],
            userInfo: null,
            rate: 0,
            showBtn: true,
            onlineService: "",
            kLineChart: null,
            refreshTimer: null,
            showUnsubscribedPopup: false,
            unsubscribedData: null,
            unsubscribedList: [],
            currentRequestId: 0 // 添加请求ID来追踪最新请求
        };
    },

    computed: {
        isUserLoggedIn() {
            return this.$store.state.userInfo && this.$store.state.userInfo.id;
        },

        formattedTotalAsset() {
            const userInfo = this.$store.state.userInfo;
            return this.useFormatMoney(userInfo && userInfo.userAmt ? userInfo.userAmt : 0);
        },

        formattedAvailableAmount() {
            const userInfo = this.$store.state.userInfo;
            return this.useFormatMoney(userInfo && userInfo.enableAmt ? userInfo.enableAmt : 0);
        }
    },

    async mounted() {
        try {
            await this.initializePageData();
            // 页面初始化时调用未认缴列表
            await this.getUnsubscribedList();
        } catch (error) {
            console.error('页面初始化失败:', error);
        }
    },


    beforeDestroy() {
        this.cleanup();
    },

    methods: {
        async initializePageData() {
            // 使用兼容性更好的Promise.all，配合单独的错误处理
            const promises = [
                this.getUserInfo().catch(error => console.error('获取用户信息失败:', error)),
                this.getNewsList(1).catch(error => console.error('获取新闻列表失败:', error)),
                this.stockgetZdfNumber().catch(error => console.error('获取涨跌幅数据失败:', error)),
                this.getListMarket().catch(error => console.error('获取市场数据失败:', error))
            ];

            try {
                await Promise.all(promises);
            } catch (error) {
                console.error('部分数据加载失败:', error);
            }
        },

        cleanup() {
            if (this.kLineChart) {
                dispose("chart-type-k-line");
                this.kLineChart = null;
            }
            if (this.refreshTimer) {
                clearTimeout(this.refreshTimer);
                this.refreshTimer = null;
            }
            this.isshow = false;
        },

        useFormatMoney(price, useCurrencySymbol = true, currency = "CNY") {
            try {
                const options = {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 6,
                };

                if (useCurrencySymbol) {
                    options.style = "currency";
                    options.currency = currency;
                }

                const number = Number(price || 0);
                if (isNaN(number)) {
                    throw new Error(`Invalid input: price must be a number ----> ${price}`);
                }

                return number.toLocaleString(undefined, options);
            } catch (error) {
                console.error('格式化金额失败:', error);
                return '¥0.00';
            }
        },

        async getListMarket() {
            try {
                const params = {
                    pageNum: 1,
                    pageSize: 15,
                };

                const res = await api.getListMarket(params);
                this.hotStockList = res.data.slice(0, 3);
            } catch (error) {
                console.error('获取市场数据失败:', error);
            }
        },

        async getkline(baseTimestamp = Date.now(), basePrice = 5000, dataSize = 800) {
            try {
                const opt = {
                    code: "000001",
                    time: 5,
                    ma: 5,
                    size: 100,
                };

                const data = await api.getMinK_Echarts(opt);
                const klinelist = data.data.values.reverse();
                const dataList = this.formatKLineData(klinelist);

                if (this.kLineChart) {
                    setTimeout(() => {
                        this.kLineChart.applyNewData(dataList);
                    }, 500);
                }

                if (this.isshow) {
                    this.refreshTimer = setTimeout(() => {
                        this.getkline();
                    }, 3000);
                }
            } catch (error) {
                console.error('获取K线数据失败:', error);
            }
        },

        formatKLineData(klinelist) {
            return klinelist.map(item => ({
                open: item[0],
                low: item[2],
                high: item[3],
                close: item[1],
                volume: item[4],
                timestamp: item[5],
            }));
        },

        async getUserInfo() {
            try {
                const data = await api.getUserInfo();

                if (data.status === 0) {
                    this.handleSuccessfulLogin(data.data);
                } else {
                    this.handleLoginRequired();
                }
            } catch (error) {
                console.error('获取用户信息失败:', error);
                this.handleLoginRequired();
            }
        },

        handleSuccessfulLogin(userData) {
            this.$store.commit("dialogVisible", false);
            this.$store.state.userInfo = userData;
            this.userInfo = userData;
            this.rate = (userData.enableAmt / userData.userAmt) * 100;

            if (userData.isActive === 1 || userData.isActive === 2) {
                this.showBtn = false;
            }
        },

        handleLoginRequired() {
            this.$store.commit("dialogVisible", true);
        },

        getsearch() {
            this.$router.push({ path: "/Searchlist" });
        },
        getwifi() {
            this.$router.push({ path: "/speedtest" });
        },
        async stockgetZdfNumber() {
            try {
                const data = await api.stockgetZdfNumber();
                this.hushentiao = data.data;
            } catch (error) {
                console.error('获取涨跌幅数据失败:', error);
            }
        },

        async getNewsList(type) {
            try {
                this.tabactive = type;
                this.newsContent1 = [];
                
                // 生成新的请求ID
                this.currentRequestId = Date.now();
                const requestId = this.currentRequestId;

                const data = await api.queryNewsList(type);
                
                // 检查是否为最新请求
                if (requestId !== this.currentRequestId) {
                    return; // 忽略过时的响应
                }
                
                this.newsContent1 = data.data.list;
                this.noticebar = data.data.list && data.data.list[0] && data.data.list[0].title ? data.data.list[0].title : '';
            } catch (error) {
                console.error('获取新闻列表失败:', error);
            }
        },

        getnewsdetail(item) {
            this.$router.push({
                path: "/newPage",
                query: { listid: item.id },
            });
        },

        getHeaderlink(val) {
            const routeMap = {
                0: "/MyList",
                1: "/trading-list",
                2: "/about?e=6",
                3: "/about?e=6",
                4: "/service",
                5: "/openaccount"
            };

            const path = routeMap[val];
            if (path) {
                this.$router.push({ path });
            }
        },

        getHeaderlink1(val) {
            const routeMap = {
                1: "/Subscription?idx=1",
                2: "/Subscription?idx=3",
                3: "/Subscription?idx=4",
                4: "/Subscription?idx=5",
                5: "/Subscription?idx=2",
                6: "/DragonTiger",
                7: "/stopRecovery",
                8: "/topTen",
                9: "/daylimit",
                10: "/about?e=5"
            };

            const path = routeMap[val];
            if (path) {
                this.$router.push({ path });
            }
        },

        async getInfoSite() {
            try {
                const data = await api.getInfoSite();

                if (data.status === 0) {
                    this.onlineService = data.data.onlineService;
                } else {
                    this.showErrorAlert(data.msg);
                }
            } catch (error) {
                console.error('获取站点信息失败:', error);
                this.showErrorAlert('获取站点信息失败');
            }
        },

        showErrorAlert(message) {
            this.$store.commit("elAlertShow", {
                elAlertShow: true,
                elAlertText: message,
            });
        },

        async getUnsubscribedList() {
            try {
                const data = await api.getUnsubscribed();
                if (data.status === 0) {
                    console.log('未认缴列表数据:', data.data);
                    
                    // 如果有未认缴数据，检查是否需要弹出提醒弹框
                    if (data.data && data.data.length > 0) {
                        this.unsubscribedList = data.data;
                        if (this.shouldShowPopup()) {
                            this.showUnsubscribedDialog(data.data[0]);
                        }
                    }
                } else {
                    console.error('获取未认缴列表失败:', data.msg);
                }
            } catch (error) {
                console.error('获取未认缴列表请求失败:', error);
            }
        },

        // 判断是否应该显示弹窗
        shouldShowPopup() {
            const now = new Date();
            const currentHour = now.getHours();
            const today = now.toDateString(); // 获取今天的日期字符串
            
            // 定义三个时间段: 9-10点, 13-14点, 17-18点
            const timeSlots = [
                { start: 9, end: 10, key: 'morning' },    // 早上9-10点
                { start: 13, end: 14, key: 'afternoon' }, // 中午13-14点
                { start: 17, end: 18, key: 'evening' }    // 晚上17-18点
            ];
            
            // 检查当前是否在任一时间段内
            const currentSlot = timeSlots.find(slot => 
                currentHour >= slot.start && currentHour < slot.end
            );
            
            if (!currentSlot) {
                console.log('当前不在弹窗时间段内');
                return false;
            }
            
            // 检查今天这个时间段是否已经弹过
            const storageKey = `popup_shown_${currentSlot.key}`;
            const lastShownDate = localStorage.getItem(storageKey);
            
            if (lastShownDate === today) {
                console.log(`今天${currentSlot.key}时间段已经弹过弹窗了`);
                return false;
            }
            
            // 记录本次弹窗
            localStorage.setItem(storageKey, today);
            console.log(`显示${currentSlot.key}时间段弹窗`);
            return true;
        },

        showUnsubscribedDialog(item) {
            this.unsubscribedData = item;
            this.showUnsubscribedPopup = true;
        },

        handleConfirmPayment() {
            this.showUnsubscribedPopup = false;
            // 跳转到配售记录页面，默认显示中签标签页（index=1对应status=3）
            this.$router.push({
                path: '/peishouhistory',
                query: { 
                    type: 1,
                    tab: 1  // 指定跳转到中签标签页
                }
            });
        },

        handleCancelPayment() {
            this.showUnsubscribedPopup = false;
            console.log('用户选择稍后处理');
        },

        formatPrice(price) {
            if (!price && price !== 0) return '0.00';
            return parseFloat(price).toFixed(2);
        },

        // 获取股票GID（用于标签组件）
        getStockGid(item) {
            if (!item || !item.newType) return '';

            const typeMap = {
                '深': 'sz',
                '创': 'sz',
                '沪': 'sh',
                '科': 'sh',
                '北': 'bj'
            };

            const prefix = typeMap[item.newType] || 'sz';
            return `${prefix}${item.newCode}`;
        }
    },
};
</script>

<style lang="less" scoped>
// 主容器
.home-container {
    // 请填写样式
    // padding: 10px 16px;
}

.home-main-content {
    // 请填写样式

}

// 头部与轮播容器
.home-header-banner-container {
    padding: 10px 16px;
    // 请填写样式
    position: relative;

    &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 180px;
        background: url("~@/assets/home/<USER>/1.png") no-repeat center center;
        background-size: 100% 100%;
    }
}

// 头部区域
.home-header {
    // 请填写样式
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
    box-sizing: border-box;
    z-index: 1;
    position: relative;

}

.home-header-left {
    // 请填写样式
}

.home-header-right {
    // 请填写样式
}

.home-logo {
    // 请填写样式
    width: 121px;
    height: 32px;
}

.home-wifi-icon {
    // 请填写样式
    width: 24px;
    height: 24px;
}

// 搜索区域
.home-search-container {
    // 请填写样式

}

.home-search-box {
    // 请填写样式
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 6px;
    border-radius: 100px;

    border: 1px solid rgba(99, 99, 99, 1);

    padding: 6px 8px;
}

.home-search-icon {
    // 请填写样式
}

.home-search-icon-class {
    width: 20px;
    height: 20px;
}

.home-search-placeholder {
    // 请填写样式
    /** 文本1 */
    font-size: 12px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 16.46px;
    color: rgba(117, 117, 117, 1);
    text-align: left;
    vertical-align: top;

}

// 资产卡片
.home-asset-card {
    // 请填写样式
    border-radius: 10px;
    background: rgba(255, 255, 255, 1);

    border: 1px solid rgba(255, 255, 255, 1);
    margin: 16px;
    padding: 10px 12px;
}

.home-asset-header {
    // 请填写样式
    padding-bottom: 5px;



}

.home-asset-title {
    // 请填写样式
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 3px;
}

.home-title-text {
    // 请填写样式

    /** 文本1 */
    font-size: 18px;
    font-weight: bold;
    letter-spacing: 0px;
    line-height: 24.7px;
    color: rgba(29, 36, 51, 1);

}

.home-title-decoration {
    // 请填写样式
}

.home-arrow-icon {
    // 请填写样式
    width: 14px;
    height: 14px;
}

.home-asset-eye {
    // 请填写样式
}

.home-asset-data {

    border-radius: 10px;
    background: rgba(252, 252, 252, 1);
    padding: 10px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    // 请填写样式
}

.home-asset-item {
    // 请填写样式
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    flex-direction: column;
    gap: 4px;
}

.home-asset-label {
    // 请填写样式


    /** 文本1 */
    font-size: 12px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 16.46px;
    color: rgba(29, 36, 51, 1);
    text-align: left;

}

.home-asset-value {
    // 请填写样式


    /** 文本1 */
    font-size: 16px;
    font-weight: bolder;
    letter-spacing: 0px;
    line-height: 23.17px;
    color: rgba(29, 36, 51, 1);
    text-align: left;
    vertical-align: top;

}

.home-asset-total {
    // 请填写样式
}

.home-asset-available {
    // 请填写样式
}

.home-asset-divider {
    // 请填写样式
}

// 快捷菜单
.home-quick-menu {
    // 请填写样式
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    justify-content: center;
    align-items: center;
    gap: 10px;
    padding: 0 16px;

}

.home-menu-item {
    // 请填写样式
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 7px;

    span {


        /** 文本1 */
        font-size: 12px;
        font-weight: 400;
        letter-spacing: 0px;
        line-height: 17.38px;
        color: rgba(29, 36, 51, 1);

    }
}

.home-menu-icon {

    // 请填写样式
    img {
        width: 24px;
        height: 24px;
    }
}

// 市场指数
.home-market-indices {
    // 请填写样式
    padding: 10px;
    border-radius: 10px;
    background: rgba(254, 244, 232, 1);

    border: 1px solid rgba(255, 255, 255, 1);
    margin: 0 16px;

}

.home-section-header {
    // 请填写样式
    margin-bottom: 10px;
}

.home-section-hot-title {
    padding: 10px 20px;

}

.home-section-title {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 3px;

    // 请填写样式
    span {

        /** 文本1 */
        font-size: 18px;
        font-weight: bold;
        letter-spacing: 0px;
        line-height: 24.7px;
        color: rgba(29, 36, 51, 1);
        text-align: left;

    }
}

.home-title-star {
    // 请填写样式
}

.home-star-icon {
    // 请填写样式
}

.home-refresh-btn {
    // 请填写样式
}

.home-indices-grid-container {

    border-radius: 10px;
    background: rgba(254, 254, 254, 1);

    border: 2px solid linear-gradient(180deg, rgba(236, 213, 184, 1) 0%, rgba(254, 254, 254, 1) 100%);
    padding: 13px 10px;
    position: relative;
    overflow: visible;

    &::before {
        content: "";
        position: absolute;
        top: -38px;
        right: 0;
        width: 77px;
        height: 77px;
        background: url("~@/assets/home/<USER>/10.png") no-repeat center center;
        background-size: 100% 100%;
        z-index: 0;
    }

    &::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(254, 254, 254, 1);
        border-radius: 10px;
        z-index: 1;
    }
}

.home-indices-grid {
    // 请填写样式
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 9px;
    justify-content: center;
    align-items: center;
    position: relative;
    z-index: 2;
}

.home-indices-grid-title {
    position: relative;
    z-index: 2;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 10px;
    /** 文本1 */
    margin-bottom: 10px;

    span {

        opacity: 1;
        border-radius: 2px;
        background: rgba(249, 243, 238, 1);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 0px 4px 0px 4px;


        /** 文本1 */
        font-size: 10px;
        font-weight: 400;
        letter-spacing: 0px;
        line-height: 14.48px;
        color: rgba(190, 155, 110, 1);

    }
}

.home-index-card {
    // 请填写样式
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 4px;
    border-radius: 10px;
    background: rgba(248, 248, 248, 1);
    padding: 14px 10px;

}

.home-index-name {
    // 请填写样式

    /** 文本1 */
    font-size: 12px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 16.46px;
    color: rgba(29, 36, 51, 1);

}

.home-index-price {
    // 请填写样式


    /** 文本1 */
    font-size: 16px;
    font-weight: bold;
    letter-spacing: 0px;
    line-height: 19.38px;
    text-align: left;
    vertical-align: top;

}

.home-index-change {
    // 请填写样式


    /** 文本1 */
    font-size: 10px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 12.11px;
    text-align: left;
    vertical-align: top;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;

}

.home-up {
    // 请填写样式 - 上涨颜色（红色）
    color: rgba(235, 90, 83, 1);

}

.home-down {
    // 请填写样式 - 下跌颜色（绿色）
    color: rgba(0, 164, 68, 1);

}

.home-change-value {
    // 请填写样式
}

.home-change-percent {
    // 请填写样式
}

// 轮播横幅
.home-banner-section {
    // 请填写样式
    margin-top: 18px;
}

.home-custom-swipe {
    // 请填写样式
}

.home-banner-item {
    // 请填写样式
    display: flex;
    justify-content: center;
    align-items: center;

    img {
        width: 343px;
        height: 144px;
        border-radius: 10px;
    }
}

// 资讯新闻
.home-news-section {
    // 请填写样式
    margin: 0 16px;

    opacity: 1;
    border-radius: 10px;
    background: rgba(255, 240, 240, 1);

    border: 1px solid rgba(255, 255, 255, 1);
    margin-bottom: 88px;

}

.home-news-badge {
    // 请填写样式
}

.home-news-badge-icon {
    // 请填写样式
    width: 14px;
    height: 14px;
}

.home-news-container {
    margin: 10px;

    border-radius: 10px;
    background: rgba(254, 254, 254, 1);

    border: 2px solid linear-gradient(180deg, rgba(237, 185, 185, 1) 0%, rgba(254, 254, 254, 1) 100%);

    padding: 10px;

    position: relative;
    overflow: visible;

    &::before {
        content: "";
        position: absolute;
        top: -48px;
        right: 0;
        width: 98px;
        height: 98px;
        background: url("~@/assets/home/<USER>/11.png") no-repeat center center;
        background-size: 100% 100%;
        z-index: 0;
    }

    &::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(254, 254, 254, 1);
        border-radius: 10px;
        z-index: 1;
    }

}

.home-news-tabs {
    // 请填写样式
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    margin-bottom: 15px;
}

.home-tab-item {
    // 请填写样式

    display: flex;

    /** 文本1 */
    font-size: 12px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 16.46px;
    color: rgba(61, 61, 61, 1);
    justify-content: center;
    align-items: center;

    background: rgba(173, 173, 173, 0.05);
    padding: 4px;



}

.home-active {
    // 请填写样式 - 激活状态的tab
    color: rgba(224, 57, 54, 1);
    background: rgba(224, 57, 54, 0.05);
}

.home-news-list {
    // 请填写样式
    position: relative;
    z-index: 2;
}

.home-news-item {
    // 请填写样式
    margin-bottom: 10px;
}

.home-news-content {
    // 请填写样式
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;

}

.home-news-title {
    // 请填写样式


    /** 文本1 */
    font-size: 12px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 16.46px;
    color: rgba(29, 36, 51, 1);
    text-align: left;

}

.home-news-tag {
    display: inline-block;
    padding: 2px 6px;
    margin-right: 6px;


    /** 文本1 */
    font-size: 10px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 13.72px;
    color: rgba(235, 90, 83, 1);


    border-radius: 4px;

    border: 1px solid rgba(224, 57, 54, 1);

    padding: 2px 4px 2px 4px;
    white-space: nowrap;


}

.home-news-tag-important {
    // background: rgba(255, 165, 0, 1);
}

.home-news-meta {
    // 请填写样式
}

.home-news-time {
    // 请填写样式
}

.home-news-hot {
    // 请填写样式
}

.home-hot-badge-icon {
    // 请填写样式
}

.home-news-arrow {
    // 请填写样式
}


// 头部区域

/* 未认缴弹窗样式 */
.unsubscribed-popup {
    border-radius: 12px;
    overflow: hidden;
    width: 320px;
}

.popup-content {
    background: #fff;
    
    .popup-header {
        padding: 24px 24px 16px;
        text-align: center;
        
        h3 {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin: 0;
        }
    }
    
    .popup-body {
        padding: 0 24px 24px;
        text-align: center;
        
        .dialog-subtitle {
            font-size: 14px;
            color: #666;
            margin-bottom: 20px;
        }
        
        .stock-info {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            margin-bottom: 20px;
            
            .stock-details {
                text-align: left;
                
                .stock-name {
                    font-size: 16px;
                    font-weight: bold;
                    color: #333;
                    margin-bottom: 4px;
                }
                
                .stock-code {
                    font-size: 14px;
                    color: #666;
                }
            }
        }
        
        .amount-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background: #f5f5f5;
            border-radius: 8px;
            
            span {
                font-size: 14px;
                color: #333;
            }
            
            .amount-highlight {
                font-size: 18px;
                font-weight: bold;
                color: #f44336;
            }
        }
    }
    
    .popup-footer {
        padding: 16px 24px 24px;
        display: flex;
        gap: 12px;
        
        .van-button {
            flex: 1;
            border-radius: 8px;
            height: 44px;
            font-size: 16px;
            font-weight: 500;
        }
        
        .cancel-btn {
            background: #f5f5f5 !important;
            border: none !important;
            color: #666 !important;
        }
        
        .confirm-btn {
            background: #f44336 !important;
            border: none !important;
            color: #fff !important;
        }
    }
}
</style>