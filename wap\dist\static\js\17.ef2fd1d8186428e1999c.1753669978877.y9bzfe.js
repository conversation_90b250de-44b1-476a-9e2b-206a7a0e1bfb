/* auto-build-1753669979406-rdxg2j */
webpackJsonp([17],{BCj1:function(t,e){},CxV0:function(t,e,s){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=s("//Fk"),a=s.n(n),r=s("Xxa5"),i=s.n(r),c=s("exGp"),o=s.n(c),u=s("Gu7T"),l=s.n(u),d=s("eOoE"),v=s("Fd2+"),p=s("/5sW"),f=s("rAph"),h={name:"SpeedTest",data:function(){return{loading:!1,connectionStatus:"已连接",connectionClass:"status-connected",currentBaseUrl:"",speedTestResults:[]}},computed:{apiSpeedTestResults:function(){return this.$apiSpeedTestResults||[]}},watch:{apiSpeedTestResults:{handler:function(t){t&&t.length>0&&(this.speedTestResults=[].concat(l()(t)),this.updateConnectionStatus())},deep:!0}},mounted:function(){this.currentBaseUrl=this.$currentBaseUrl||d.a.defaults.baseURL||"未知服务器",this.$apiSpeedTestResults&&this.$apiSpeedTestResults.length>0?(this.speedTestResults=[].concat(l()(this.$apiSpeedTestResults)),this.updateConnectionStatus()):this.refreshTest()},methods:{refreshTest:function(){var t=this;return o()(i.a.mark(function e(){var s,n,r,c,u,l;return i.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t.loading=!0,e.prev=1,0!==(s=t.$apiUrls||f.a.API_URLS||[]).length){e.next=7;break}return v.x.fail("无可用服务器地址"),t.loading=!1,e.abrupt("return");case 7:return n=[],r=null,c=1/0,u=s.map(function(){var e=o()(i.a.mark(function e(s){var a,o,u,l,v,p,h;return i.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return a=performance.now(),e.prev=1,e.next=4,d.a.get(""+s+(t.$apiHealthCheckPath||f.a.HEALTH_CHECK_PATH),{timeout:2e4,withCredentials:!1});case 4:o=e.sent,u=performance.now(),l=u-a,v=o.data&&o.data.data===(t.$apiHealthCheckResponse||f.a.HEALTH_CHECK_RESPONSE),p={url:s,responseTime:l,status:v?"可用":"不可用",isValid:v},n.push(p),v&&l<c&&(r=s,c=l),e.next=17;break;case 13:e.prev=13,e.t0=e.catch(1),h=performance.now(),n.push({url:s,responseTime:h-a,status:"不可用",error:e.t0.message,isValid:!1});case 17:case"end":return e.stop()}},e,t,[[1,13]])}));return function(t){return e.apply(this,arguments)}}()),e.next=13,a.a.all(u);case 13:l=n.sort(function(t,e){return t.responseTime-e.responseTime}),t.speedTestResults=l,p.default.prototype.$apiSpeedTestResults=l,t.$apiSpeedTestResults=l,t.updateConnectionStatus(),r&&r!==t.currentBaseUrl?v.e.confirm({title:"发现更快的服务器",message:"已发现响应更快的服务器："+t.getServerNameByUrl(r)+"，是否切换？",confirmButtonText:"切换",cancelButtonText:"保持当前",confirmButtonColor:"#f6020c"}).then(function(){t.setAsDefault(r)}).catch(function(){v.x.success("测速完成")}):v.x.success("测速完成"),e.next=25;break;case 21:e.prev=21,e.t0=e.catch(1),console.error("测速失败:",e.t0),v.x.fail("测速失败，请稍后重试");case 25:return e.prev=25,t.loading=!1,e.finish(25);case 28:case"end":return e.stop()}},e,t,[[1,21,25,28]])}))()},updateConnectionStatus:function(){var t=this,e=this.speedTestResults.find(function(e){return e.url===t.currentBaseUrl});if(!e)return this.connectionStatus="未知状态",void(this.connectionClass="status-unknown");e.isValid?e.responseTime<200?(this.connectionStatus="极速连接",this.connectionClass="status-excellent"):e.responseTime<500?(this.connectionStatus="良好连接",this.connectionClass="status-good"):e.responseTime<1e3?(this.connectionStatus="正常连接",this.connectionClass="status-normal"):(this.connectionStatus="缓慢连接",this.connectionClass="status-slow"):(this.connectionStatus="连接异常",this.connectionClass="status-error")},setAsDefault:function(t){var e=this;t&&v.e.confirm({title:"设置默认服务器",message:"确定要将 "+this.getServerNameByUrl(t)+" 设置为默认服务器吗？",confirmButtonText:"确定",cancelButtonText:"取消",confirmButtonColor:"#f6020c"}).then(function(){try{d.a.defaults.baseURL=t,p.default.prototype.$currentBaseUrl=t,"undefined"!=typeof window&&(window.$currentBaseUrl=t),e.currentBaseUrl=t,e.updateConnectionStatus(),v.x.success("服务器已切换")}catch(t){console.error("设置服务器失败:",t),v.x.fail("设置失败，请重试")}}).catch(function(){})},formatUrl:function(t){if(!t)return"未知";try{return new URL(t).hostname}catch(e){return t.replace(/^https?:\/\//,"").split("/")[0]}},getCurrentServerName:function(){var t=this;if(!this.currentBaseUrl)return"未知线路";var e=this.speedTestResults.findIndex(function(e){return e.url===t.currentBaseUrl});if(-1!==e)return"线路"+(e+1);var s=(this.$apiUrls||f.a.API_URLS||[]).findIndex(function(e){return e===t.currentBaseUrl});return-1!==s?"线路"+(s+1):"未知线路"},getServerName:function(t,e){return t?"线路"+(e+1):"未知线路"},getServerNameByUrl:function(t){if(!t)return"未知线路";var e=this.speedTestResults.findIndex(function(e){return e.url===t});if(-1!==e)return"线路"+(e+1);var s=(this.$apiUrls||f.a.API_URLS||[]).findIndex(function(e){return e===t});return-1!==s?"线路"+(s+1):"未知线路"}}},_={render:function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"speed-test-container"},[s("div",{staticClass:"header"},[s("van-nav-bar",{attrs:{title:"网络测速","left-arrow":""},on:{"click-left":function(e){return t.$router.go(-1)}},scopedSlots:t._u([{key:"left",fn:function(){return[s("van-icon",{attrs:{name:"arrow-left",size:"20",color:"#333"}})]},proxy:!0},{key:"title",fn:function(){return[s("span",{staticClass:"nav-title"},[t._v("网络连接测速")])]},proxy:!0}])})],1),t._v(" "),s("div",{staticClass:"content-wrapper"},[s("div",{staticClass:"server-card"},[s("div",{staticClass:"card-header"},[s("div",{staticClass:"card-title"},[s("van-icon",{attrs:{name:"setting-o"}}),t._v(" "),s("span",[t._v("当前服务器")])],1),t._v(" "),s("div",{staticClass:"refresh-btn",on:{click:t.refreshTest}},[s("van-icon",{attrs:{name:"replay"}}),t._v(" "),s("span",[t._v("重新测试")])],1)]),t._v(" "),s("div",{staticClass:"server-info"},[s("div",{staticClass:"server-url"},[t._v(t._s(t.getCurrentServerName()||"正在加载..."))]),t._v(" "),s("div",{staticClass:"connection-status",class:t.connectionClass},[s("div",{staticClass:"status-dot"}),t._v(" "),s("span",[t._v(t._s(t.connectionStatus))])])])]),t._v(" "),s("div",{staticClass:"servers-list-card"},[s("div",{staticClass:"card-header"},[s("div",{staticClass:"card-title"},[s("van-icon",{attrs:{name:"cluster-o"}}),t._v(" "),s("span",[t._v("全部服务器")])],1),t._v(" "),s("div",{staticClass:"servers-count"},[t._v("共 "+t._s(t.speedTestResults.length)+" 个")])]),t._v(" "),s("div",{staticClass:"servers-table"},[s("div",{staticClass:"mobile-server-list"},[t.loading?s("div",{staticClass:"loading-state"},[s("van-loading",{attrs:{type:"spinner",color:"#f6020c"}}),t._v(" "),s("span",[t._v("正在测速中...")])],1):t._l(t.speedTestResults,function(e,n){return s("div",{key:n,staticClass:"server-item",class:[e.url===t.currentBaseUrl?"current-server":"",e.isValid?"":"invalid-server"]},[s("div",{staticClass:"server-item-rank"},[s("span",{staticClass:"rank-number"},[t._v(t._s(n+1))])]),t._v(" "),s("div",{staticClass:"server-item-content"},[s("div",{staticClass:"server-item-header"},[s("div",{staticClass:"server-name"},[t._v(t._s(t.getServerName(e.url,n)))]),t._v(" "),s("div",{staticClass:"server-status",class:e.isValid?"status-ok":"status-fail"},[t._v("\n                                        "+t._s(e.status)+"\n                                    ")])]),t._v(" "),s("div",{staticClass:"server-item-details"},[s("div",{staticClass:"response-time"},[s("van-icon",{attrs:{name:"clock-o"}}),t._v(" "),s("span",[t._v(t._s(Math.round(e.responseTime))+"ms")])],1),t._v(" "),s("div",{staticClass:"server-action"},[s("van-button",{attrs:{type:"primary",size:"small",disabled:e.url===t.currentBaseUrl||!e.isValid},on:{click:function(s){return t.setAsDefault(e.url)}}},[t._v("\n                                            "+t._s(e.url===t.currentBaseUrl?"当前使用":"设为默认")+"\n                                        ")])],1)])])])})],2)])]),t._v(" "),s("div",{staticClass:"speed-info-card"},[s("div",{staticClass:"card-header"},[s("div",{staticClass:"card-title"},[s("van-icon",{attrs:{name:"info-o"}}),t._v(" "),s("span",[t._v("测速说明")])],1)]),t._v(" "),t._m(0)])])])},staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"info-content"},[e("p",[this._v("为提供更佳的交易体验，系统会自动选择响应速度最快的服务器。您可以通过本页面查看当前网络环境下各服务器的连接速度。")]),this._v(" "),e("p",[this._v('如果您遇到交易延迟或断连问题，建议点击"重新测试"按钮刷新测速结果。')])])}]};var C=s("VU/8")(h,_,!1,function(t){s("BCj1")},"data-v-50193ea2",null);e.default=C.exports}});
/* auto-build-1753669979406-rdxg2j */