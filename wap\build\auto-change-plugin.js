class AutoChangePlugin {
    constructor(options = {}) {
        this.options = {
            // 是否在JS文件中添加随机注释
            addRandomComments: true,
            // 是否修改CSS文件
            modifyCSS: true,
            // 注释前缀
            commentPrefix: options.commentPrefix || 'build-marker',
            ...options
        };
    }

    apply(compiler) {
        const buildTimestamp = Date.now();
        const buildId = Math.random().toString(36).substring(7);

        // 兼容webpack 3.x的API
        compiler.plugin('compilation', (compilation) => {
            compilation.plugin('optimize-chunk-assets', (chunks, callback) => {
                const randomComment = `/* ${this.options.commentPrefix}-${buildTimestamp}-${buildId} */`;
                const randomCSSComment = `/* build-${buildTimestamp}-${buildId} */`;

                chunks.forEach(chunk => {
                    chunk.files.forEach(filename => {
                        if (this.options.addRandomComments && filename.endsWith('.js')) {
                            // 在JS文件开头添加随机注释
                            const source = compilation.assets[filename].source();
                            const newSource = `${randomComment}\n${source}\n${randomComment}`;
                            compilation.assets[filename] = {
                                source: () => newSource,
                                size: () => newSource.length
                            };
                        }

                        if (this.options.modifyCSS && filename.endsWith('.css')) {
                            // 在CSS文件中添加随机注释
                            const source = compilation.assets[filename].source();
                            const newSource = `${randomCSSComment}\n${source}\n${randomCSSComment}`;
                            compilation.assets[filename] = {
                                source: () => newSource,
                                size: () => newSource.length
                            };
                        }
                    });
                });

                callback();
            });
        });
    }
}

module.exports = AutoChangePlugin; 