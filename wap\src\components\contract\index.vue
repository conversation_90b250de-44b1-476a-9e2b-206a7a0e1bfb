<template>
    <div class="signature-container">
        <!-- 签名区域标题 -->
        <div class="signature-header">
            <div class="header-icon">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"
                        fill="currentColor" />
                </svg>
            </div>
            <div class="header-text">
                <h3>电子签名</h3>
                <p>{{ signatureUrl ? '已完成签名' : '请在下方区域内签署您的姓名' }}</p>
            </div>
            <div class="signature-status" :class="{ 'completed': signatureUrl }">
                <svg v-if="signatureUrl" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" stroke="currentColor" stroke-width="2"
                        stroke-linecap="round" stroke-linejoin="round" />
                </svg>
                <svg v-else viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" stroke="currentColor" stroke-width="2"
                        stroke-linecap="round" stroke-linejoin="round" />
                </svg>
            </div>
        </div>

        <!-- 签名画布区域 -->
        <div class="signature-pad-wrapper">
            <!-- 已完成的签名显示 -->
            <div v-if="signatureUrl" class="signature-display">
                <img :src="signatureUrl" alt="签名" class="signature-image">
                <div class="signature-overlay">
                    <div class="signature-badge">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" stroke="currentColor"
                                stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                        </svg>
                        <span>签名完成</span>
                    </div>
                </div>
            </div>

            <!-- 签名画布 -->
            <div v-else class="signature-pad-container">
                <canvas ref="canvas" class="signature-canvas" @mousedown="startDrawing" @mousemove="draw"
                    @mouseup="stopDrawing" @mouseleave="stopDrawing" @touchstart="startDrawing" @touchmove="draw"
                    @touchend="stopDrawing"></canvas>

                <!-- 画布指导线 -->
                <div class="canvas-guide">
                    <div class="guide-line"></div>
                    <div class="guide-text">请在此区域签名</div>
                </div>

                <!-- 绘制状态指示器 -->
                <div v-if="drawing" class="drawing-indicator">
                    <div class="indicator-dot"></div>
                    <span>正在绘制...</span>
                </div>
            </div>
        </div>

        <!-- 操作按钮区域 -->
        <div class="signature-actions" v-if="!signatureUrl">
            <button class="action-button clear-button" @click="clear" :disabled="!isDrawable || imgloading">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M6 7h12l-1 10H7L6 7zM8 3h8v2H8V3z" stroke="currentColor" stroke-width="2"
                        stroke-linecap="round" stroke-linejoin="round" />
                </svg>
                <span>清除重写</span>
            </button>

            <button class="action-button save-button" @click="save" :disabled="!isDrawable || imgloading">
                <!-- 加载状态 -->
                <div v-if="imgloading" class="loading-spinner">
                    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                            stroke-dasharray="60" stroke-dashoffset="60">
                            <animate attributeName="stroke-dashoffset" dur="2s" values="60;0;60"
                                repeatCount="indefinite" />
                        </circle>
                    </svg>
                </div>
                <!-- 正常状态 -->
                <svg v-else viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M19 13v6a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h6m4-3l4 4-8 8H9v-4l8-8z"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
                <span>{{ imgloading ? '保存中...' : '确认签名' }}</span>
            </button>
        </div>

        <!-- 提示信息 -->
        <div class="signature-tips" v-if="!signatureUrl">
            <div class="tip-item">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" stroke="currentColor"
                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
                <span>请使用真实姓名进行签名</span>
            </div>
            <div class="tip-item">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
                <span>支持手写笔和触屏操作</span>
            </div>
        </div>
    </div>
</template>

<script>
import * as api from "@/axios/api";
import { Toast } from "mint-ui";
import * as qiniu from "qiniu-js";

export default {
    props: {
        signatureUrl: {
            type: String,
            default: null,
        },
        qiniuDomain: {
            type: String,
            default: null,
        },
        type: {
            type: [String, Number],
            default: "",
        },
        id: {
            type: [String, Number],
            default: "",
        },
    },
    data() {
        return {
            drawing: false,
            context: null,
            isDrawable: false,
            imgloading: false,
            lastX: 0,
            lastY: 0,
        };
    },
    async mounted() {
        this.resizeCanvas();
        window.addEventListener("resize", this.resizeCanvas);
        if (this.signatureUrl) {
            this.loadSignature();
        }
    },
    beforeDestroy() {
        window.removeEventListener("resize", this.resizeCanvas);
    },
    methods: {
        resizeCanvas() {
            const canvas = this.$refs.canvas;
            if (!canvas) return;

            const container = canvas.parentElement;
            const dpr = window.devicePixelRatio || 1;

            // 设置画布实际尺寸
            canvas.width = container.offsetWidth * dpr;
            canvas.height = container.offsetHeight * dpr;

            // 设置画布显示尺寸
            canvas.style.width = container.offsetWidth + 'px';
            canvas.style.height = container.offsetHeight + 'px';

            this.context = canvas.getContext("2d");
            this.context.scale(dpr, dpr);
            this.context.strokeStyle = "#1a365d";
            this.context.lineWidth = 3;
            this.context.lineCap = "round";
            this.context.lineJoin = "round";
            this.context.imageSmoothingEnabled = true;

            if (this.signatureUrl) {
                this.loadSignature();
            }
        },
        loadSignature() {
            const canvas = this.$refs.canvas;
            if (!canvas) return;

            const context = this.context;
            const img = new Image();
            img.crossOrigin = "anonymous";
            img.src = this.signatureUrl;
            img.onload = () => {
                context.clearRect(0, 0, canvas.width, canvas.height);
                context.drawImage(img, 0, 0, canvas.offsetWidth, canvas.offsetHeight);
                this.isDrawable = true;
            };
        },
        getEventPosition(event) {
            const canvas = this.$refs.canvas;
            const rect = canvas.getBoundingClientRect();

            if (event.touches && event.touches.length > 0) {
                return {
                    x: event.touches[0].clientX - rect.left,
                    y: event.touches[0].clientY - rect.top,
                };
            }

            return {
                x: event.clientX - rect.left,
                y: event.clientY - rect.top,
            };
        },
        startDrawing(event) {
            if (this.signatureUrl || this.imgloading) return;

            event.preventDefault();
            this.drawing = true;

            const pos = this.getEventPosition(event);
            this.lastX = pos.x;
            this.lastY = pos.y;

            this.context.beginPath();
            this.context.moveTo(pos.x, pos.y);
            this.isDrawable = true;
        },
        draw(event) {
            if (!this.drawing || this.signatureUrl || this.imgloading) return;

            event.preventDefault();
            const pos = this.getEventPosition(event);

            // 使用二次贝塞尔曲线让线条更平滑
            const midX = (this.lastX + pos.x) / 2;
            const midY = (this.lastY + pos.y) / 2;

            this.context.quadraticCurveTo(this.lastX, this.lastY, midX, midY);
            this.context.stroke();

            this.lastX = pos.x;
            this.lastY = pos.y;
        },
        stopDrawing() {
            if (!this.drawing || this.signatureUrl || this.imgloading) return;

            this.drawing = false;
            this.context.closePath();
        },
        clear() {
            if (this.signatureUrl || this.imgloading) return;

            const canvas = this.$refs.canvas;
            this.context.clearRect(0, 0, canvas.offsetWidth, canvas.offsetHeight);
            this.isDrawable = false;

            // 添加清除动画效果
            canvas.style.animation = 'clearFlash 0.3s ease-out';
            setTimeout(() => {
                canvas.style.animation = '';
            }, 300);
        },
        async save() {
            if (this.signatureUrl || this.imgloading || !this.isDrawable) return;

            const canvas = this.$refs.canvas;
            const dataURL = canvas.toDataURL("image/png", 0.9);
            const file = this.dataURLtoFile(dataURL, "signature.png");

            this.imgloading = true;

            try {
                const url = await this.upqiniu(file);
                if (!url) {
                    this.imgloading = false;
                    return;
                }

                const formData = {
                    userId: this.id,
                    imgUrl: url,
                    type: this.type,
                };

                const res = await api.adminupload(formData);
                if (res.status == 0) {
                    Toast({
                        message: "签名保存成功",
                        iconClass: "mintui mintui-success"
                    });
                    this.$emit("getInfo");
                } else {
                    Toast({
                        message: "签署失败，请重试",
                        iconClass: "mintui mintui-field-error"
                    });
                }
            } catch (error) {
                console.error('Save signature error:', error);
                Toast({
                    message: "保存失败，请检查网络连接",
                    iconClass: "mintui mintui-field-error"
                });
            } finally {
                this.imgloading = false;
            }
        },
        async upqiniu(file) {
            try {
                const res = await api.getUploadToken();
                if (res.status !== 0) {
                    Toast("获取上传Token失败");
                    return null;
                }

                const uploadToken = res.data;
                if (!file) {
                    Toast("请完成签名后再保存");
                    return null;
                }

                const options = {
                    quality: 0.85,
                    noCompressIfLarger: true,
                    maxWidth: 800,
                    maxHeight: 400
                };

                const data = await qiniu.compressImage(file, options);
                const putExtra = {};
                const config = {
                    region: qiniu.region.z2,
                };

                const timestamp = Date.now();
                const fileExtension = file.name.split(".").pop();
                const fileName = `signature_${timestamp}.${fileExtension}`;

                const observable = qiniu.upload(
                    data.dist,
                    fileName,
                    uploadToken,
                    putExtra,
                    config
                );

                const domain = this.qiniuDomain;

                return new Promise((resolve, reject) => {
                    observable.subscribe({
                        next(res) {
                            // 可以在这里添加上传进度显示
                        },
                        error(err) {
                            console.error('Upload error:', err);
                            Toast("文件上传失败");
                            reject(err);
                        },
                        complete(res) {
                            const fileUrl = `${domain}/${res.key}`;
                            resolve(fileUrl);
                        },
                    });
                });
            } catch (error) {
                console.error('Upload process error:', error);
                Toast("上传过程出现错误");
                return null;
            }
        },
        dataURLtoFile(dataurl, filename) {
            const arr = dataurl.split(",");
            const mime = arr[0].match(/:(.*?);/)[1];
            const bstr = atob(arr[1]);
            let n = bstr.length;
            const u8arr = new Uint8Array(n);
            while (n--) {
                u8arr[n] = bstr.charCodeAt(n);
            }
            return new File([u8arr], filename, { type: mime });
        },
    },
};
</script>

<style scoped lang="less">
.signature-container {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    margin: 16px 0;
    position: relative;
    overflow: hidden;
}

.signature-header {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e2e8f0;
    position: relative;

    .header-icon {
        width: 48px;
        height: 48px;
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);

        svg {
            width: 24px;
            height: 24px;
            color: white;
        }
    }

    .header-text {
        flex: 1;

        h3 {
            margin: 0 0 4px 0;
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
            letter-spacing: -0.025em;
        }

        p {
            margin: 0;
            color: #64748b;
            font-size: 14px;
            line-height: 1.5;
        }
    }

    .signature-status {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f1f5f9;
        color: #64748b;
        transition: all 0.3s ease;

        &.completed {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        svg {
            width: 20px;
            height: 20px;
        }
    }
}

.signature-pad-wrapper {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
    background: white;
    border: 2px solid #e2e8f0;
}

.signature-display {
    position: relative;
    height: 300px;
    background: linear-gradient(135deg, #fefefe 0%, #f8fafc 100%);

    .signature-image {
        width: 100%;
        height: 100%;
        object-fit: contain;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
    }

    .signature-overlay {
        position: absolute;
        top: 16px;
        right: 16px;

        .signature-badge {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);

            svg {
                width: 16px;
                height: 16px;
            }
        }
    }
}

.signature-pad-container {
    position: relative;
    height: 300px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);

    .signature-canvas {
        display: block;
        width: 100%;
        height: 100%;
        cursor: crosshair;
        transition: all 0.3s ease;

        &:hover {
            background: #fefefe;
        }
    }

    .canvas-guide {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        pointer-events: none;
        opacity: 0.3;
        transition: opacity 0.3s ease;

        .guide-line {
            width: 200px;
            height: 2px;
            background: linear-gradient(90deg, transparent, #94a3b8, transparent);
            margin-bottom: 8px;
        }

        .guide-text {
            text-align: center;
            color: #64748b;
            font-size: 14px;
            font-weight: 500;
        }
    }

    .drawing-indicator {
        position: absolute;
        top: 16px;
        left: 16px;
        display: flex;
        align-items: center;
        gap: 8px;
        background: rgba(59, 130, 246, 0.9);
        color: white;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
        backdrop-filter: blur(4px);

        .indicator-dot {
            width: 8px;
            height: 8px;
            background: white;
            border-radius: 50%;
            animation: pulse 1.5s infinite;
        }
    }
}

.signature-actions {
    display: flex;
    gap: 16px;
    margin-top: 24px;

    .action-button {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        padding: 14px 20px;
        border: none;
        border-radius: 12px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        svg {
            width: 20px;
            height: 20px;
        }

        &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }

        &:not(:disabled):hover {
            transform: translateY(-2px);
        }

        &:not(:disabled):active {
            transform: translateY(0);
        }
    }

    .clear-button {
        background: linear-gradient(135deg, #ef4444, #dc2626);
        color: white;
        box-shadow: 0 4px 16px rgba(239, 68, 68, 0.3);

        &:not(:disabled):hover {
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
        }
    }

    .save-button {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);

        &:not(:disabled):hover {
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
        }

        .loading-spinner {
            svg {
                animation: spin 1s linear infinite;
            }
        }
    }
}

.signature-tips {
    margin-top: 20px;
    padding: 16px;
    background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
    border-radius: 12px;
    border: 1px solid #7dd3fc;

    .tip-item {
        display: flex;
        align-items: center;
        gap: 10px;
        color: #0369a1;
        font-size: 13px;
        line-height: 1.5;
        margin-bottom: 8px;

        &:last-child {
            margin-bottom: 0;
        }

        svg {
            width: 16px;
            height: 16px;
            flex-shrink: 0;
        }
    }
}

@keyframes pulse {

    0%,
    100% {
        opacity: 1;
        transform: scale(1);
    }

    50% {
        opacity: 0.7;
        transform: scale(1.1);
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

@keyframes clearFlash {
    0% {
        background: rgba(239, 68, 68, 0.1);
    }

    100% {
        background: transparent;
    }
}

// 移动端适配
@media (max-width: 768px) {
    .signature-container {
        padding: 16px;
        margin: 12px 0;
        border-radius: 12px;
    }

    .signature-header {
        margin-bottom: 16px;
        padding-bottom: 16px;

        .header-icon {
            width: 40px;
            height: 40px;
            margin-right: 12px;

            svg {
                width: 20px;
                height: 20px;
            }
        }

        .header-text h3 {
            font-size: 18px;
        }

        .signature-status {
            width: 36px;
            height: 36px;

            svg {
                width: 18px;
                height: 18px;
            }
        }
    }

    .signature-pad-container {
        height: 250px;
    }

    .signature-actions {
        gap: 12px;
        margin-top: 16px;

        .action-button {
            padding: 12px 16px;
            font-size: 14px;

            svg {
                width: 18px;
                height: 18px;
            }
        }
    }

    .signature-tips {
        margin-top: 16px;
        padding: 12px;

        .tip-item {
            font-size: 12px;
        }
    }
}

// 暗色模式支持
@media (prefers-color-scheme: dark) {
    .signature-container {
        background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
        border-color: #475569;
        color: #f1f5f9;
    }

    .signature-header {
        border-bottom-color: #475569;

        .header-text h3 {
            color: #f1f5f9;
        }

        .header-text p {
            color: #94a3b8;
        }
    }

    .signature-pad-wrapper {
        border-color: #475569;
        background: #334155;
    }

    .signature-pad-container {
        background: linear-gradient(135deg, #334155 0%, #475569 100%);
    }

    .signature-tips {
        background: linear-gradient(135deg, #1e3a8a, #1e40af);
        border-color: #3b82f6;
    }
}
</style>