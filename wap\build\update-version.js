const fs = require('fs');
const path = require('path');

// 生成版本信息
const buildVersion = {
    version: Date.now().toString(),
    buildId: Math.random().toString(36).substring(7),
    timestamp: new Date().toISOString(),
    commit: process.env.CI_COMMIT_SHA || 'local-build'
};

// 写入版本文件
const versionPath = path.join(__dirname, '../src/utils/version.js');
const versionContent = `// 自动生成的版本文件 - 请勿手动修改
export const buildInfo = ${JSON.stringify(buildVersion, null, 2)};

// 用于在控制台输出构建信息
console.log('构建版本:', buildInfo.version);
console.log('构建ID:', buildInfo.buildId);
console.log('构建时间:', buildInfo.timestamp);
`;

fs.writeFileSync(versionPath, versionContent, 'utf8');
console.log('✅ 版本文件已更新:', buildVersion);

// 更新package.json中的版本号
const packagePath = path.join(__dirname, '../package.json');
const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));

// 生成新的版本号 (基于时间戳的微版本)
const now = new Date();
const year = now.getFullYear();
const month = String(now.getMonth() + 1).padStart(2, '0');
const day = String(now.getDate()).padStart(2, '0');
const hour = String(now.getHours()).padStart(2, '0');
const minute = String(now.getMinutes()).padStart(2, '0');

packageJson.version = `1.${year}${month}${day}.${hour}${minute}`;

fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2), 'utf8');
console.log('✅ package.json版本已更新为:', packageJson.version);

module.exports = buildVersion; 