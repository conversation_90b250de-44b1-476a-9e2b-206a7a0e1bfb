<template>
    <svg :width="size" :height="size" :style="{ fill: color }" t="1743077301130" class="icon" viewBox="0 0 1025 1024"
        version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="8096">
        <path
            d="M512.653762 1023.998047A511.791377 511.791377 0 0 1 313.345574 40.47724a512.105864 512.105864 0 0 1 398.616376 943.45886 508.786731 508.786731 0 0 1-199.308188 40.061947z m0-943.45886C274.285175 80.539187 80.986279 273.838083 80.986279 512.20667s193.298896 431.667483 431.667483 431.667482 431.667483-193.298896 431.667482-431.667482S751.022348 80.539187 512.653762 80.539187z"
            fill="" p-id="8097">
        </path>
        <path
            d="M427.522124 715.521052a40.061947 40.061947 0 0 1-28.043364-12.018584L207.181413 511.205121a40.36742 40.36742 0 0 1 57.088275-57.088275l163.252436 164.253984 339.525003-339.525004a40.36742 40.36742 0 0 1 57.088275 57.088275L455.565487 703.502468a40.061947 40.061947 0 0 1-28.043363 12.018584z"
            fill="" p-id="8098">
        </path>
    </svg>
</template>

<script>
export default {
    name: 'IconCheck',
    props: {
        size: {
            type: [String, Number],
            default: 64
        },
        color: {
            type: String,
            default: 'rgba(14, 201, 177, 1)'
        }
    }
}
</script>